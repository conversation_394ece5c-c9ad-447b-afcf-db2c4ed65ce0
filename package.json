{"name": "job-service-nest", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/src/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node ./node_modules/typeorm/cli", "migration:generate": "npm run typeorm -- migration:generate -d typeorm.config.ts", "migration:create": "npm run typeorm -- migration:create", "migration:run": "npm run typeorm -- migration:run -d typeorm.config.ts", "migration:revert": "npm run typeorm -- migration:revert -d typeorm.config.ts", "seed": "ts-node src/seeds/seed.ts", "swagger:generate": "ts-node scripts/generate-swagger.ts", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org neduai --project job-service ./dist && sentry-cli sourcemaps upload --org neduai --project job-service ./dist"}, "dependencies": {"@anatine/zod-nestjs": "^2.0.12", "@aws-sdk/client-s3": "^3.775.0", "@aws-sdk/lib-storage": "^3.775.0", "@google-cloud/logging": "^11.2.0", "@google-cloud/pubsub": "^4.10.0", "@google-cloud/vertexai": "^1.9.3", "@google/genai": "^0.6.1", "@keyv/redis": "^4.2.0", "@modelcontextprotocol/sdk": "^1.12.1", "@nestjs/cache-manager": "^3.0.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.0", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^3.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.0.4", "@nestjs/typeorm": "^11.0.0", "@pinecone-database/pinecone": "^5.1.1", "@sentry/cli": "^2.43.0", "@sentry/nestjs": "^9.11.0", "@sentry/profiling-node": "^9.11.0", "@types/cache-manager": "^5.0.0", "axios": "^1.8.3", "cache-manager": "^6.4.0", "cacheable": "^1.8.8", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "csv-parse": "^5.6.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "deepl-node": "^1.17.3", "dify-client": "^2.3.2", "keyv": "^5.2.3", "mongodb": "^5.9.2", "node-fetch": "^3.3.2", "openai": "^4.89.0", "pg": "^8.13.3", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socks-proxy-agent": "^8.0.5", "supertokens-node": "^21.1.0", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.20", "uuid": "^11.1.0", "zod": "^3.24.4"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/lodash": "^4.17.15", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^15.14.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}