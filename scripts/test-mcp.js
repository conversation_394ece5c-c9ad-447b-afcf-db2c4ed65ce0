#!/usr/bin/env node

/**
 * Test script for MCP endpoint
 * Usage: MCP_TOKEN=your-token node scripts/test-mcp.js
 */

const axios = require('axios');

const MCP_TOKEN = process.env.MCP_TOKEN || 'test-token';
const BASE_URL = process.env.BASE_URL || 'http://localhost:3000';

async function testMCPEndpoint() {
  console.log('Testing MCP endpoint...\n');

  const tests = [
    {
      name: 'Initialize',
      request: {
        method: 'initialize',
        params: {
          protocolVersion: '2024-11-05',
          capabilities: {},
          clientInfo: {
            name: 'test-client',
            version: '1.0.0',
          },
        },
        id: 1,
      },
    },
    {
      name: 'List Tools',
      request: {
        method: 'tools/list',
        id: 2,
      },
    },
    {
      name: 'Health Check',
      request: {
        method: 'tools/call',
        params: {
          name: 'health_check',
          arguments: {
            context: {
              userId: 'test-user',
              requestId: `test-${Date.now()}`,
              source: 'dify_agent',
              reason: 'Testing MCP endpoint',
            },
            includeDetails: true,
          },
        },
        id: 3,
      },
    },
    {
      name: 'Invalid Method',
      request: {
        method: 'invalid/method',
        id: 4,
      },
      expectError: true,
    },
  ];

  for (const test of tests) {
    console.log(`\n--- Test: ${test.name} ---`);
    console.log('Request:', JSON.stringify(test.request, null, 2));

    try {
      const response = await axios.post(`${BASE_URL}/mcp`, test.request, {
        headers: {
          'Content-Type': 'application/json',
          'x-service-token': MCP_TOKEN,
          'x-correlation-id': `test-${Date.now()}`,
        },
      });

      if (test.expectError) {
        console.log('❌ Expected error but got success');
      } else {
        console.log('✅ Success');
        console.log('Response:', JSON.stringify(response.data, null, 2));
      }
    } catch (error) {
      if (test.expectError) {
        console.log('✅ Got expected error');
        console.log('Error:', error.response?.data || error.message);
      } else {
        console.log('❌ Unexpected error');
        console.log('Error:', error.response?.data || error.message);
      }
    }
  }

  console.log('\n--- Batch Request Test ---');
  const batchRequest = [
    {
      method: 'tools/list',
      id: 'batch-1',
    },
    {
      method: 'tools/call',
      params: {
        name: 'health_check',
        arguments: {
          context: {
            userId: 'batch-user',
            requestId: `batch-${Date.now()}`,
            source: 'dify_agent',
            reason: 'Batch test',
          },
        },
      },
      id: 'batch-2',
    },
  ];

  try {
    const response = await axios.post(`${BASE_URL}/mcp`, batchRequest, {
      headers: {
        'Content-Type': 'application/json',
        'x-service-token': MCP_TOKEN,
        'x-correlation-id': `batch-test-${Date.now()}`,
      },
    });

    console.log('✅ Batch request success');
    console.log('Response:', JSON.stringify(response.data, null, 2));
  } catch (error) {
    console.log('❌ Batch request failed');
    console.log('Error:', error.response?.data || error.message);
  }
}

// Run tests
testMCPEndpoint().catch(console.error);