import { NestFactory } from '@nestjs/core';
import { AppModule } from '../src/app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as fs from 'fs';
import * as path from 'path';

async function generateSwaggerJson() {
  const app = await NestFactory.create(AppModule);

  const config = new DocumentBuilder()
    .setTitle('NEDU Job Service API')
    .setDescription(
      `
      API documentation for the NEDU Job Service.
      
      ## Features
      - Education and Industry recommendations
      - Program alignment scoring
      - Industry growth projections
      - Salary information
    `,
    )
    .setVersion('1.0')
    .addTag(
      'Education Industries',
      'Industry recommendations and insights for educational programs',
    )
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, config);

  const outputPath = path.resolve(
    process.cwd(),
    'job-service-swagger-spec.json',
  );
  fs.writeFileSync(outputPath, JSON.stringify(document, null, 2), {
    encoding: 'utf8',
  });

  console.log(`Swagger JSON file written to: ${outputPath}`);
  await app.close();
}

generateSwaggerJson();
