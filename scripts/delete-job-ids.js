const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const { stringify } = require('csv-stringify/sync');

// Path to the CSV file
const csvDirPath = '/Users/<USER>/WebstormProjects/nedu/job-service-nest/exports/';
const csvFiles = fs.readdirSync(csvDirPath).filter(file => file.startsWith('job_batch_') && file.endsWith('.csv'));
const csvFilePath = path.join(csvDirPath, csvFiles.sort().pop());

// IDs to delete (from the image)
const idsToDelete = [
  'c9eac74e-6977-4f1c-adb4-e2a21c53bbba',
  '27f6e1ba-884b-44a4-bfa6-34ee60eb72c8',
  '5f1ed78f-c186-4d3d-9d37-ffb566c2<PERSON><PERSON>',
  'd8ddbda1-d180-43d0-b286-01fa661ce119',
  '8efcd55c-6f67-4785-8602-16d065141bd1',
  'aa0f7760-81d1-4ab6-bf5a-27923201856b',
  '369a1b30-6f67-468c-a506-63d11cbd63fc',
  'febe46d4-8d1b-45c9-a25d-36f54bded5a2',
  'ec1cc759-7e5a-4eb3-88ad-bf0659372798',
  '12823738-d491-4604-880b-36149d70acd3',
  '8ed6eb46-3822-4d9d-be59-bdde1df3311f'
];

// Read the CSV file
async function processCSV() {
  const rows = [];
  const headers = [];
  let headerRead = false;

  // Create a backup of the original file
  const backupPath = `${csvFilePath}.backup`;
  fs.copyFileSync(csvFilePath, backupPath);
  console.log(`Created backup at ${backupPath}`);

  // Read and process the CSV
  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('headers', (headerList) => {
        headerRead = true;
        headers.push(...headerList);
      })
      .on('data', (row) => {
        // Only keep rows with IDs not in our deletion list
        if (!idsToDelete.includes(row.ext_id)) {
          rows.push(row);
        }
      })
      .on('end', () => {
        resolve({ rows, headers });
      })
      .on('error', (err) => {
        reject(err);
      });
  }).then(({ rows, headers }) => {
    // Write filtered data back to CSV
    const output = stringify(rows, { header: true, columns: headers });
    fs.writeFileSync(csvFilePath, output);
    
    const deletedCount = idsToDelete.length - (rows.length - (fs.readFileSync(backupPath, 'utf-8').split('\n').length - 2));
    console.log(`Removed ${deletedCount} rows from ${csvFilePath}`);
    console.log(`Original file backed up at ${backupPath}`);
  });
}

// Run the process
processCSV().catch(err => {
  console.error('Error processing CSV:', err);
});
