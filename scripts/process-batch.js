#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const { parse } = require('csv-parse/sync');
const { stringify } = require('csv-stringify/sync');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });
const { WorkflowClient } = require('./dify-client');

/**
 * Batch Processing Script for Job Service
 * --------------------------------------
 * This script takes job entries from a CSV file, processes them through a workflow service,
 * and posts the results to the job service callback endpoint.
 *
 * Usage:
 *   node process-batch.js [csv-file-path] [api-key]
 *
 * Arguments:
 *   csv-file-path - Path to the CSV file (default: ../exports/job_batch_*.csv)
 *   api-key       - Workflow service API key (default: uses WORKFLOW_JOB_PROCESSOR_API_KEY from .env)
 *
 * Environment Variables:
 *   WORKFLOW_API_URL             - The workflow service URL
 *   WORKFLOW_JOB_PROCESSOR_API_KEY - API key for job processing workflow (can be overridden via command line)
 */

// Debug mode for verbose logging
const DEBUG = true;

// Simple console logger
const logger = {
  info: (message) => console.log(`[${new Date().toISOString()}] [INFO] ${message}`),
  error: (message) => console.error(`[${new Date().toISOString()}] [ERROR] ${message}`),
  debug: (message) => DEBUG && console.log(`[${new Date().toISOString()}] [DEBUG] ${message}`)
};

// Helper function to format milliseconds to HH:mm:ss
function formatMillisecondsToHHMMSS(ms) {
  const totalSeconds = Math.floor(ms / 1000);
  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  const pad = (num) => String(num).padStart(2, '0');

  return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
}

// Find the latest batch file in the exports directory
function findLatestBatchFile() {
  const exportDir = path.join(__dirname, '../exports');
  if (!fs.existsSync(exportDir)) {
    logger.error(`Export directory not found: ${exportDir}`);
    process.exit(1);
  }

  const batchFiles = fs.readdirSync(exportDir)
    .filter(file => file.startsWith('job_batch_') && file.endsWith('.csv'))
    .map(file => ({ 
      name: file,
      path: path.join(exportDir, file),
      time: fs.statSync(path.join(exportDir, file)).mtime.getTime()
    }))
    .sort((a, b) => b.time - a.time); // Sort by most recent first

  if (batchFiles.length === 0) {
    logger.error('No batch files found in exports directory');
    process.exit(1);
  }

  logger.info(`Using most recent batch file: ${batchFiles[0].name}`);
  return batchFiles[0].path;
}

// Configuration object
const CONFIG = {
  // Local job service callback endpoint (using localhost as requested)
  callbackUrl: 'https://job-svc-develop-521027808991.europe-north1.run.app/jobs/callback',
  
  // CSV file path (use wildcard to find latest file if not specified)
  csvFilePath: process.argv[2] || findLatestBatchFile(),
  
  // Whether to delete entries from CSV after processing
  removeProcessedEntries: true,
  
  // Concurrency limit (how many jobs to process in parallel)
  concurrencyLimit: 10,
  
  // Delay between workflow requests (in milliseconds)
  requestDelay: 3000,
  
  // Debug mode flag
  debug: DEBUG,
  
  // Timeouts (in milliseconds)
  timeouts: {
    workflow: 310000, // 200 seconds for workflow processing
    callback: 10000   // 10 seconds for callback requests
  },
  
  // Retry configuration
  retries: {
    workflow: {
      maxRetries: 0,     // Retry workflow calls once
      retryDelay: 2000  // 2 second delay between retries
    }
  },
  
  // Dify workflow configuration
  dify: {
    // Command line argument overrides environment variable
    apiKey: process.argv[3] || process.env.WORKFLOW_JOB_PROCESSOR_API_KEY,
    
    // API URL from environment
    apiUrl: process.env.WORKFLOW_API_URL,
  }
};

// Initialize Dify client - this will hold the workflow client
let workflowClient;

// Initialize the Dify client with configuration
async function initDifyClient() {
  if (!CONFIG.dify.apiKey) {
    throw new Error('Workflow API key not set. Specify via command line argument or set WORKFLOW_JOB_PROCESSOR_API_KEY in .env');
  }
  
  if (!CONFIG.dify.apiUrl) {
    throw new Error('WORKFLOW_API_URL environment variable not set in .env');
  }
  
  try {
    logger.info(`Initializing Dify client with API URL: ${CONFIG.dify.apiUrl}`);
    
    // Create new workflow client with custom options
    workflowClient = new WorkflowClient(
      CONFIG.dify.apiKey, 
      CONFIG.dify.apiUrl,
      {
        timeout: CONFIG.timeouts.workflow,
        maxRetries: CONFIG.retries.workflow.maxRetries,
        retryDelay: CONFIG.retries.workflow.retryDelay
      }
    );
    
    return workflowClient;
  } catch (error) {
    logger.error(`Failed to initialize Dify client: ${error.message}`);
    throw error;
  }
}

// Process a single job entry using Dify client
async function processJob(job) {
  const jobStartTime = process.hrtime(); // Record job start time
  try {
    logger.info(`Processing job: ${job.ext_id} (Employer: ${job.employer || 'Unknown Employer'})`);
    
    // Prepare query for Dify workflow
    const user = `job-processor-${job.ext_id}`;
    const query = {
      employer: job.employer || 'Unknown Employer',
      job_description: job.job_description,
      ext_id: job.ext_id
    };
    
    // Call processor service via Dify with timeout
    logger.debug(`Calling workflow service for job: ${job.ext_id}`);
    const response = await workflowClient.runWorkflow(query, user, {
      timeout: CONFIG.timeouts.workflow
    });

    const jobEndTime = process.hrtime(jobStartTime); // Record job end time
    const jobProcessingTimeMs = (jobEndTime[0] * 1000 + jobEndTime[1] / 1e6);
    logger.info(`Job ${job.ext_id} processed in ${formatMillisecondsToHHMMSS(jobProcessingTimeMs)}`);

    if (!response || !response.data) {
      logger.error(`Empty or invalid response from processor for job: ${job.ext_id}`);
      return { success: false, reason: 'Empty or invalid response', output: null };
    }
    
    // Parse the response output into a proper JavaScript object
    let result;
    try {
      // Get the output data
      const output = response.data.data.outputs;
      
      // Check if it's already a JavaScript object
      if (typeof output.data === 'object' && output.data !== null) {
        // The output is already a JavaScript object, use it directly
        result = replaceNoneWithNull(output.data);
      } else if (typeof output.data === 'string') {
        // The response comes as a string, attempt to parse it
        const outputStr = output.data;
        
        try {
          // First try to parse as JSON
          result = replaceNoneWithNull(JSON.parse(outputStr));
        } catch (jsonError) {
          // If JSON parsing fails, try to evaluate as a Python dictionary
          // This handles Python-style dictionaries with single quotes
          const parsedResult = eval(`(${outputStr})`);
          result = replaceNoneWithNull(parsedResult);
        }
      } else {
        throw new Error(`Unexpected output format: ${typeof output.data}`);
      }
      
      logger.debug(`Successfully parsed result for job: ${job.ext_id}`);
      // logger.debug(`Result: ${JSON.stringify(result)}`);
    } catch (err) {
      logger.error(`Failed to parse processor response for job ${job.ext_id}: ${err.message}`);
      logger.debug(`Raw output: ${JSON.stringify(response.data.data.outputs)}`);
      return { 
        success: false, 
        reason: `Failed to parse: ${err.message}`, 
        output: JSON.stringify(response.data.data.outputs) 
      };
    }
    
    // Prepare the payload for the callback in the expected format
    const callbackPayload = {
      ext_id: job.ext_id,
      result: result
    };
    
    logger.debug(`Prepared callback payload for job ${job.ext_id}`);
    
    // Call callback endpoint with result
    logger.debug(`Calling callback endpoint for job: ${job.ext_id}`);
    const callbackResponse = await axios.post(
      CONFIG.callbackUrl,
      callbackPayload,
      {
        timeout: CONFIG.timeouts.callback
      }
    );
    
    logger.info(`Successfully processed job ${job.ext_id}. Callback status: ${callbackResponse.status}`);
    return { success: true };
  } catch (error) {
    logger.error(`Error processing job ${job.ext_id}: ${error.message}`);
    // Ensure job timing is recorded even on error if not already
    const jobEndTimeOnError = process.hrtime(jobStartTime);
    const jobProcessingTimeMsOnError = (jobEndTimeOnError[0] * 1000 + jobEndTimeOnError[1] / 1e6);
    logger.info(`Job ${job.ext_id} (failed) took ${formatMillisecondsToHHMMSS(jobProcessingTimeMsOnError)} before error`);
    return { success: false, reason: error.message, output: null };
  }
}

// Recursive function to replace None with null in objects and arrays
function replaceNoneWithNull(obj) {
  if (obj === undefined || obj === null) {
    return null;
  }
  
  if (obj === 'None') {
    return null;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(item => replaceNoneWithNull(item));
  }
  
  if (typeof obj === 'object') {
    const newObj = {};
    for (const [key, value] of Object.entries(obj)) {
      newObj[key] = replaceNoneWithNull(value);
    }
    return newObj;
  }
  
  return obj;
}

// Write failed jobs to CSV
function writeFailedJobsToCSV(failedJobsDetails) {
  if (!failedJobsDetails || failedJobsDetails.length === 0) {
    logger.info('No failed jobs to write to CSV');
    return;
  }

  try {
    // Create failed.csv file path in same directory as the input file
    const dirName = path.dirname(CONFIG.csvFilePath);
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const failedFilePath = path.join(dirName, `failed_${timestamp}.csv`);

    // Prepare data for CSV
    const csvData = failedJobsDetails.map(job => ({
      ext_id: job.ext_id,
      employer: job.employer || 'Unknown Employer',
      reason: job.reason || 'Unknown error',
      raw_output: job.output || ''
    }));

    // Write to CSV
    const csvContent = stringify(csvData, { 
      header: true
    });
    fs.writeFileSync(failedFilePath, csvContent);
    
    logger.info(`Wrote ${failedJobsDetails.length} failed jobs to ${failedFilePath}`);
    return failedFilePath;
  } catch (error) {
    logger.error(`Error writing failed jobs to CSV: ${error.message}`);
    return null;
  }
}

// Process a batch of jobs from CSV file
async function processBatch() {
  try {
    // Initialize Dify client (now async)
    await initDifyClient();
    
    // Check if file exists
    if (!fs.existsSync(CONFIG.csvFilePath)) {
      logger.error(`CSV file not found: ${CONFIG.csvFilePath}`);
      process.exit(1);
    }
    
    // Read and parse CSV
    const csvContent = fs.readFileSync(CONFIG.csvFilePath, 'utf8');
    const jobs = parse(csvContent, {
      columns: true,
      skip_empty_lines: true,
      trim: true
    });
    
    logger.info(`Loaded ${jobs.length} jobs from CSV file`);
    
    if (jobs.length === 0) {
      logger.info('No jobs to process, exiting');
      process.exit(0);
    }
    
    // Sleep function for adding delays
    const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));
    
    // Process jobs with concurrency limit
    const results = [];
    const processedJobs = [];
    const failedJobs = [];
    const failedJobsDetails = [];
    
    // Process jobs in chunks based on concurrency limit
    for (let i = 0; i < jobs.length; i += CONFIG.concurrencyLimit) {
      const chunk = jobs.slice(i, i + CONFIG.concurrencyLimit);
      logger.info(`Processing batch ${Math.floor(i / CONFIG.concurrencyLimit) + 1}/${Math.ceil(jobs.length / CONFIG.concurrencyLimit)} (${chunk.length} jobs)`);
      
      const chunkPromises = chunk.map(async (job, index) => {
        // Add delay between jobs in the same chunk (except for the first one)
        if (index > 0) {
          logger.debug(`Waiting ${CONFIG.requestDelay}ms before processing next job...`);
          await sleep(CONFIG.requestDelay);
        }
        
        const result = await processJob(job);
        if (result.success) {
          processedJobs.push(job);
        } else {
          failedJobs.push(job);
          failedJobsDetails.push({
            ...job,
            reason: result.reason,
            output: result.output
          });
        }
        return { job, success: result.success };
      });
      
      const chunkResults = await Promise.all(chunkPromises);
      results.push(...chunkResults);
      
      // Add delay between chunks
      if (i + CONFIG.concurrencyLimit < jobs.length) {
        logger.info(`Waiting ${CONFIG.requestDelay}ms before processing next batch...`);
        await sleep(CONFIG.requestDelay);
      }
    }
    
    logger.info(`Completed processing: ${processedJobs.length} succeeded, ${failedJobs.length} failed`);
    
    // Write failed jobs to CSV
    if (failedJobsDetails.length > 0) {
      const failedFilePath = writeFailedJobsToCSV(failedJobsDetails);
      if (failedFilePath) {
        logger.info(`Failed jobs written to: ${failedFilePath}`);
      }
    }
    
    // Update CSV file by removing processed entries
    if (CONFIG.removeProcessedEntries && processedJobs.length > 0) {
      const remainingJobs = jobs.filter(job => !processedJobs.some(pJob => pJob.ext_id === job.ext_id));
      
      if (remainingJobs.length > 0) {
        // Write remaining jobs back to CSV
        const updatedCsvContent = stringify(remainingJobs, { 
          header: true,
          columns: Object.keys(jobs[0])
        });
        fs.writeFileSync(CONFIG.csvFilePath, updatedCsvContent);
        logger.info(`Updated CSV file with ${remainingJobs.length} remaining jobs`);
      } else {
        // All jobs processed, create empty file with header
        const header = 'employer,job_description,ext_id\n';
        fs.writeFileSync(CONFIG.csvFilePath, header);
        logger.info('All jobs processed, CSV file now contains only header');
      }
    }
    
    return {
      total: jobs.length,
      processed: processedJobs.length,
      failed: failedJobs.length,
      failedJobsDetailsCount: failedJobsDetails.length
    };
  } catch (error) {
    logger.error(`Error in batch processing: ${error.message}`);
    if (error.stack) {
      logger.error(error.stack);
    }
    process.exit(1);
  }
}

// Main execution
(async () => {
  const scriptStartTime = process.hrtime(); // Record script start time
  logger.info('Starting batch processor');
  logger.info(`Using CSV file: ${CONFIG.csvFilePath}`);
  
  try {
    const result = await processBatch();
    
    logger.info(`Batch processing complete: ${result.processed}/${result.total} jobs processed successfully`);
    
    if (result.failed > 0) {
      logger.info(`Failed jobs remaining in CSV: ${result.failed}`);
      process.exit(result.failed > 0 ? 1 : 0);
    }
  } catch (error) {
    logger.error(`Batch processing failed: ${error.message}`);
    process.exitCode = 1; // Indicate failure
  } finally {
    const scriptEndTime = process.hrtime(scriptStartTime); // Record script end time
    const scriptTotalTimeMs = (scriptEndTime[0] * 1000 + scriptEndTime[1] / 1e6);
    logger.info(`Batch processor finished in ${formatMillisecondsToHHMMSS(scriptTotalTimeMs)}.`);
    if (process.exitCode === 1) {
      logger.error('Script finished with errors.');
    } else {
      logger.info('Script finished successfully.');
    }
  }
})();
