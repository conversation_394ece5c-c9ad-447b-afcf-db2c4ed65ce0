/**
 * Simplified Dify client implementation
 * Created specifically for the job-service-nest batch processor
 */
const axios = require('axios');

// Default base URL for Dify API
const DEFAULT_BASE_URL = 'https://api.dify.ai/v1';

/**
 * Simple Dify API client focused on workflow functionality
 */
class DifyClient {
  /**
   * Create a new Dify client instance
   * 
   * @param {string} apiKey - The API key for authentication
   * @param {string} baseUrl - Base URL for the Dify API (optional)
   * @param {object} options - Additional configuration options
   */
  constructor(apiKey, baseUrl = DEFAULT_BASE_URL, options = {}) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
    this.options = {
      timeout: 30000, // Default 30 second timeout
      maxRetries: 0,  // Default no retries
      retryDelay: 1000, // Default 1 second between retries
      ...options
    };
  }

  /**
   * Send a request to the Dify API
   * 
   * @param {string} method - HTTP method (GET, POST, etc.)
   * @param {string} endpoint - API endpoint
   * @param {object} data - Request data (for POST, PUT, etc.)
   * @param {object} params - Query parameters
   * @param {boolean} stream - Whether to use streaming response
   * @param {object} requestOptions - Additional request options
   * @returns {Promise} Axios response promise
   */
  async sendRequest(method, endpoint, data = null, params = null, stream = false, requestOptions = {}) {
    const headers = {
      Authorization: `Bearer ${this.apiKey}`,
      'Content-Type': 'application/json',
      ...(requestOptions.headers || {})
    };

    const url = `${this.baseUrl}${endpoint}`;
    const options = {
      method,
      url,
      timeout: requestOptions.timeout || this.options.timeout,
      headers,
      ...(method !== 'GET' && data && { data }),
      ...(params && { params }),
      ...(stream && { responseType: 'stream' })
    };

    let lastError = null;
    const maxRetries = requestOptions.maxRetries !== undefined 
      ? requestOptions.maxRetries 
      : this.options.maxRetries;
    
    // Attempt request with retries if configured
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await axios(options);
      } catch (error) {
        lastError = error;
        
        // If this was the last attempt, don't retry
        if (attempt === maxRetries) {
          break;
        }
        
        // Only retry for network errors or 5xx responses
        const shouldRetry = !error.response || (error.response && error.response.status >= 500);
        if (!shouldRetry) {
          break;
        }
        
        // Wait before retry
        const delay = requestOptions.retryDelay || this.options.retryDelay;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    
    // If we got here, all attempts failed
    throw lastError;
  }
}

/**
 * Workflow client for interacting with Dify workflows
 */
class WorkflowClient extends DifyClient {
  /**
   * Run a workflow with the provided inputs
   * 
   * @param {object} inputs - Input data for the workflow
   * @param {string} user - User identifier
   * @param {object} options - Additional options including timeout
   * @returns {Promise} Workflow execution result
   */
  async runWorkflow(inputs, user, options = {}) {
    const data = {
      inputs,
      user,
      response_mode: options.stream ? 'streaming' : 'blocking'
    };

    return this.sendRequest(
      'POST',
      '/workflows/run',
      data,
      null, 
      options.stream || false,
      {
        timeout: options.timeout,
        maxRetries: options.maxRetries,
        retryDelay: options.retryDelay
      }
    );
  }

  /**
   * Stop a running workflow
   * 
   * @param {string} taskId - ID of the task to stop
   * @param {string} user - User identifier
   * @returns {Promise} Stop operation result
   */
  async stopWorkflow(taskId, user) {
    const data = { user };
    return this.sendRequest(
      'POST',
      `/workflows/${taskId}/stop`,
      data
    );
  }
}

module.exports = {
  DifyClient,
  WorkflowClient
};
