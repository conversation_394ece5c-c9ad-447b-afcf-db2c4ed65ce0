**AI Backend Developer System Instructions**  
**Job Service MVP (v1+)**

---

### **Core Principles**
1. **Modularity**: Services must remain standalone; external dependencies via APIs/queues only.
2. **Scalability**: Assume all components will handle 10x current load.
3. **Clarity**: Prioritize readable code over clever optimizations.
4. **Reliability**: All background jobs (Google Cloud PubSub) must be idempotent.

---

### **Coding Standards**
- **TypeScript**: Strict mode enabled; no `any` types.
- **Linting**: ESLint + Prettier enforced.
- **Git**: Atomic commits; prefix with `[JOBS]` (e.g., `[JOBS] Add translation cache`).

---

### **Architectural Guidelines**
1. **Layered Design**:
    - Routes → Services → Data Access (Prisma).
2. **Async Processing**:
    - Use Google Cloud PubSub for all background tasks (imports/translations).
3. **Errors**:
    - Log to Sentry; fail fast in critical paths (e.g., job classification).
4. **Caching**:
    - Redis for translations/recommendations (24h TTL).

---

### **API Design**
- **Stateless**: No session storage; authenticate via JWT (future-proof).
- **Versioning**: Prefix routes with `/v1/`.
- **Validation**: All inputs/outputs use Zod schemas.

---

### **Data Management**
- **Migrations**: Prisma for schema changes; backward-compatible always.

---

### **Security & Compliance**
- **Inputs**: Sanitize all free-text fields (e.g., job descriptions).
- **Dependencies**: Audit weekly with `npm audit`; no unmaintained packages.
- **GDPR**: Pseudonymize user identifiers in logs.

---

### **Collaboration**
- **Docs**: Swagger for APIs; ADRs for major decisions.
- **Reviews**: All PRs require peer review + passing CI (tests/linting).
---

### **Google Cloud**
- **Region**: europe-north1
- **Project**: skilful-ethos-405819
- **Service**: job-svc-develop
- **Deploy Command**:
  ```bash
  gcloud run services update job-svc-develop \
    --region=europe-north1 \
    --project=skilful-ethos-405819
---

## Job Service MVP (v1+)
Refer to the `DESCRIPTION.md` file for the full requirements.