# Jobly Integration Plan

This document outlines the steps to integrate Jobly as a new job import source into the existing job service.

## Phase 1: Setup and Configuration

1.  **New Configuration Variables:**
    *   Add the following to `.env.example` and `.env`:
        ```env
        JOBLY_API_ENDPOINT="https://jobly.almamedia.fi/aineistot/nedu_ai/jobs/jobly_jobs.json"
        JOBLY_IMPORT_CACHE_KEY="jobly_import:processing"
        JOBLY_IMPORT_COMPLETED_CACHE_KEY="jobly_import:completed"
        JOBLY_CACHE_TTL=3600
        ```
    *   Ensure `ConfigService` can access these variables.

2.  **Create Job Metadata Entity (`src/entities/job-metadata.entity.ts`):**
    ```typescript
    import { Column, Entity, Index } from 'typeorm';
    import { BaseEntity } from './base.entity';

    @Entity('job_metadata')
    @Index(['ext_id', 'key'], { unique: true })
    export class JobMetadata extends BaseEntity {
      @Column({ name: 'ext_id' })
      ext_id: string;

      @Column()
      key: string;

      @Column('jsonb')
      value: any;

      @Column({ nullable: true })
      source: string;  // e.g., 'ai_extraction', 'api', 'manual'
    }
    ```

## Phase 2: Employer Entity and Service Modifications

1.  **Modify `Employer` Entity (`src/entities/employer.entity.ts`):**
    *   Make `business_id` nullable:
        ```typescript
        @Column({ unique: true, nullable: true })
        @Index({ unique: true, where: '"business_id" IS NOT NULL' }) // Postgres specific
        business_id: string | null;
        ```
    *   Add `@IsOptional()` to `business_id` for `class-validator`.
    *   Consider if the `@Matches` decorator for `business_id` needs adjustment for nullable values.

2.  **Update `EmployerRepository` (`src/app/employer/repository/employer.repository.ts`):**
    *   Adapt `upsertByBusinessId` or create a new method like `upsertEmployer(data: EmployerData, source?: string)` that can handle employers without `business_id` by relying on `name` and `source`.
    *   For Jobly employers, the primary key for upserting will likely be a combination of `name` and a `source` identifier (e.g., 'Jobly').

3.  **Update `EmployerService` (`src/app/employer/services/employer.service.ts`):**
    *   Create `async createOrUpdateEmployerFromJobly(joblyJob: JoblyJob): Promise<Employer>`:
        *   Extracts `company` (as name), `company_logo` from `joblyJob`.
        *   Sets `language_code` (e.g., 'fi').
        *   Calls the repository to upsert the employer, passing `source: 'Jobly'`.
        *   Maps `joblyJob.company_logo` to `logoUrl`.

## Phase 3: New Jobly API Client

1.  **Create `JoblyApiClient` (`src/app/jobs/services/jobly-api.service.ts`):**
    *   Define `JoblyJob` interface based on the API response:
        ```typescript
        interface JoblyJob {
          id: number;
          publish_date: string;  // Format: "YYYY-MM-DD"
          title: string;
          city: string;
          region: string;
          country: string;
          country_code_alpha_2: string;  // ISO 3166-1 alpha-2 country code
          description: string;
          url: string;  // Direct URL to the job posting
          employment_type: string[];  // e.g., ["Vakituinen", "Kokopäiväinen"]
          occupation: string[];  // Job categories
          industry: string[];  // Industry categories (empty in example)
          company: string;  // Company name
          company_logo: string;  // URL to company logo
        }
        ```
    *   Implement `async getJobPostings(): Promise<JoblyJob[]>`:
        *   Uses `axios` or `node-fetch` for a GET request to `JOBLY_API_ENDPOINT`.
        *   Includes error handling and logging.
    *   No authentication needed.

## Phase 4: Jobly Data Conversion

1.  **Create `jobly-data-to-entity.converter.ts` (`src/app/jobs/services/helpers/jobly-data-to-entity.converter.ts`):**
    *   Implement `convertJoblyUpsertInput(joblyJobs: JoblyJob[], employerMap: Map<string, number>): { jobData: Partial<Job>[], jobLocations: Partial<JobLocation>[], jobTranslations: Partial<JobTranslation>[] }`.
        *   `employerMap` key: Jobly employer name (or generated unique ID). Value: `Employer` entity `id`.
    *   **`Job` Entity Mapping:**
        *   `ext_id`: `joblyItem.id.toString()`
        *   `employer_name`: `joblyItem.company`
        *   `employer_id`: From `employerMap`.
        *   `languages`: `['en']` (only store English)
        *   `working_time`, `continuity`: Map from `joblyItem.employment_type` using a predefined mapping constant. Use "01" as default values if no mapping exists.
            ```typescript
            // Example mapping
            export const joblyEmploymentTypeToJobFields = {
              "Vakituinen": { continuity: "01" /* Permanent */ },
              "Kokopäiväinen": { working_time: "01" /* Full-time */ },
              "Määräaikainen": { continuity: "02" /* Temporary */ },
              "Osa-aikainen": { working_time: "02" /* Part-time */ },
              "Osa-aikainen ja tuntityö": { working_time: "02" /* Part-time */ },
              // ... other types
            };
            
            // Default values to use if no mapping exists
            const DEFAULT_WORKING_TIME = "01"; // Full-time
            const DEFAULT_CONTINUITY = "01";  // Permanent
            ```
        *   `working_hours`: Potentially `01` or derived if "Osa-aikainen" or "Osa-aikainen ja tuntityö".
        *   `expires_at`: `null` or `publish_date + 30 days`. Primary expiry via feed check.
        *   `title`: Translated English version of `joblyItem.title`.
        *   `description`: Translated English version of `joblyItem.description`.
        *   `application_url`: `joblyItem.url`.
        *   `source`: **`'Jobly'`**.
        *   `published_date`: `new Date(joblyItem.publish_date)`.
        *   `last_import_date`: `new Date()`.
    *   **`JobLocation` Entity Mapping:**
        *   `job_id`: `joblyItem.id.toString()`.
        *   `country`: `joblyItem.country`.
        *   **Municipality Lookup:**
            *   Query the `municipality` table where `classificationName` matches `joblyItem.city`.
            *   Use the found municipality's `code` for the `municipality` field.
            *   Example query:
                ```typescript
                const municipality = await municipalityRepository.findOne({
                  where: { classificationName: joblyItem.city }
                });
                
                if (municipality) {
                  jobLocation.municipality = municipality.code;
                }
                ```
        *   **Region Lookup:**
            *   Query the `region` table where `classificationName` matches `joblyItem.region`.
            *   Use the found region's `code` for the `region` field.
            *   Example query:
                ```typescript
                const region = await regionRepository.findOne({
                  where: { classificationName: joblyItem.region }
                });
                
                if (region) {
                  jobLocation.region = region.code;
                }
                ```
        *   Store the original `joblyItem.city` and `joblyItem.region` in the `name` field for reference.
    *   **`JobTranslation` Entity Mapping:**
        *   **English Only:**
            *   `job_id`: `joblyItem.id.toString()`.
            *   `language`: `'en'`.
            *   `title`: Translated English version of `joblyItem.title`.
            *   `description`: Translated English version of `joblyItem.description`.

## Phase 5: Core Import Logic in `JobsImportService`

1.  **Modify `JobsImportService` (`src/app/jobs/services/jobs-import.service.ts`):**
    *   Inject `JoblyApiClient`.
    *   Create `async importFromJobly(languagesToTranslateTo: string[])`.
    *   **Caching:** Use `JOBLY_IMPORT_CACHE_KEY` and `JOBLY_IMPORT_COMPLETED_CACHE_KEY` for processing flags.
    *   **Fetch Data:** Call `joblyApiClient.getJobPostings()`.
    *   **Handle Job Expiry:**
        *   Get all `ext_id`s from the current Jobly feed (`currentJoblyExtIds`).
        *   Create `JobsImportRepository.markSourceJobsAsExpired(activeExtIds: string[], source: string)`:
            ```typescript
            async markSourceJobsAsExpired(activeExtIds: string[], source: string): Promise<number> {
              const query = this.repository
                .createQueryBuilder()
                .update(Job)
                .set({ expired: true, expires_at: new Date() })
                .where("source = :source", { source })
                .andWhere("expired = false");

              if (activeExtIds.length > 0) {
                query.andWhere("ext_id NOT IN (:...activeExtIds)", { activeExtIds });
              } else {
                // If activeExtIds is empty, all jobs from this source are expired
              }
              
              const result = await query.execute();
              return result.affected || 0;
            }
            ```
        *   Call `await this.jobsRepository.markSourceJobsAsExpired(currentJoblyExtIds, 'Jobly');`.
    *   **Process Employers:**
        *   Loop through fetched Jobly jobs, call `employerService.createOrUpdateEmployerFromJobly(joblyJob)`.
        *   Populate `employerMap` (e.g., `Map<string, number>` mapping employer name to employer ID).
    *   **Translation to English Only:**
        *   For each job posting, translate title and description to English (the only language we store in the database):
            ```typescript
            const translatedJobsPromises = fetchedJoblyJobs.map(async (job) => {
              // Create translatable structure similar to existing job import
              const titleTranslatable = [{
                languageCode: 'fi',
                value: job.title
              }];
              
              const descriptionTranslatable = [{
                languageCode: 'fi',
                value: job.description
              }];
              
              // Translate title and description to English only
              const translatedTitle = await this.translationService.translate(job.title, {
                source: 'fi',
                target: 'en'
              });
              
              const translatedDesc = await this.translationService.translate(job.description, {
                source: 'fi',
                target: 'en'
              });
              
              // Return enhanced job object with English translations
              return {
                ...job,
                title_en: translatedTitle,
                description_en: translatedDesc
              };
            });
            
            const translatedJobs = await Promise.all(translatedJobsPromises);
            ```
        *   Modify `convertJoblyUpsertInput` to use these English translations.
        *   Set job entity's `languages` array to `['en']` only.
    *   **Convert & Upsert Jobs:**
        *   Use `convertJoblyUpsertInput(translatedJobs, employerMap)`.
        *   Call `jobsRepository.upsert()` with the converted data that only includes English content.
    *   **Classification:** Reuse `generateBasicClassification`.
    *   **Logging:** Add Jobly-specific logs.

## Phase 6: Scheduling and Invocation

1.  **Scheduler:**
    *   Implement a NestJS cron task (`@Cron()`) in `JobsImportService` or a dedicated scheduling service to call `importFromJobly` (e.g., hourly).
    *   Alternatively, create a controller endpoint for manual/external triggering.

## Phase 7: AI-Powered Field Extraction

### Overview
We'll implement AI-powered extraction for two distinct types of data:
1. **Job Entity Fields**: Core fields that map directly to the Job entity
2. **JobMetadata**: Additional metadata stored in a separate table for flexibility

### 1. Job Entity Field Extraction
These fields will be extracted and mapped directly to the Job entity:
- `expires_at`: Application deadline
- `application_url`: URL to apply for the job
- `workplace_flexibility`: Remote/hybrid/onsite information
- `salary_type`: Type of salary (hourly/monthly/yearly)
- `shift_type`: Type of shift (day/evening/night/rotating)
- `part_time_hours`: Hours for part-time positions
- `work_begins`: Expected start date
- `driver_license_codes`: Required driver's license codes

### 2. JobMetadata Storage
Additional extracted information will be stored in the `job_metadata` table with this structure:
- `ext_id` (string, required): Reference to the job's external ID
- `key` (string, required, max 100 chars): Metadata field name
- `value` (JSONB, required): Extracted value (any valid JSON)
- `value_type` (string): Type of the stored value (string, number, boolean, array, object, null)
- `source` (string, required, max 50 chars): Source of the data (e.g., 'ai_extraction')
- `confidence` (float, 0-1, optional): AI confidence score if applicable

#### Validation Rules:
1. All fields are required except `confidence`
2. `key` must be alphanumeric with underscores (regex: `^[a-zA-Z0-9_]+$`)
3. `source` must be a known source identifier
4. `confidence` must be between 0 and 1 if provided
5. `value` must be a valid JSON value
6. Maximum nesting depth: 10 levels
7. Maximum key length: 100 characters
8. Maximum source length: 50 characters

### Implementation

1.  **Enhance `GeminiService` (`src/gemini/gemini.service.ts`):**
    *   Add new method `extractJobMetadata(description: string): Promise<JobMetadataDto>`
        - Uses Gemini AI to extract both Job fields and metadata
        - Returns a strongly-typed DTO with all extracted fields
        - Includes proper error handling and logging
    *   **Features:**
        - Handles various date formats and normalizes to YYYY-MM-DD
        - Validates and types all extracted data
        - Gracefully handles API failures

2.  **Create `JobMetadata` DTO (`src/gemini/dto/job-metadata.dto.ts`):**
    *   Defines the structure of extracted job metadata
    *   Includes Swagger documentation for API reference
    *   Handles all possible fields that can be extracted

3.  **Update `JobMetadataService` (`src/app/jobs/services/job-metadata.service.ts`):**
    *   Methods:
        *   `extractAndSaveMetadata(job: Job, description: string): Promise<void>`
            - Uses `GeminiService.extractJobMetadata()` to get structured data
            - Updates Job entity fields with high-confidence values
            - Saves remaining data to `job_metadata` table
            - Handles validation and type conversion
    *   **Features:**
        - Falls back to default values when extraction fails
        - Logs all extraction results for auditing
        - Updates only non-null fields to preserve existing data
        - Tracks source and confidence of extracted data

2.  **Update Import Flow:**
    *   After creating/updating a job, call `jobMetadataService.extractAndSaveMetadata()`
    *   Update job fields with high-confidence extracted data
    *   Store all extracted data in `job_metadata` for reference

## Phase 8: Testing

1.  **Unit Tests:**
    *   `JoblyApiClient`
    *   `jobly-data-to-entity.converter.ts`
    *   `JobMetadataService` and metadata extraction
    *   New/modified methods in `EmployerService`, `EmployerRepository`
    *   New/modified methods in `JobsImportService`, `JobsImportRepository`

2.  **Integration Tests:** 
    *   End-to-end Jobly import flow
    *   AI metadata extraction
    *   Job metadata storage and retrieval

3.  **AI Testing:**
    *   Test with various job descriptions
    *   Validate extraction accuracy
    *   Handle edge cases and malformed inputs

## Key Decisions & Considerations:

*   **Employer Identification for Jobly:**
    *   Chosen approach: Make `business_id` in `Employer` nullable. Upsert Jobly employers by `name` + `source='Jobly'`.
*   **Mapping `employment_type` (Jobly) to `working_time` & `continuity` (Job Entity):**
    *   A clear mapping needs to be defined (see example in Phase 4). The `Job` entity has distinct fields, so Jobly's array might populate one or both based on values.
*   **JobLocation for Jobly (City/Region):**
    *   Initial approach: Store names directly. Code lookup can be a future enhancement.
*   **AI-Powered Field Extraction:**
    *   Use `GeminiService.extractJobMetadata()` for structured data extraction
    *   Extract and store application URL from description if not provided in the main job data
    *   Store raw extracted data in `job_metadata` table with source tracking
    *   Update relevant job fields with high-confidence extractions
    *   Handle potential inaccuracies in AI extraction with proper validation
    *   Include comprehensive logging for all extraction operations
*   **Metadata Storage:**
    *   Use JSONB for flexible schema
    *   Index on `job_ext_id` and `key` for efficient lookups
    *   Include source tracking for data lineage
*   **Idempotency:** The upsert logic and expiry handling should make the import process idempotent.
*   **Error Handling:** 
    *   Robust error handling and logging throughout the new services and methods
    *   Graceful degradation if AI service is unavailable
*   **Performance Considerations:**
    *   Batch process metadata extraction where possible
    *   Consider rate limiting for AI API calls
    *   Cache common metadata patterns to reduce API calls
