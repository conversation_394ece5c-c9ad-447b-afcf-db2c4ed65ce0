{"openapi": "3.0.0", "paths": {"/education/industries/recommend": {"post": {"description": "\n      Returns a list of recommended industries based on the user's educational program.\n      The recommendations include:\n      - Industry name (translated based on responseLanguage)\n      - Industry code\n      - Projected growth rate\n      - Program alignment score\n      - Median salary range\n      \n      The response is sorted by alignment score in descending order.\n    ", "operationId": "IndustryController_getRecommendedIndustries", "parameters": [], "requestBody": {"required": true, "description": "Program code and language preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndustryRecommendationRequestDto"}}}}, "responses": {"200": {"description": "Successfully retrieved industry recommendations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IndustryRecommendationResponseDto"}}}}}, "400": {"description": "Invalid request parameters"}}, "summary": "Get industry recommendations based on program code", "tags": ["Education Industries"]}}, "/education/industries/overview": {"get": {"description": "\n      Returns a detailed overview of a specific industry including:\n      - Industry name and description\n      - Required skills and certifications\n      - Popular jobs in the industry with:\n        - Salary information\n        - Experience requirements\n        - Top employers\n        - Historical salary trends\n      \n      The response is localized based on the responseLanguage parameter.\n    ", "operationId": "IndustryController_getIndustryOverview", "parameters": [{"name": "industryName", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "responseLanguage", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "programCode", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved industry overview", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndustryViewDto"}}}}, "400": {"description": "Invalid request parameters"}, "404": {"description": "Industry not found"}}, "summary": "Get detailed overview of a specific industry", "tags": ["Education Industries"]}}, "/education/careers/paths": {"post": {"description": "\n      Returns a list of recommended career paths based on the user's educational program.\n      The recommendations include:\n      - Career title (translated based on responseLanguage)\n      - Description\n      - Required skills\n      - Growth potential score\n      - Program alignment score\n      - Salary range\n      \n      The response is sorted by alignment score in descending order.\n    ", "operationId": "CareerController_getCareerPaths", "parameters": [], "requestBody": {"required": true, "description": "Program code and language preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CareerPathsRequestDto"}}}}, "responses": {"200": {"description": "Successfully retrieved career path recommendations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CareerPathResponseDto"}}}}}, "400": {"description": "Invalid request parameters"}}, "summary": "Get career path recommendations based on program code", "tags": ["Education Careers"]}}, "/education/careers/view": {"post": {"description": "\n      Returns a detailed analysis of a specific career path based on the user's educational program.\n      The analysis includes:\n      - Skills assessment\n      - Transferable skills analysis\n      - Career progression path\n      - Future challenges and opportunities\n    ", "operationId": "CareerController_getCareerView", "parameters": [], "requestBody": {"required": true, "description": "Program code, job role, and language preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CareerViewRequestDto"}}}}, "responses": {"200": {"description": "Successfully retrieved career view analysis", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CareerViewResponseDto"}}}}, "400": {"description": "Invalid request parameters"}}, "summary": "Get detailed career view analysis", "tags": ["Education Careers"]}}, "/education/careers/suggestions": {"post": {"operationId": "CareerController_getCareerSuggestions", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CareerPathsRequestDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Education Careers"]}}, "/v1/education/programs": {"get": {"operationId": "ProgramController_findAll", "parameters": [], "responses": {"200": {"description": ""}}, "tags": ["Program"]}, "post": {"operationId": "ProgramController_create", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProgramDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Program"]}}, "/v1/education/programs/{id}": {"get": {"operationId": "ProgramController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Program"]}, "put": {"operationId": "ProgramController_update", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateProgramDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["Program"]}, "delete": {"operationId": "ProgramController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}, "tags": ["Program"]}}, "/v1/education/programs/load-from-json": {"post": {"operationId": "ProgramController_loadFromJson", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Program"]}}, "/v1/education/competences": {"get": {"description": "Retrieves a list of all competences with their associated program information", "operationId": "CompetenceController_findAll", "parameters": [], "responses": {"200": {"description": "List of competences retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompetenceWithProgramDto"}}}}}}, "summary": "Get all competences", "tags": ["Education Competences"]}, "post": {"description": "Creates a new competence with the provided details", "operationId": "CompetenceController_create", "parameters": [], "requestBody": {"required": true, "description": "Competence data to create", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCompetenceDto"}}}}, "responses": {"201": {"description": "Competence created successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompetenceDto"}}}}, "400": {"description": "Invalid competence data provided"}}, "summary": "Create a new competence", "tags": ["Education Competences"]}}, "/v1/education/competences/{id}": {"get": {"description": "Retrieves a specific competence by its unique identifier", "operationId": "CompetenceController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "The ID of the competence to retrieve", "schema": {"type": "number"}}], "responses": {"200": {"description": "Competence retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompetenceWithProgramDto"}}}}, "404": {"description": "Competence not found"}}, "summary": "Get competence by ID", "tags": ["Education Competences"]}, "put": {"description": "Updates an existing competence with the provided details", "operationId": "CompetenceController_update", "parameters": [{"name": "id", "required": true, "in": "path", "description": "The ID of the competence to update", "schema": {"type": "number"}}], "requestBody": {"required": true, "description": "Updated competence data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCompetenceDto"}}}}, "responses": {"200": {"description": "Competence updated successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompetenceDto"}}}}, "400": {"description": "Invalid competence data provided"}, "404": {"description": "Competence not found"}}, "summary": "Update a competence", "tags": ["Education Competences"]}, "delete": {"description": "Removes a competence by its ID", "operationId": "CompetenceController_remove", "parameters": [{"name": "id", "required": true, "in": "path", "description": "The ID of the competence to delete", "schema": {"type": "number"}}], "responses": {"204": {"description": "Competence deleted successfully"}, "404": {"description": "Competence not found"}}, "summary": "Delete a competence", "tags": ["Education Competences"]}}, "/v1/education/competences/program/{programCode}": {"get": {"description": "Retrieves all competences associated with a specific program code, optionally filtered by competence type", "operationId": "CompetenceController_findByProgram", "parameters": [{"name": "programCode", "required": true, "in": "path", "description": "The code of the program to find competences for", "schema": {"type": "string"}}, {"name": "type", "required": false, "in": "query", "description": "Optional filter by competence type", "schema": {"enum": ["CORE", "COMPLEMENTARY"], "type": "string"}}], "responses": {"200": {"description": "List of competences for the program retrieved successfully", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CompetenceWithProgramDto"}}}}}, "404": {"description": "Program not found"}}, "summary": "Get competences by program code", "tags": ["Education Competences"]}}, "/v1/education/competences/load-from-json": {"post": {"description": "Loads competence data from a predefined JSON file into the database", "operationId": "CompetenceController_loadFromJson", "parameters": [], "responses": {"200": {"description": "Competences loaded successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Competences loaded successfully"}}}}}}, "500": {"description": "Failed to load competences from JSON"}}, "summary": "Load competences from JSON", "tags": ["Education Competences"]}}, "/v1/education/user-competences/user/{supertokensId}": {"get": {"operationId": "UserCompetenceController_findByUserId", "parameters": [{"name": "supertokensId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["UserCompetence"]}}, "/v1/education/user-competences/user/{supertokensId}/program/{programId}": {"get": {"operationId": "UserCompetenceController_findByUserAndProgram", "parameters": [{"name": "supertokensId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "programId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["UserCompetence"]}}, "/v1/education/user-competences/select": {"post": {"operationId": "UserCompetenceController_selectCompetences", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SelectUserCompetencesDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserCompetence"]}}, "/v1/education/user-competences/user/{supertokensId}/program/{programId}/select-core": {"post": {"operationId": "UserCompetenceController_selectUserProgram", "parameters": [{"name": "supertokensId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "programId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"201": {"description": ""}}, "tags": ["UserCompetence"]}}, "/jobs": {"get": {"description": "Returns \"ok\" if the Jobs API is running properly", "operationId": "JobsController_status", "parameters": [], "responses": {"200": {"description": "API is healthy", "content": {"application/json": {"schema": {"type": "string", "example": "ok"}}}}}, "summary": "Health check endpoint", "tags": ["Jobs"]}}, "/jobs/{extId}/classify": {"post": {"operationId": "JobsController_getJob", "parameters": [{"name": "extId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordsData"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request - Missing or invalid data"}, "404": {"description": "Job not found"}}, "summary": "Get job by external ID using keyword matching", "tags": ["Jobs"]}}, "/jobs/import": {"post": {"description": "Publishes a message to the PubSub topic to start the job import process", "operationId": "JobsController_sendImportEvent", "parameters": [], "responses": {"201": {"description": "Import event successfully triggered", "content": {"application/json": {"schema": {"type": "object", "properties": {"messageId": {"type": "string", "description": "ID of the published message"}}}}}}, "500": {"description": "Failed to publish message to PubSub"}}, "summary": "Trigger job import process", "tags": ["Jobs"]}}, "/jobs/processing": {"post": {"description": "Manually starts the job processing pipeline for unprocessed jobs", "operationId": "JobsController_processing", "parameters": [], "responses": {"201": {"description": "Processing successfully started", "content": {"application/json": {"schema": {"type": "object", "properties": {"processed": {"type": "number", "description": "Number of jobs processed"}, "success": {"type": "boolean", "description": "Processing status"}}}}}}, "500": {"description": "Failed to process jobs"}}, "summary": "Trigger job processing", "tags": ["Jobs"]}}, "/jobs/jobly-import": {"post": {"description": "Manually starts the import process for Jobly jobs (disabled in production)", "operationId": "JobsController_importJobly", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"languages": {"type": "array", "items": {"type": "string"}, "description": "Languages to translate job data to (defaults to ['en'])", "example": ["en"]}, "limitJobs": {"type": "number", "description": "Optional limit for number of jobs to import (for testing)", "example": 20}}}}}}, "responses": {"201": {"description": "Import successfully started", "content": {"application/json": {"schema": {"type": "object", "properties": {"processed": {"type": "number", "description": "Number of jobs processed"}, "success": {"type": "boolean", "description": "Import status"}}}}}}, "403": {"description": "Jobly import is disabled in production"}, "500": {"description": "Failed to import Jobly jobs"}}, "summary": "Trigger Jobly import process", "tags": ["Jobs"]}}, "/jobs/jobly-import/clear-cache": {"post": {"description": "Clears the Jobly import cache lock to allow new import processes (disabled in production)", "operationId": "JobsController_clearJoblyImportCache", "parameters": [], "responses": {"201": {"description": "<PERSON><PERSON> cleared successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Operation status"}, "message": {"type": "string", "description": "Status message"}}}}}}, "403": {"description": "Jobly cache operations are disabled in production"}}, "summary": "Clear Jobly import cache", "tags": ["Jobs"]}}, "/jobs/export-batch": {"post": {"description": "Exports unprocessed jobs to a CSV file for batch processing with external tools", "operationId": "JobsController_exportBatch", "parameters": [], "requestBody": {"required": true, "description": "Export configuration", "content": {"application/json": {"schema": {"type": "object", "properties": {"batchSize": {"type": "number", "description": "Maximum number of jobs to export (default: 1000)", "example": 500}}}}}}, "responses": {"200": {"description": "No jobs available for export", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "message": {"type": "string", "example": "No unprocessed jobs available"}}}}}}, "201": {"description": "Jobs successfully exported", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "filePath": {"type": "string", "example": "/tmp/jobs-export-20250313.csv"}, "message": {"type": "string", "example": "Exported batch file to /tmp/jobs-export-20250313.csv"}}}}}}}, "summary": "Export unprocessed jobs to CSV", "tags": ["Jobs"]}}, "/jobs/callback": {"post": {"description": "Receives classification results from external job processors and updates job records", "operationId": "JobsController_processJobCallback", "parameters": [], "requestBody": {"required": true, "description": "Job classification data", "content": {"application/json": {"schema": {"type": "object", "required": ["ext_id", "result"], "properties": {"ext_id": {"type": "string", "description": "External ID of the job", "example": "job-123456"}, "result": {"type": "object", "description": "Classification results from the processor", "example": {"skills": ["JavaScript", "React", "Node.js"], "categories": ["Software Development", "Web Development"], "seniority": "Mid-level"}}}}}}}, "responses": {"201": {"description": "Job successfully processed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Successfully processed job: job-123456"}}}}}}, "400": {"description": "Missing required fields in request body"}, "404": {"description": "Job not found"}, "500": {"description": "Error processing job"}}, "summary": "Job classification callback endpoint", "tags": ["Jobs"]}}, "/jobs/upload-to-pinecone": {"post": {"description": "Uploads processed jobs to Pinecone vector database for semantic search", "operationId": "JobsController_uploadToPinecone", "parameters": [], "requestBody": {"required": true, "description": "Upload configuration", "content": {"application/json": {"schema": {"type": "object", "properties": {"batchSize": {"type": "number", "description": "Maximum number of jobs to process in each batch (default: 100)", "example": 100}}}}}}, "responses": {"200": {"description": "No jobs available to upload", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "count": {"type": "number", "example": 0}, "message": {"type": "string", "example": "No processed jobs available to upload"}}}}}}, "201": {"description": "Jobs successfully uploaded to Pinecone", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "count": {"type": "number", "example": 42}, "message": {"type": "string", "example": "Successfully uploaded 42 jobs to Pinecone"}}}}}}, "500": {"description": "Error uploading jobs to Pinecone"}}, "summary": "Upload jobs to Pinecone", "tags": ["Jobs"]}}, "/jobs/update-pinecone-keywords": {"post": {"description": "Updates all jobs in Pinecone to ensure they have the keywords field properly populated", "operationId": "JobsController_updatePineconeKeywords", "parameters": [], "requestBody": {"required": true, "description": "Update configuration", "content": {"application/json": {"schema": {"type": "object", "properties": {"batchSize": {"type": "number", "description": "Maximum number of jobs to process in each batch (default: 100)", "example": 100}}}}}}, "responses": {"200": {"description": "No jobs available to update", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": false}, "count": {"type": "number", "example": 0}, "message": {"type": "string", "example": "No processed jobs available to update"}}}}}}, "201": {"description": "Jobs successfully updated in Pinecone", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "count": {"type": "number", "example": 42}, "message": {"type": "string", "example": "Successfully updated 42 jobs in Pinecone with keywords"}}}}}}, "500": {"description": "Error updating jobs in Pinecone"}}, "summary": "Update jobs in Pinecone with keywords", "tags": ["Jobs"]}}, "/jobs/pinecone/update-expires-at": {"post": {"description": "Converts all expires_at fields in Pinecone from ISO string to Unix timestamp for better filtering", "operationId": "JobsController_updateExpiresAtInPinecone", "parameters": [], "responses": {"201": {"description": "Update process successfully started", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean"}, "jobsUpdated": {"type": "number"}}}}}}, "500": {"description": "Failed to update expires_at fields in Pinecone"}}, "summary": "Update expires_at field in Pinecone", "tags": ["Jobs"]}}, "/jobs/search": {"post": {"description": "Search for jobs using smart profile and filters from ProcessedJob entity with pagination support", "operationId": "JobsController_searchJobs", "parameters": [], "requestBody": {"required": true, "description": "Search parameters", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaginatedSearchDto"}}}}, "responses": {"200": {"description": "Successfully retrieved jobs"}}, "summary": "Search for jobs", "tags": ["Jobs"]}}, "/jobs/top": {"post": {"description": "Retrieves jobs with the highest match scores for the user", "operationId": "JobsController_getTopJobs", "parameters": [], "requestBody": {"required": true, "description": "Search parameters with user profile", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SmartProfileSearchDto"}}}}, "responses": {"200": {"description": "Top jobs retrieved successfully"}}, "summary": "Get top matching jobs", "tags": ["Jobs"]}}, "/jobs/applications": {"post": {"description": "Retrieves jobs that the user has applied to", "operationId": "JobsController_getUserApplications", "parameters": [], "requestBody": {"required": true, "description": "Search parameters with user ID", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobUserApplicationsDto"}}}}, "responses": {"200": {"description": "User applications retrieved successfully"}, "400": {"description": "Missing user_id parameter"}}, "summary": "Get user job applications", "tags": ["Jobs"]}}, "/jobs/update-expired": {"post": {"description": "Marks jobs as expired if their expiration date has passed", "operationId": "JobsController_updateExpiredJobs", "parameters": [], "responses": {"200": {"description": "Successfully updated expired status", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "newly_expired_count": {"type": "number", "example": 15}, "total_expired_count": {"type": "number", "example": 50}, "total_unexpired_count": {"type": "number", "example": 150}, "total_unexpired_in_pinecone": {"type": "number", "example": 120}, "message": {"type": "string", "example": "Successfully marked 15 jobs as expired. Total expired: 50, Total unexpired: 150, Unexpired in Pinecone: 120"}}}}}}}, "summary": "Update expired status for jobs", "tags": ["Jobs"]}}, "/jobs/for-you": {"post": {"description": "Retrieves persisted personalized job recommendations for a specific user", "operationId": "JobsController_getForYouRecommendations", "parameters": [], "requestBody": {"required": true, "description": "User ID for which to retrieve recommendations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserIdDto"}}}}, "responses": {"200": {"description": "Recommendations retrieved successfully"}, "400": {"description": "Missing user ID or invalid request"}}, "summary": "Get \"For You\" job recommendations", "tags": ["Jobs"]}}, "/jobs/view": {"post": {"description": "Records that a user has viewed a specific job", "operationId": "JobsController_mark<PERSON><PERSON>iewed", "parameters": [], "requestBody": {"required": true, "description": "Job view data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobViewDto"}}}}, "responses": {"201": {"description": "Job successfully marked as viewed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Job successfully marked as viewed"}}}}}}, "400": {"description": "Missing required fields in request body"}, "404": {"description": "Job recommendation not found"}}, "summary": "Mark a job as viewed", "tags": ["Jobs"]}}, "/jobs/rate": {"post": {"description": "Records a user's rating (like/dislike) for a specific job", "operationId": "JobsController_rateJob", "parameters": [], "requestBody": {"required": true, "description": "Job rating data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRatingDto"}}}}, "responses": {"201": {"description": "Job rating successfully updated", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Job rating successfully updated"}}}}}}, "400": {"description": "Missing required fields in request body"}, "404": {"description": "Job recommendation not found"}}, "summary": "Rate a job", "tags": ["Jobs"]}}, "/jobs/classification": {"post": {"description": "Stores AI classification data for a specific job and user", "operationId": "JobsController_storeJobClassification", "parameters": [], "requestBody": {"required": true, "description": "Job classification data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobClassificationDto"}}}}, "responses": {"201": {"description": "Classification data successfully stored", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Classification data successfully stored"}}}}}}, "400": {"description": "Missing required fields in request body"}, "404": {"description": "Job recommendation not found"}}, "summary": "Store job classification data", "tags": ["Jobs"]}}, "/jobs/embedding": {"post": {"description": "Generates an embedding for the provided text and writes it to a file", "operationId": "JobsController_generateEmbedding", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "properties": {"text": {"type": "string", "example": "This is a test text for embedding generation"}}}}}}, "responses": {"201": {"description": "Embedding generated successfully", "content": {"application/json": {"schema": {"type": "object", "properties": {"message": {"type": "string", "example": "Embedding generated and written to file"}, "dimensions": {"type": "number", "example": 3072}, "filePath": {"type": "string", "example": "/path/to/emb.txt"}}}}}}}, "summary": "Generate embedding for testing", "tags": ["Jobs"]}}, "/jobs/{extId}": {"get": {"operationId": "JobsController_getJobByExtId", "parameters": [{"name": "extId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Job found"}, "404": {"description": "Job not found"}}, "summary": "Get a single job by external ID", "tags": ["Jobs"]}}, "/v1/job-applications": {"post": {"description": "Creates a new job application record with CV data", "operationId": "JobApplicationController_createJobApplication", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobApplicationDto"}}}}, "responses": {"201": {"description": "The job application has been successfully created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobApplicationResponseDto"}}}}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Job not found"}}, "security": [{"bearer": []}], "summary": "Create a new job application", "tags": ["job-applications"]}, "get": {"description": "Returns all job applications for the specified user ID", "operationId": "JobApplicationController_getJobApplicationsByUserId", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of job applications", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/JobApplicationResponseDto"}}}}}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}}, "security": [{"bearer": []}], "summary": "Get all job applications for a user", "tags": ["job-applications"]}}, "/v1/job-applications/{id}": {"get": {"description": "Returns a specific job application by its ID", "operationId": "JobApplicationController_getJobApplicationById", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "The job application", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobApplicationResponseDto"}}}}, "400": {"description": "Bad Request"}, "401": {"description": "Unauthorized"}, "404": {"description": "Job application not found"}}, "security": [{"bearer": []}], "summary": "Get a job application by ID", "tags": ["job-applications"]}}, "/for-you/generate": {"post": {"description": "Initiates the generation of personalized job recommendations for a specific user and returns immediately", "operationId": "ForYouController_generateForYouRecommendations", "parameters": [], "requestBody": {"required": true, "description": "User ID for which to generate recommendations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserIdDto"}}}}, "responses": {"201": {"description": "Recommendation generation process started successfully"}, "400": {"description": "Missing user ID or invalid request"}}, "summary": "Generate \"For You\" job recommendations", "tags": ["ForYou"]}}, "/for-you/recommendations": {"post": {"description": "Retrieves persisted personalized job recommendations for a specific user", "operationId": "ForYouController_getForYouRecommendations", "parameters": [], "requestBody": {"required": true, "description": "User ID for which to retrieve recommendations", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserIdDto"}}}}, "responses": {"200": {"description": "Recommendations retrieved successfully"}, "400": {"description": "Missing user ID or invalid request"}}, "summary": "Get \"For You\" job recommendations", "tags": ["ForYou"]}}, "/for-you/status": {"post": {"description": "Checks if a user has recommendations and whether generation is in progress", "operationId": "ForYouController_getForYouStatus", "parameters": [], "requestBody": {"required": true, "description": "User ID to check status for", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserIdDto"}}}}, "responses": {"200": {"description": "Status retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForYouStatusDto"}}}}, "400": {"description": "Missing user ID or invalid request"}}, "summary": "Get status of \"For You\" recommendations", "tags": ["ForYou"]}}, "/for-you/view": {"post": {"description": "Records that a user has viewed a specific job", "operationId": "ForYouController_mark<PERSON>ob<PERSON>iewed", "parameters": [], "requestBody": {"required": true, "description": "Job view data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobViewDto"}}}}, "responses": {"201": {"description": "Job successfully marked as viewed", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Job successfully marked as viewed"}}}}}}, "400": {"description": "Missing required fields in request body"}, "404": {"description": "Job recommendation not found"}}, "summary": "Mark job as viewed", "tags": ["ForYou"]}}, "/for-you/rate": {"post": {"description": "Records a user's rating (like/dislike) for a specific job", "operationId": "ForYouController_rateJob", "parameters": [], "requestBody": {"required": true, "description": "Job rating data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobRatingDto"}}}}, "responses": {"201": {"description": "Job rating successfully updated", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Job rating successfully updated"}}}}}}, "400": {"description": "Missing required fields in request body"}, "404": {"description": "Job recommendation not found"}}, "summary": "Rate a job", "tags": ["ForYou"]}}, "/for-you/classification": {"post": {"description": "Stores AI classification data for a specific job and user", "operationId": "ForYouController_storeJobClassification", "parameters": [], "requestBody": {"required": true, "description": "Job classification data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobClassificationDto"}}}}, "responses": {"201": {"description": "Classification data successfully stored", "content": {"application/json": {"schema": {"type": "object", "properties": {"success": {"type": "boolean", "example": true}, "message": {"type": "string", "example": "Classification data successfully stored"}}}}}}, "400": {"description": "Missing required fields in request body"}, "404": {"description": "Job recommendation not found"}}, "summary": "Store job classification data", "tags": ["ForYou"]}}, "/v1/saved-jobs/{extId}": {"post": {"operationId": "SavedJobsController_saveJob", "parameters": [{"name": "extId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"201": {"description": "Job saved", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SavedJobResponseDto"}}}}, "404": {"description": "Job not found"}}, "security": [{"bearer": []}], "summary": "Save a job for a user", "tags": ["Saved<PERSON><PERSON>s"]}, "delete": {"operationId": "SavedJobsController_removeSavedJob", "parameters": [{"name": "extId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"204": {"description": "Job unsaved"}}, "security": [{"bearer": []}], "summary": "Remove a saved job for a user", "tags": ["Saved<PERSON><PERSON>s"]}}, "/v1/saved-jobs": {"get": {"operationId": "SavedJobsController_getSavedJobs", "parameters": [{"name": "userId", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "List of saved jobs", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SavedJobResponseDto"}}}}}}, "security": [{"bearer": []}], "summary": "Get saved jobs for a user", "tags": ["Saved<PERSON><PERSON>s"]}}, "/api/translation/translate": {"post": {"operationId": "TranslationController_translateText", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TranslateTextDto"}}}}, "responses": {"200": {"description": "Translation successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TranslationResponseDto"}}}}, "400": {"description": "Bad request"}}, "summary": "Translate a single text from English to Finnish or Swedish", "tags": ["translation"]}, "get": {"operationId": "TranslationController_translateTextGet", "parameters": [{"name": "text", "required": true, "in": "query", "description": "Text to translate (English)", "schema": {"type": "string"}}, {"name": "targetLanguage", "required": true, "in": "query", "description": "Target language code (fi or sv)", "schema": {"enum": ["fi", "sv", "en", "ru"], "type": "string"}}], "responses": {"200": {"description": "Translation successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TranslationResponseDto"}}}}, "400": {"description": "Bad request"}}, "summary": "Translate a text using GET method", "tags": ["translation"]}}, "/api/translation/batch": {"post": {"operationId": "TranslationController_batchTranslate", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchTranslateDto"}}}}, "responses": {"200": {"description": "Translations successful", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchTranslationResponseDto"}}}}, "400": {"description": "Bad request"}}, "summary": "Translate multiple texts from English to Finnish or Swedish", "tags": ["translation"]}}, "/v1/employers": {"get": {"operationId": "EmployerController_findAll", "parameters": [], "responses": {"200": {"description": "Return all employers", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Employer"}}}}}}, "summary": "Get all employers", "tags": ["employers"]}}, "/v1/employers/{id}": {"get": {"operationId": "EmployerController_findOne", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Employer ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "Return the employer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Employer"}}}}, "404": {"description": "Employer not found"}}, "summary": "Get employer by ID", "tags": ["employers"]}}, "/v1/employers/business/{businessId}": {"get": {"operationId": "EmployerController_findByBusinessId", "parameters": [{"name": "businessId", "required": true, "in": "path", "description": "Business ID", "schema": {"type": "string"}}], "responses": {"200": {"description": "Return the employer", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Employer"}}}}, "404": {"description": "Employer not found"}}, "summary": "Get employer by business ID", "tags": ["employers"]}}, "/v1/react-app/jobs/match": {"post": {"operationId": "ReactAppController_getMatchedJobs", "parameters": [], "responses": {"200": {"description": "Returns jobs that match the provided smart profile with skill information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MatchedJobsResponseDto"}}}}}, "summary": "Get jobs that match a smart profile", "tags": ["React App"]}}, "/v1/react-app/skill-suggestions": {"post": {"description": "Analyzes the provided smart profile data and generates AI-powered skill suggestions based on background and experience. The smart profile should be provided as a JSON string containing skills, experience, and other relevant professional information.", "operationId": "ReactAppController_getMySkillSuggestions", "parameters": [], "responses": {"200": {"description": "Skill suggestions generated successfully", "content": {"application/json": {"schema": {"example": {"success": true, "data": {"skills": ["JavaScript", "React", "Node.js", "TypeScript", "API Development", "Cloud Architecture", "System Design", "Data Structures", "Agile Methodologies", "CI/CD"]}, "message": "Skill suggestions generated successfully", "timestamp": "2025-03-18T22:01:38+02:00"}}}}}, "400": {"description": "Bad Request - Invalid smart profile format", "content": {"application/json": {"schema": {"example": {"statusCode": 400, "message": ["smartProfile must be a string"], "error": "Bad Request"}}}}}, "404": {"description": "Smart profile not found or invalid", "content": {"application/json": {"schema": {"example": {"success": false, "data": {"skills": []}, "message": "Failed to generate skill suggestions: Smart profile not found or invalid", "timestamp": "2025-03-18T22:01:38+02:00"}}}}}, "500": {"description": "Internal Server Error - AI service connection failure", "content": {"application/json": {"schema": {"example": {"success": false, "data": {"skills": []}, "message": "Failed to generate skill suggestions: Service unavailable", "timestamp": "2025-03-18T22:01:38+02:00"}}}}}}, "summary": "Generate skill suggestions based on smart profile", "tags": ["React App"]}}, "/v1/react-app/keywords-data": {"post": {"description": "Analyzes the provided smart profile data and extracts keywords and skill contexts using AI analysis. Returns structured keyword data that can be used for job matching and recommendations.", "operationId": "ReactAppController_generateKeywordsData", "parameters": [], "responses": {"200": {"description": "Keywords data generated successfully", "schema": {"example": {"success": true, "data": {"keywords": ["javascript", "react", "nodejs", "typescript", "aws"], "skill_contexts": ["web development", "frontend", "backend", "cloud"]}, "message": "Keywords data generated successfully", "timestamp": "2025-03-19T00:48:39+02:00"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KeywordsResponseDto"}}}}, "400": {"description": "Bad Request - Invalid smart profile format", "content": {"application/json": {"schema": {"example": {"statusCode": 400, "message": ["smartProfile must be a string or object"], "error": "Bad Request"}}}}}, "404": {"description": "Smart profile not found or invalid", "content": {"application/json": {"schema": {"example": {"success": false, "data": {"keywords": [], "skill_contexts": []}, "message": "Failed to generate keywords: Smart profile not found or invalid", "timestamp": "2025-03-19T00:48:39+02:00"}}}}}, "500": {"description": "Internal Server Error - AI service connection failure", "content": {"application/json": {"schema": {"example": {"success": false, "data": {"keywords": [], "skill_contexts": []}, "message": "Failed to generate keywords: Service unavailable", "timestamp": "2025-03-19T00:48:39+02:00"}}}}}}, "summary": "Generate keyword data from smart profile", "tags": ["React App"]}}, "/v1/user-actions/search": {"post": {"operationId": "UserActionController_trackSearch", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackSearchDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserAction"]}}, "/v1/user-actions/view": {"post": {"operationId": "UserActionController_trackJobView", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackJobViewDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserAction"]}}, "/v1/user-actions/save": {"post": {"operationId": "UserActionController_trackJobSave", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackJobSaveDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserAction"]}}, "/v1/user-actions/unsave": {"post": {"operationId": "UserActionController_trackJobUnsave", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackJobSaveDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserAction"]}}, "/v1/user-actions/apply": {"post": {"operationId": "UserActionController_trackJobApply", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackJobApplyDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserAction"]}}, "/v1/user-actions/recommendations-shown": {"post": {"operationId": "UserActionController_trackRecommendationsShown", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackRecommendationsShownDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserAction"]}}, "/v1/user-actions/recommendation-clicked": {"post": {"operationId": "UserActionController_trackRecommendationClicked", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TrackRecommendationClickedDto"}}}}, "responses": {"201": {"description": ""}}, "tags": ["UserAction"]}}, "/v1/user-metrics/{timeframe}": {"get": {"operationId": "UserMetricsController_getUserMetrics", "parameters": [{"name": "timeframe", "required": true, "in": "path", "description": "Timeframe for metrics (daily, weekly, monthly)", "schema": {"enum": ["daily", "weekly", "monthly"], "type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "User ID (optional, defaults to authenticated user)", "schema": {"type": "string"}}, {"name": "fresh", "required": false, "in": "query", "description": "Force fresh calculation (optional)", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "User metrics retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserMetricsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get user metrics by timeframe", "tags": ["UserMetrics"]}}, "/v1/user-metrics/{timeframe}/calculate": {"post": {"operationId": "UserMetricsController_triggerMetricsCalculation", "parameters": [{"name": "timeframe", "required": true, "in": "path", "description": "Timeframe for metrics (daily, weekly, monthly)", "schema": {"enum": ["daily", "weekly", "monthly"], "type": "string"}}, {"name": "userId", "required": false, "in": "query", "description": "User ID (optional, defaults to authenticated user)", "schema": {"type": "string"}}], "responses": {"201": {"description": "Metric calculation triggered successfully"}}, "security": [{"bearer": []}], "summary": "Trigger calculation of user metrics", "tags": ["UserMetrics"]}}, "/v1/org-metrics/{organizationId}/{timeframe}": {"get": {"operationId": "OrgMetricsController_getOrgMetrics", "parameters": [{"name": "organizationId", "required": true, "in": "path", "description": "Organization ID", "schema": {"type": "string"}}, {"name": "timeframe", "required": true, "in": "path", "description": "Timeframe for metrics (daily, weekly, monthly)", "schema": {"enum": ["daily", "weekly", "monthly"], "type": "string"}}, {"name": "fresh", "required": false, "in": "query", "description": "Force fresh calculation (optional)", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "Organization metrics retrieved successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrgMetricsResponseDto"}}}}}, "security": [{"bearer": []}], "summary": "Get organization metrics by timeframe", "tags": ["OrganizationMetrics"]}}, "/v1/org-metrics/{organizationId}/{timeframe}/calculate": {"post": {"operationId": "OrgMetricsController_triggerMetricsCalculation", "parameters": [{"name": "organizationId", "required": true, "in": "path", "description": "Organization ID", "schema": {"type": "string"}}, {"name": "timeframe", "required": true, "in": "path", "description": "Timeframe for metrics (daily, weekly, monthly)", "schema": {"enum": ["daily", "weekly", "monthly"], "type": "string"}}], "responses": {"201": {"description": "Metric calculation triggered successfully"}}, "security": [{"bearer": []}], "summary": "Trigger calculation of organization metrics", "tags": ["OrganizationMetrics"]}}, "/react-smart-profile/profile/{id}": {"get": {"operationId": "ReactSmartProfileController_findByProfileId", "parameters": [{"name": "id", "required": true, "in": "path", "description": "Profile ID", "schema": {"type": "number"}}], "responses": {"200": {"description": "The profile has been successfully retrieved."}}, "summary": "Find a profile by profile ID", "tags": ["react-smart-profile"]}}, "/locations": {"get": {"description": "Returns \"ok\" if the Location API is running properly", "operationId": "LocationController_status", "parameters": [], "responses": {"200": {"description": "API is healthy", "content": {"application/json": {"schema": {"type": "string", "example": "ok"}}}}}, "summary": "Health check endpoint", "tags": ["Locations"]}}, "/locations/municipalities": {"get": {"description": "Returns a list of all municipalities", "operationId": "LocationController_getAllMunicipalities", "parameters": [], "responses": {"200": {"description": "Successfully retrieved municipalities", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Municipality"}}}}}}, "summary": "Get all municipalities", "tags": ["Locations"]}}, "/locations/municipalities/{code}": {"get": {"description": "Returns a municipality by its code", "operationId": "LocationController_getMunicipalityByCode", "parameters": [{"name": "code", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved municipality", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Municipality"}}}}, "404": {"description": "Municipality not found"}}, "summary": "Get municipality by code", "tags": ["Locations"]}}, "/locations/regions": {"get": {"description": "Returns a list of all regions", "operationId": "LocationController_getAllRegions", "parameters": [], "responses": {"200": {"description": "Successfully retrieved regions", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Region"}}}}}}, "summary": "Get all regions", "tags": ["Locations"]}}, "/locations/regions/{code}": {"get": {"description": "Returns a region by its code", "operationId": "LocationController_getRegionByCode", "parameters": [{"name": "code", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved region", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Region"}}}}, "404": {"description": "Region not found"}}, "summary": "Get region by code", "tags": ["Locations"]}}, "/v1/cv-workspace/augment": {"post": {"description": "Processes and enhances CV content based on the request type", "operationId": "CvWorkspaceController_augmentContent", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AugmentRequestDto"}}}}, "responses": {"200": {"description": "Content successfully augmented", "schema": {"example": {"success": true, "data": {"augmentedContent": "Enhanced content...", "suggestions": ["Add more details about your technical skills", "Highlight your achievements with measurable results"]}, "message": "Content successfully augmented", "timestamp": "2025-03-24T05:19:28+02:00"}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AugmentResponseDto"}}}}, "400": {"description": "Bad Request - Invalid input format", "content": {"application/json": {"schema": {"example": {"statusCode": 400, "message": ["reqType must be a valid enum value", "data must be an object"], "error": "Bad Request"}}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"example": {"success": false, "data": {}, "message": "Failed to augment content: Service unavailable", "timestamp": "2025-03-24T05:19:28+02:00"}}}}}}, "summary": "Augment CV content", "tags": ["CV Workspace"]}}, "/mcp": {"post": {"operationId": "MCPController_handleMCPRequest", "parameters": [{"name": "x-correlation-id", "required": true, "in": "header", "schema": {"type": "string"}}, {"name": "x-service-token", "required": true, "in": "header", "schema": {"type": "string"}}], "requestBody": {"required": true, "description": "MCP Request", "content": {"application/json": {"schema": {"type": "object", "properties": {"method": {"type": "string"}, "params": {"type": "object"}, "id": {"oneOf": [{"type": "string"}, {"type": "number"}]}}, "required": ["method"]}}}}, "responses": {"200": {"description": "MCP Response"}, "401": {"description": "Unauthorized"}, "429": {"description": "Rate limit exceeded"}, "500": {"description": "Internal server error"}}, "summary": "Handle MCP protocol requests", "tags": ["MCP"]}}}, "info": {"title": "NEDU Job Service API", "description": "API documentation for the NEDU Job Service", "version": "1.0", "contact": {}}, "tags": [{"name": "Education Industries", "description": "Endpoints related to education and industry recommendations"}], "servers": [], "components": {"schemas": {"IndustryRecommendationRequestDto": {"type": "object", "properties": {"programCode": {"type": "string", "description": "The program code of the user", "example": "CS2021"}, "responseLanguage": {"type": "string", "description": "The preferred response language", "example": "en"}, "languagesSpoken": {"description": "Languages spoken by the user", "example": ["en", "fi"], "type": "array", "items": {"type": "string"}}}, "required": ["programCode", "responseLanguage", "languagesSpoken"]}, "IndustryRecommendationResponseDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the industry", "example": "Software Development"}, "code": {"type": "string", "description": "Industry code", "example": "IT001"}, "projectedIndustryGrowth": {"type": "number", "description": "Projected industry growth percentage", "example": 12.5}, "alignmentScore": {"type": "number", "description": "Alignment score between program and industry", "example": 85.5}, "medianSalary": {"type": "string", "description": "Median salary in the industry", "example": "€45,000 - €65,000"}}, "required": ["name", "code", "projectedIndustryGrowth", "alignmentScore", "medianSalary"]}, "SkillDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the skill"}, "isAquired": {"type": "boolean", "description": "Whether the skill is acquired"}, "type": {"type": "string", "description": "Type of skill"}}, "required": ["name", "isAquired", "type"]}, "CertificationDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the certification"}, "level": {"type": "string", "description": "Level of certification"}}, "required": ["name", "level"]}, "TopEmployerDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the employer"}, "location": {"type": "string", "description": "Location of the employer"}, "website": {"type": "string", "description": "Website URL of the employer"}, "payRange": {"type": "string", "description": "Salary range offered by the employer"}}, "required": ["name", "location", "website", "payRange"]}, "SalaryByYearDto": {"type": "object", "properties": {"year": {"type": "string", "description": "Year of the salary data"}, "salary": {"type": "number", "description": "Average salary for the year"}}, "required": ["year", "salary"]}, "SalaryByExperienceDto": {"type": "object", "properties": {"experience": {"type": "string", "description": "Experience level"}, "salary": {"type": "number", "description": "Average salary for the experience level"}}, "required": ["experience", "salary"]}, "PopularJobDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Job title"}, "salary": {"type": "string", "description": "Average salary range"}, "experience": {"type": "string", "description": "Required experience level"}, "popularityLevel": {"type": "string", "description": "Job popularity level"}, "topEmployers": {"description": "List of top employers", "type": "array", "items": {"$ref": "#/components/schemas/TopEmployerDto"}}, "salaryByYear": {"description": "Historical salary data by year", "type": "array", "items": {"$ref": "#/components/schemas/SalaryByYearDto"}}, "salaryByExperience": {"description": "Salary data by experience level", "type": "array", "items": {"$ref": "#/components/schemas/SalaryByExperienceDto"}}}, "required": ["name", "salary", "experience", "popularityLevel", "topEmployers", "salaryByYear", "salaryByExperience"]}, "IndustryViewDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the industry"}, "description": {"type": "string", "description": "Description of the industry"}, "skills": {"description": "List of relevant skills", "type": "array", "items": {"$ref": "#/components/schemas/SkillDto"}}, "certifications": {"description": "List of relevant certifications", "type": "array", "items": {"$ref": "#/components/schemas/CertificationDto"}}, "popularJobs": {"description": "List of popular jobs in the industry", "type": "array", "items": {"$ref": "#/components/schemas/PopularJobDto"}}}, "required": ["name", "description", "skills", "certifications", "popularJobs"]}, "CareerPathsRequestDto": {"type": "object", "properties": {"programCode": {"type": "string", "description": "Educational program code", "example": "CS-101"}, "languagesSpoken": {"description": "Languages spoken by the user", "example": ["en", "fr"], "type": "array", "items": {"type": "array"}}, "responseLanguage": {"type": "string", "description": "Preferred language for the response", "example": "en"}}, "required": ["programCode", "languagesSpoken", "responseLanguage"]}, "CareerSkill": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the skill", "example": "Problem Solving"}, "isAcquired": {"type": "boolean", "description": "Whether the skill is acquired through the degree program", "example": true}, "type": {"type": "string", "description": "Type of skill", "enum": ["Technical", "Soft", "Domain", "Practical"], "example": "Technical"}}, "required": ["name", "isAcquired", "type"]}, "CareerPathResponseDto": {"type": "object", "properties": {"role": {"type": "string", "description": "Job Role name", "example": "Software Developer"}, "popularityLevel": {"type": "string", "description": "Popularity and growth level of the role", "enum": ["High Growth", "Moderate", "Steady", "Declining", "<PERSON><PERSON>"], "example": "High Growth"}, "salaryRange": {"type": "string", "description": "Salary range for role in Finland", "example": "€45,000 - €60,000"}, "alignment": {"type": "string", "description": "Alignment of the role with the student's profile", "enum": ["Highly aligned", "Moderately aligned", "Partially aligned", "Not aligned"], "example": "Highly aligned"}, "skills": {"description": "Skills that will help succeed in this role", "maxItems": 4, "type": "array", "items": {"$ref": "#/components/schemas/CareerSkill"}}}, "required": ["role", "popularityLevel", "salaryRange", "alignment", "skills"]}, "CareerViewRequestDto": {"type": "object", "properties": {"programCode": {"type": "string", "description": "Educational program code", "example": "CS-101"}, "jobRole": {"type": "string", "description": "Job role to analyze", "example": "Software Developer"}, "languagesSpoken": {"description": "Languages spoken by the user", "example": ["en", "fr"], "type": "array", "items": {"type": "array"}}, "responseLanguage": {"type": "string", "description": "Preferred language for the response", "example": "en"}}, "required": ["programCode", "jobRole", "languagesSpoken", "responseLanguage"]}, "CareerSkillAssessment": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the skill", "example": "Python Programming"}, "isAquired": {"type": "boolean", "description": "Whether the skill is acquired", "example": true}, "comment": {"type": "string", "description": "Comment about the skill acquisition", "example": "Strong foundation from coursework"}}, "required": ["name", "isAquired", "comment"]}, "TransferableSkill": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the transferable skill", "example": "Problem Solving"}, "existing": {"type": "string", "description": "Current context of the skill", "example": "Academic projects"}, "destination": {"type": "string", "description": "How the skill applies to the target role", "example": "Production system debugging"}}, "required": ["name", "existing", "destination"]}, "CareerPathStep": {"type": "object", "properties": {"role": {"type": "string", "description": "Role title", "example": "Junior Developer"}, "yearsExperience": {"type": "string", "description": "Years of experience required", "example": "0-2 years"}, "skills": {"description": "Key skills for this step", "example": ["JavaScript", "Git", "Agile"], "type": "array", "items": {"type": "string"}}, "salary": {"type": "string", "description": "Expected salary range", "example": "€35,000 - €45,000"}, "icon": {"type": "string", "description": "Icon identifier for the role", "example": "developer"}}, "required": ["role", "yearsExperience", "skills", "salary", "icon"]}, "CareerProgressionPath": {"type": "object", "properties": {"steps": {"description": "Career progression steps", "type": "array", "items": {"$ref": "#/components/schemas/CareerPathStep"}}, "futureChallenges": {"type": "string", "description": "Potential future challenges", "example": "Rapid technology changes requiring continuous learning"}, "optimisticScenario": {"type": "string", "description": "Optimistic career scenario", "example": "Leading a development team within 5 years"}, "expectedPath": {"type": "string", "description": "Expected career progression", "example": "Steady growth with focus on technical expertise"}}, "required": ["steps", "futureChall<PERSON>es", "optimistic<PERSON><PERSON><PERSON><PERSON>", "expectedPath"]}, "CareerViewResponseDto": {"type": "object", "properties": {"role": {"type": "string", "description": "Job role being analyzed", "example": "Software Developer"}, "skills": {"description": "Skills assessment", "type": "array", "items": {"$ref": "#/components/schemas/CareerSkillAssessment"}}, "transferableSkills": {"description": "Transferable skills analysis", "type": "array", "items": {"$ref": "#/components/schemas/TransferableSkill"}}, "careerPath": {"description": "Career progression path", "allOf": [{"$ref": "#/components/schemas/CareerProgressionPath"}]}}, "required": ["role", "skills", "transferableSkills", "careerPath"]}, "CreateProgramDto": {"type": "object", "properties": {}}, "CompetenceWithProgramDto": {"type": "object", "properties": {}}, "CreateCompetenceDto": {"type": "object", "properties": {}}, "CompetenceDto": {"type": "object", "properties": {}}, "SelectUserCompetencesDto": {"type": "object", "properties": {}}, "KeywordsData": {"type": "object", "properties": {"keywords": {"description": "List of keywords", "type": "array", "items": {"type": "string"}}, "skill_contexts": {"description": "List of skill contexts", "type": "array", "items": {"type": "string"}}, "languages": {"description": "List of Languages the user speaks", "type": "array", "items": {"type": "string"}}}, "required": ["keywords", "skill_contexts", "languages"]}, "PaginatedSearchDto": {"type": "object", "properties": {"smartProfile": {"description": "Smart profile with keywords and skill contexts", "example": {"keywords": ["javascript", "react", "typescript"], "languages": ["English", "FI"], "skill_contexts": ["web development", "frontend"]}, "allOf": [{"$ref": "#/components/schemas/KeywordsData"}]}, "query": {"type": "string", "description": "Search query text"}, "page": {"type": "number", "description": "Page number for pagination (1-based)", "default": 1, "minimum": 1}, "limit": {"type": "number", "description": "Number of items per page", "default": 10, "minimum": 1, "maximum": 50}, "offset": {"type": "number", "description": "Starting offset for pagination"}, "sort_by": {"type": "string", "description": "Field to sort by"}, "sort_direction": {"type": "string", "description": "Sort direction", "enum": ["asc", "desc"], "default": "desc"}, "municipality_names": {"description": "Filter by municipality names", "type": "array", "items": {"type": "string"}}, "municipality_codes": {"description": "Filter by municipality codes", "type": "array", "items": {"type": "string"}}, "region_names": {"description": "Filter by region names", "type": "array", "items": {"type": "string"}}, "region_codes": {"description": "Filter by region codes", "type": "array", "items": {"type": "string"}}, "country": {"description": "Filter by country codes/names", "type": "array", "items": {"type": "string"}}, "user_id": {"type": "string", "description": "User ID for tracking and personalization"}, "useCache": {"type": "boolean", "description": "Whether to use cached results if available", "default": true}}, "required": ["smartProfile"]}, "SmartProfileSearchDto": {"type": "object", "properties": {"smartProfile": {"description": "Smart profile with keywords and skill contexts", "example": {"keywords": ["javascript", "react", "typescript"], "languages": ["English", "FI"], "skill_contexts": ["web development", "frontend"]}, "allOf": [{"$ref": "#/components/schemas/KeywordsData"}]}, "query": {"type": "string", "description": "Search query text"}, "page": {"type": "number", "description": "Page number for pagination", "default": 1}, "limit": {"type": "number", "description": "Number of items per page", "default": 20}, "offset": {"type": "number", "description": "Starting offset for pagination"}, "sort_by": {"type": "string", "description": "Field to sort by"}, "sort_direction": {"type": "string", "description": "Sort direction", "enum": ["asc", "desc"], "default": "desc"}, "municipality_names": {"description": "Filter by municipality names", "type": "array", "items": {"type": "string"}}, "municipality_codes": {"description": "Filter by municipality codes", "type": "array", "items": {"type": "string"}}, "region_names": {"description": "Filter by region names", "type": "array", "items": {"type": "string"}}, "region_codes": {"description": "Filter by region codes", "type": "array", "items": {"type": "string"}}, "country": {"description": "Filter by country codes/names", "type": "array", "items": {"type": "string"}}}, "required": ["smartProfile"]}, "JobUserApplicationsDto": {"type": "object", "properties": {"smartProfile": {"description": "Smart profile with keywords and skill contexts", "example": {"keywords": ["javascript", "react", "typescript"], "languages": ["English", "FI"], "skill_contexts": ["web development", "frontend"]}, "allOf": [{"$ref": "#/components/schemas/KeywordsData"}]}, "query": {"type": "string", "description": "Search query text"}, "page": {"type": "number", "description": "Page number for pagination", "default": 1}, "limit": {"type": "number", "description": "Number of items per page", "default": 20}, "offset": {"type": "number", "description": "Starting offset for pagination"}, "sort_by": {"type": "string", "description": "Field to sort by"}, "sort_direction": {"type": "string", "description": "Sort direction", "enum": ["asc", "desc"], "default": "desc"}, "municipality_names": {"description": "Filter by municipality names", "type": "array", "items": {"type": "string"}}, "municipality_codes": {"description": "Filter by municipality codes", "type": "array", "items": {"type": "string"}}, "region_names": {"description": "Filter by region names", "type": "array", "items": {"type": "string"}}, "region_codes": {"description": "Filter by region codes", "type": "array", "items": {"type": "string"}}, "country": {"description": "Filter by country codes/names", "type": "array", "items": {"type": "string"}}}, "required": ["smartProfile"]}, "UserIdDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID for which to generate or retrieve recommendations", "example": "user-123456"}, "limit": {"type": "number", "description": "Maximum number of recommendations to generate", "example": 10}, "smartProfile": {"type": "object", "description": "Smart profile data for the user, used to generate personalized recommendations"}}, "required": ["userId"]}, "JobViewDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID of the user viewing the job", "example": "user-123456"}, "extId": {"type": "string", "description": "ID of the job being viewed", "example": 12345}}, "required": ["userId", "extId"]}, "JobRatingDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID of the user rating the job", "example": "user-123456"}, "extId": {"type": "string", "description": "ID of the job being rated", "example": 12345}, "isLiked": {"type": "boolean", "description": "Whether the job is liked (true) or disliked (false)", "example": true}}, "required": ["userId", "extId", "isLiked"]}, "ClassifyJobDto": {"type": "object", "properties": {"match": {"type": "string", "description": "Matching score between the job and the user profile"}, "mySkills": {"description": "Skills from user profile that match the job requirements", "type": "array", "items": {"type": "string"}}, "missingSkills": {"description": "Skills required by the job but missing in user profile", "type": "array", "items": {"type": "string"}}, "header": {"type": "string", "description": "AI note about the job"}}, "required": ["match", "mySkills", "missingSkills", "header"]}, "JobClassificationDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "ID of the user", "example": "user-123456"}, "extId": {"type": "string", "description": "ID of the job", "example": 12345}, "classificationData": {"description": "Classification data for the job", "allOf": [{"$ref": "#/components/schemas/ClassifyJobDto"}]}}, "required": ["userId", "extId", "classificationData"]}, "CvSectionDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Unique identifier for the section", "example": "experience"}, "name": {"type": "string", "description": "Display name for the section", "example": "Work Experience"}, "isRequired": {"type": "boolean", "description": "Indicates if the section is required", "example": true}, "isEnabled": {"type": "boolean", "description": "Indicates if the section is enabled", "example": true}, "order": {"type": "number", "description": "Order of the section in the document", "example": 2}}, "required": ["id", "name", "isRequired", "isEnabled", "order"]}, "KeyFitPointDto": {"type": "object", "properties": {"description": {"type": "string", "description": "Description of the fit point", "example": "Your experience with React matches the job requirements"}}, "required": ["description"]}, "JobApplicationDto": {"type": "object", "properties": {"userId": {"type": "string", "description": "User ID from Supertoken", "example": "9f8d7c6b-5432-1a0b-9c8d-7654321fedcb"}, "cvHtml": {"type": "string", "description": "HTML content of the generated CV", "example": "<div>CV content</div>"}, "cvUrl": {"type": "string", "description": "URL to the generated CV PDF", "example": "https://storage.googleapis.com/cvs/generated-cv-123.pdf"}, "jobExtId": {"type": "string", "description": "External ID of the job being applied to", "example": "013c9f4a-54ea-4b65-9e97-1fbf1c4a4606"}, "selectedLanguage": {"type": "string", "description": "Selected language for the CV", "example": "en"}, "language": {"type": "string", "description": "Normalized language name (Finnish, Swedish, English)", "example": "English"}, "documentType": {"type": "string", "description": "Type of document generated", "example": "CV_ONLY"}, "selectedTemplate": {"type": "string", "description": "Selected CV template", "example": "Modern Minimal"}, "sections": {"description": "Sections included in the CV", "type": "array", "items": {"$ref": "#/components/schemas/CvSectionDto"}}, "generatedAboutMe": {"type": "string", "description": "AI-generated About Me section content", "example": "I am a passionate software developer with over 5 years of experience..."}, "keyFitPoints": {"description": "Key points highlighting fit with job position", "type": "array", "items": {"$ref": "#/components/schemas/KeyFitPointDto"}}}, "required": ["userId", "cvHtml", "jobExtId", "selectedLanguage", "documentType", "selectedTemplate", "sections"]}, "JobApplicationResponseDto": {"type": "object", "properties": {"id": {"type": "string", "description": "Job application ID", "example": "9f8d7c6b-5432-1a0b-9c8d-7654321fedcb"}, "userId": {"type": "string", "description": "User ID from Supertoken", "example": "9f8d7c6b-5432-1a0b-9c8d-7654321fedcb"}, "cvUrl": {"type": "string", "description": "URL to the generated CV PDF", "example": "https://storage.googleapis.com/cvs/generated-cv-123.pdf"}, "jobExtId": {"type": "string", "description": "External ID of the job being applied to", "example": "013c9f4a-54ea-4b65-9e97-1fbf1c4a4606"}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation timestamp", "example": "2025-03-25T10:13:29.000Z"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update timestamp", "example": "2025-03-25T10:13:29.000Z"}}, "required": ["id", "userId", "cvUrl", "jobExtId", "createdAt", "updatedAt"]}, "ForYouStatusDataDto": {"type": "object", "properties": {"hasRecommendations": {"type": "boolean", "description": "Whether the user has any For You recommendations", "example": true}, "inProgress": {"type": "boolean", "description": "Whether For You recommendations generation is currently in progress", "example": false}, "needsRefresh": {"type": "boolean", "description": "Whether recommendations should be refreshed", "example": false}, "processStatus": {"type": "string", "description": "Current status of the For You process", "nullable": true, "enum": ["pending", "in_progress", "completed", "failed"], "example": "completed"}, "lastUpdated": {"format": "date-time", "type": "string", "description": "When the recommendations were last updated", "nullable": true, "example": "2025-04-14T12:00:00.000Z"}}, "required": ["hasRecommendations", "inProgress", "needsRefresh", "processStatus", "lastUpdated"]}, "ForYouStatusDto": {"type": "object", "properties": {"latestJobUploadTimestamp": {"format": "date-time", "type": "string", "description": "Timestamp of the last job upload to Pinecone", "nullable": true, "example": "2025-04-14T12:00:00.000Z"}, "hasGeneratedForYouToday": {"type": "boolean", "description": "Whether 'For You' recommendations were generated for the user today or are being generated", "example": true}, "success": {"type": "boolean", "description": "Whether the operation was successful", "example": true}, "data": {"description": "Response data", "allOf": [{"$ref": "#/components/schemas/ForYouStatusDataDto"}]}, "message": {"type": "string", "description": "Response message", "example": "ForYou status retrieved successfully"}, "timestamp": {"type": "string", "description": "Timestamp of the response", "example": "2025-04-14T12:00:00.000Z"}}, "required": ["latestJobUploadTimestamp", "hasGeneratedForYouToday", "success", "data", "message", "timestamp"]}, "SavedJobResponseDto": {"type": "object", "properties": {"id": {"type": "number", "description": "Saved job DB ID", "example": 1}, "userId": {"type": "string", "description": "User ID", "example": "9f8d7c6b-5432-1a0b-9c8d-7654321fedcb"}, "jobExtId": {"type": "string", "description": "External job ID", "example": "013c9f4a-54ea-4b65-9e97-1fbf1c4a4606"}, "createdAt": {"format": "date-time", "type": "string", "description": "Creation timestamp"}, "updatedAt": {"format": "date-time", "type": "string", "description": "Last update timestamp"}}, "required": ["id", "userId", "jobExtId", "createdAt", "updatedAt"]}, "TranslateTextDto": {"type": "object", "properties": {}}, "TranslationResponseDto": {"type": "object", "properties": {}}, "BatchTranslateDto": {"type": "object", "properties": {}}, "BatchTranslationResponseDto": {"type": "object", "properties": {}}, "Employer": {"type": "object", "properties": {"type": {"type": "string", "enum": ["01", "02"]}, "business_id": {"type": "object"}, "name": {"type": "string"}, "language_code": {"type": "string"}, "website": {"type": "string"}, "email": {"type": "string"}, "phone": {"type": "string"}, "logoUrl": {"type": "string"}, "businessInfoUrl": {"type": "string"}, "company_type": {"type": "string", "enum": ["startup", "scaleup", "enterprise", "sme", "other"]}, "company_size": {"type": "string", "enum": ["micro", "small", "medium", "large"]}, "industry": {"type": "string"}, "description": {"type": "string"}, "logo_url": {"type": "string"}, "social_media": {"type": "object"}}, "required": ["type", "name", "language_code"]}, "MatchedJobDto": {"type": "object", "properties": {"match": {"type": "string", "description": "Matching score between the job and the user profile"}, "mySkills": {"description": "Skills from user profile that match the job requirements", "type": "array", "items": {"type": "string"}}, "missingSkills": {"description": "Skills required by the job but missing in user profile", "type": "array", "items": {"type": "string"}}, "header": {"type": "string", "description": "AI note about the job"}, "extId": {"type": "string", "description": "External ID of the job"}, "score": {"type": "number", "description": "Matching score between the job and the user profile"}}, "required": ["match", "mySkills", "missingSkills", "header", "extId", "score"]}, "MatchedJobsResponseDto": {"type": "object", "properties": {"jobs": {"description": "List of matched jobs with skill information", "type": "array", "items": {"$ref": "#/components/schemas/MatchedJobDto"}}, "meta": {"type": "object", "description": "Metadata for pagination", "properties": {"total": {"type": "number", "description": "Total number of matched jobs"}, "page": {"type": "number", "description": "Current page number"}, "limit": {"type": "number", "description": "Number of items per page"}, "has_next": {"type": "boolean", "description": "Whether there are more pages"}}}}, "required": ["jobs", "meta"]}, "KeywordDataDto": {"type": "object", "properties": {"keywords": {"description": "List of keywords associated with the user profile", "example": ["typescript", "react", "<PERSON><PERSON><PERSON>"], "type": "array", "items": {"type": "string"}}, "skill_contexts": {"description": "List of skill contexts associated with the keywords", "example": ["web development", "frontend", "backend"], "type": "array", "items": {"type": "string"}}}, "required": ["keywords", "skill_contexts"]}, "KeywordsResponseDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates if the operation was successful", "example": true}, "data": {"description": "The keywords data", "allOf": [{"$ref": "#/components/schemas/KeywordDataDto"}]}, "message": {"type": "string", "description": "Message describing the result of the operation", "example": "Keywords data generated successfully"}, "timestamp": {"type": "string", "description": "Timestamp when the response was generated", "example": "2025-03-19T00:48:39+02:00"}}, "required": ["success", "data", "message", "timestamp"]}, "TrackSearchDto": {"type": "object", "properties": {}}, "TrackJobViewDto": {"type": "object", "properties": {}}, "TrackJobSaveDto": {"type": "object", "properties": {}}, "TrackJobApplyDto": {"type": "object", "properties": {}}, "TrackRecommendationsShownDto": {"type": "object", "properties": {}}, "TrackRecommendationClickedDto": {"type": "object", "properties": {}}, "UserMetricsResponseDto": {"type": "object", "properties": {}}, "OrgMetricsResponseDto": {"type": "object", "properties": {}}, "Municipality": {"type": "object", "properties": {}}, "Region": {"type": "object", "properties": {}}, "AugmentRequestDto": {"type": "object", "properties": {"reqType": {"type": "string", "description": "The type of augmentation request", "enum": ["ABOUT_ME", "RELEVANCE", "CV", "CL", "FIT"], "example": "ABOUT_ME"}, "data": {"type": "object", "description": "The data to be augmented", "example": {"experience": "I have worked as a software developer for 5 years.", "skills": ["JavaScript", "TypeScript", "React", "Node.js"]}}}, "required": ["reqType", "data"]}, "AugmentResponseDto": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Indicates whether the operation was successful", "example": true}, "data": {"type": "object", "description": "The augmented data", "example": {"augmentedText": "I am a seasoned software developer with 5 years of experience...", "suggestions": ["Add more details about your React projects", "Highlight your leadership experience"]}}, "message": {"type": "string", "description": "A descriptive message about the operation", "example": "CV content successfully augmented"}, "timestamp": {"type": "string", "description": "The timestamp of the response", "example": "2025-03-24T03:19:28.000Z"}}, "required": ["success", "data", "message", "timestamp"]}}}}