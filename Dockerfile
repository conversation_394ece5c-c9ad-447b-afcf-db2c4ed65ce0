# Use the Node official image
FROM node:lts

# Create and change to the app directory
WORKDIR /app

# Copy package files first for better caching
COPY package*.json ./

# Install packages
RUN npm ci

# Copy local code to the container image
COPY . ./

# Copy the research programs
COPY research_programs ./dist/research_programs

# Make the shell script executable
RUN chmod +x docker-entrypoint.sh

# Expose the port
EXPOSE 3000

# Use the shell script as entrypoint
CMD ["./docker-entrypoint.sh"]
