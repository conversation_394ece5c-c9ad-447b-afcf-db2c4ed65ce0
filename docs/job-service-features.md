# Job Service Backend Features Report

## Executive Summary

This document provides a comprehensive overview of the NestJS-based job service backend features, excluding React app and educational recommendation components. The service implements a sophisticated job search platform with AI-powered features, multi-source job aggregation, and personalized recommendations.

## Architecture Overview

### Technology Stack
- **Framework**: NestJS v11 with TypeScript
- **Databases**: PostgreSQL (primary), Redis (caching), MongoDB (smart profiles)
- **AI Services**: OpenAI (embeddings), Gemini (metadata extraction), Pinecone (vector search), Cohere (reranking)
- **External APIs**: Tyomarkkinatori (Finnish job market), Jobly, DeepL (translations)
- **Authentication**: SuperTokens for session management

### Key Design Patterns
- Domain-driven modular architecture
- Repository pattern for data access
- DTO-based request/response handling
- Interceptor-based cross-cutting concerns
- Multi-level caching strategy

## Core Job Search Features

### 1. Hybrid Search System

The platform implements a sophisticated hybrid search combining:

#### Semantic Vector Search
- Uses OpenAI embeddings (3072 dimensions)
- Searches against job embeddings in Pinecone
- Enables meaning-based job discovery beyond keyword matching

#### Metadata Filtering
- Location filters (municipality, region, country)
- Professional filters (occupation, industry, skills, seniority)
- Employment filters (working time, continuity, flexibility)
- Real-time expiry filtering

#### AI-Powered Reranking
- Cohere Rerank 3.5 model for result refinement
- Reranks based on user skill contexts
- Weighted scoring: 30% search + 70% rerank score

### 2. Search Endpoints

| Endpoint | Method | Purpose |
|----------|--------|---------|
| `/jobs/search` | POST | Main search with filters and pagination |
| `/jobs/top` | POST | Top matches based on user profile |
| `/jobs/{extId}` | GET | Single job details |
| `/jobs/for-you` | POST | Personalized recommendations |
| `/for-you/generate` | POST | Generate new recommendations |

### 3. Advanced Pagination

#### Implementation
- Page-based and offset-based pagination support
- Default: 10 items/page, max 50
- Metadata includes total count and navigation flags

#### Performance Optimization
- Fetches up to 200 results on first request
- Caches full result set per user (30 min TTL)
- Subsequent pages served from cache
- Preserves ranking scores across pages

### 4. Response Formats

#### List View (SimplifiedJobDataDto)
- Basic job info: id, title, employer, location
- Employment details: type, hours, continuity
- Timing: published date, expiry, new/closing flags
- Skills and language requirements
- Excludes detailed summaries for performance

#### Detail View (EnrichedJobDataDto)
- All list view fields
- Market salary data
- AI-generated summaries
- Evolution/career path data
- User interaction status (hasApplied)

## User Interaction Features

### 1. Job Applications

#### Endpoints
- `POST /v1/job-applications` - Submit application
- `GET /v1/job-applications?userId={id}` - List applications
- `GET /v1/job-applications/{id}` - Get specific application

#### Application Data Storage
- Full CV HTML content
- PDF URL reference
- Selected language and template
- CV sections (JSONB format)
- AI-generated about me text
- Key fit points analysis

### 2. Saved Jobs Management

#### Features
- Save/unsave jobs with duplicate prevention
- Retrieve saved jobs ordered by recency
- Unique constraint on userId + job combination
- Integrated analytics tracking

#### Endpoints
- `POST /v1/saved-jobs/{extId}` - Save job
- `GET /v1/saved-jobs` - List saved jobs
- `DELETE /v1/saved-jobs/{extId}` - Remove saved job

### 3. User Action Analytics

#### Tracked Actions
- **Search**: Query, filters, result count
- **View**: Source, duration, correlation ID
- **Save/Unsave**: With source tracking
- **Apply**: Application details
- **Recommendations**: Show/click events

#### Implementation
- Non-blocking tracking (errors don't affect UX)
- Correlation IDs for action tracing
- Flexible JSONB metadata storage
- Source attribution (search, for-you, browse, email)

## Data Management Features

### 1. Multi-Source Job Import

#### Tyomarkkinatori Integration
- OAuth2 authentication
- Bilingual support (Finnish/English)
- Category and employer type filtering
- Configurable pagination and rate limiting

#### Jobly Integration
- REST API with JSON feed
- Hourly scheduled imports (cron)
- Import locking to prevent concurrency
- Automatic job expiry for missing items
- Finnish content auto-translation

### 2. Employer Management

#### Flexible Matching Strategy
- Primary: Business ID (format: 1234567-8)
- Fallback: Name + Source combination
- Source tracking in description field
- Support for companies without business IDs

#### Data Model
- Multiple employer types (Company, Municipality, etc.)
- Optional contact information
- Company metadata (size, industry)
- Social media links (JSON storage)

### 3. Job Processing Pipeline

#### Processing Stages
1. **Import**: Raw data from external APIs
2. **Translation**: Multi-language content generation
3. **Employer Resolution**: Create/update employer records
4. **Location Mapping**: Municipality/region code resolution
5. **AI Enhancement**: Gemini metadata extraction
6. **Classification**: External Dify workflow processing
7. **Vector Indexing**: Pinecone upload for search

#### Batch Processing
- CSV export for external processing
- Configurable batch sizes (default 1000)
- Concurrent processing support
- Retry logic and failure tracking
- Status updates via callback endpoint

### 4. Job Lifecycle Management

#### Creation/Updates
- Upsert based on external ID
- Source tracking (Jobly/Tyomarkkinatori)
- Import date tracking for freshness
- Original publication date preservation

#### Expiry Handling
- Time-based: expires_at field
- Feed-based: Missing from import = expired
- Batch expiry updates before processing
- Source-specific expiry logic

## Supporting Systems

### 1. Translation System

#### Architecture
- DeepL API integration
- Database-cached translations
- Finnish and Swedish support
- English as source language

#### Implementation
- `@TranslateResponse()` decorator
- Accept-Language header detection
- Recursive object translation
- Batch processing for arrays
- SHA-256 hash-based caching

### 2. Caching Strategy

#### Multi-Level Cache
1. **Standard Result Cache**
   - MD5 hash of search parameters
   - 15 min TTL (5 min for empty)
   
2. **User-Specific Full Cache**
   - Complete result sets (up to 200)
   - Score preservation
   - 30 min TTL

3. **Translation Cache**
   - Database-persisted
   - Hash-based lookup
   - Permanent storage

### 3. AI Integration

#### OpenAI
- Embedding generation for semantic search
- 3072-dimensional vectors
- Batch processing support

#### Gemini
- Metadata extraction from job descriptions
- Confidence-based field updates
- Extracts: URLs, dates, flexibility, requirements

#### Pinecone
- Vector similarity search
- Metadata filtering
- Real-time index updates

#### Cohere
- Result reranking
- Multi-context scoring
- Relevance optimization

## API Design Patterns

### Authentication
- SuperTokens session management
- `@UseGuards(AuthGuard)` protection
- Session data via decorators

### Validation
- Zod + class-validator
- DTO-based validation
- Global exception handling

### Documentation
- Auto-generated Swagger/OpenAPI
- Comprehensive endpoint descriptions
- Request/response examples

### Error Handling
- Centralized exception filter
- Structured error responses
- Correlation ID tracking

## Performance Optimizations

1. **Parallel Processing**
   - Concurrent API calls
   - Batch database operations
   - Async job processing

2. **Efficient Queries**
   - Indexed database fields
   - Optimized joins
   - Query result limits

3. **Resource Management**
   - Connection pooling
   - Rate limiting
   - Memory-efficient streaming

## Key Integration Points for Frontend

### Required Headers
- `Authorization`: Bearer token for authenticated endpoints
- `Accept-Language`: For translation (fi, sv, en)
- `X-User-Id`: For personalization (alternative to session)

### Pagination Parameters
- `page`: 1-based page number
- `limit`: Items per page (max 50)
- `offset`: Alternative to page

### Search Filters
- Text query with semantic understanding
- Location hierarchy (country → region → municipality)
- Professional classifications
- Skill matching
- User profile integration

### Response Handling
- Consistent DTO structures
- Metadata for pagination
- Error format standardization
- Translation support

## Recommendations for Frontend Implementation

1. **Search Implementation**
   - Implement debouncing for search queries
   - Cache search results client-side
   - Preload next page for smooth pagination
   - Handle empty states gracefully

2. **User Experience**
   - Show loading states during AI processing
   - Implement optimistic UI for save/unsave
   - Display relative timestamps
   - Highlight new and closing-soon jobs

3. **Performance**
   - Use pagination metadata to prevent unnecessary requests
   - Leverage browser caching for static data
   - Implement virtual scrolling for large lists
   - Lazy load detailed job information

4. **Error Handling**
   - Implement retry logic for failed requests
   - Show user-friendly error messages
   - Log errors with correlation IDs
   - Handle offline scenarios

5. **Personalization**
   - Store user preferences locally
   - Sync saved jobs and applications
   - Track user interactions for analytics
   - Implement smart profile management

## Conclusion

This job service backend provides a comprehensive platform for job search and management with advanced AI features, multi-source aggregation, and robust performance optimizations. The modular architecture and well-defined APIs enable straightforward frontend integration while maintaining flexibility for future enhancements.