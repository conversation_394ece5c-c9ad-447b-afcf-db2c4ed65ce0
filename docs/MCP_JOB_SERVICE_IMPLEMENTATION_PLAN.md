# MCP Server Implementation Plan for NeduAI Job Service

## Executive Summary

This plan outlines the implementation of an MCP (Model Context Protocol) Server within the Job Service microservice to enable AI Agents to perform job-related operations on behalf of users. The MCP Server will run within the same Google Cloud Run instance using Streamable HTTP transport protocol and provide structured tools for AI Agents to search jobs, manage applications, handle saved jobs, and interact with the job recommendation system.

## Architecture Decision: API-Based Approach

### Recommended Approach: **API Integration over Direct Database Access**

**Rationale:**
1. **Leverages Existing Business Logic**: Complex search algorithms, AI reranking, and caching are already implemented
2. **Security**: Maintains authentication/authorization through SuperTokens
3. **Audit Trail**: User actions are automatically tracked through existing UserActionService
4. **Consistency**: Ensures all operations go through the same validation and processing pipeline
5. **Performance**: Utilizes existing caching layers and optimizations
6. **Cloud Run Compatible**: API calls work seamlessly with Streamable HTTP transport

### Authentication Strategy: Service Account with User Context

1. **Service Account Creation**:
   - Create dedicated service account for AI Agent with SYSTEM role
   - Store credentials securely in Google Secret Manager
   
2. **User Context Flow**:
   - AI Agent authenticates as service account
   - Includes user context in every tool call
   - Modified auth middleware to validate SYSTEM role operations
   - All actions logged with both service account and user context

## Framework Selection: Official MCP SDK

### Recommended: **@modelcontextprotocol/sdk** (Official TypeScript SDK)

**Rationale:**
1. **Official Support**: Maintained by Anthropic with active development
2. **TypeScript Native**: Perfect fit for NestJS/TypeScript codebase
3. **Minimal Overhead**: Ideal for wrapping existing job service APIs
4. **Strong Type Safety**: Comprehensive TypeScript definitions
5. **Streamable HTTP Support**: Native support for Cloud Run's infrastructure

## Transport Protocol: Streamable HTTP

### Decision: **Streamable HTTP Protocol**

**Rationale:**
1. **Cloud Run Optimized**: Perfect for Google Cloud Run's HTTP-based infrastructure
2. **Stateless Option**: Can operate in both stateless HTTP and streaming modes
3. **Performance**: Efficient for job search operations that may return large result sets
4. **SDK Native**: Built-in support in the official SDK

**Implementation Details:**
- Endpoint: `/mcp` for Streamable HTTP connections
- Configure Cloud Run's request timeout for long-running operations
- Implement automatic reconnection with exponential backoff

## MCP Tools Inventory

### Tool Definition Pattern

Every tool follows this pattern where `context` is always the first required parameter:

```typescript
{
  name: 'tool_name',
  description: 'Tool description',
  inputSchema: {
    type: 'object',
    properties: {
      context: {
        type: 'object',
        properties: {
          userId: { type: 'string', description: 'Target user ID' },
          requestId: { type: 'string', description: 'Request correlation ID' },
          source: { type: 'string', description: 'Origin system' },
          reason: { type: 'string', description: 'Action reason' }
        },
        required: ['userId', 'requestId', 'source', 'reason']
      },
      // ... other tool-specific parameters
    },
    required: ['context', /* other required params */]
  }
}
```

### 1. Job Search Tools

#### Tool: `job_search`
- **Purpose**: Search jobs with filters and AI-powered ranking
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `query` (optional): search text
  - `filters` (optional): location, occupation, skills, employment type, etc.
  - `page` (optional): pagination page number
  - `limit` (optional): results per page (max 50)
- **Maps to**: POST /jobs/search

#### Tool: `job_get_details`
- **Purpose**: Get detailed information about a specific job
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): external job ID
- **Maps to**: GET /jobs/:extId

#### Tool: `job_search_top_matches`
- **Purpose**: Get top job matches based on user profile
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `limit` (optional): number of results
- **Maps to**: POST /jobs/top

### 2. Saved Jobs Management Tools

#### Tool: `saved_jobs_list`
- **Purpose**: Get user's saved jobs
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `page` (optional): pagination page
  - `limit` (optional): items per page
- **Maps to**: GET /v1/saved-jobs

#### Tool: `saved_job_add`
- **Purpose**: Save a job for later
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): external job ID to save
- **Maps to**: POST /v1/saved-jobs/:extId

#### Tool: `saved_job_remove`
- **Purpose**: Remove a saved job
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): external job ID to remove
- **Maps to**: DELETE /v1/saved-jobs/:extId

### 3. Job Application Tools

#### Tool: `job_applications_list`
- **Purpose**: Get user's job applications
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `page` (optional): pagination page
  - `limit` (optional): items per page
- **Maps to**: GET /v1/job-applications?userId={userId}

#### Tool: `job_application_submit`
- **Purpose**: Submit a job application
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): external job ID
  - `cvHtml` (required): CV content in HTML
  - `pdfUrl` (optional): PDF version URL
  - `language` (required): CV language
  - `template` (required): CV template used
  - `aboutMe` (optional): AI-generated about section
  - `keyFitPoints` (optional): fit analysis
- **Maps to**: POST /v1/job-applications

#### Tool: `job_application_get`
- **Purpose**: Get specific application details
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `applicationId` (required): application ID
- **Maps to**: GET /v1/job-applications/:id

### 4. For You (Recommendations) Tools

#### Tool: `for_you_jobs_list`
- **Purpose**: Get personalized job recommendations
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `status` (optional): filter by status
  - `page` (optional): pagination page
  - `limit` (optional): items per page
- **Maps to**: POST /jobs/for-you

#### Tool: `for_you_jobs_generate`
- **Purpose**: Generate new personalized recommendations
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `limit` (optional): number to generate
- **Maps to**: POST /for-you/generate

#### Tool: `for_you_job_show`
- **Purpose**: Mark recommendation as shown to user
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): job ID
- **Maps to**: PUT /for-you/:id/show

#### Tool: `for_you_job_click`
- **Purpose**: Mark recommendation as clicked
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): job ID
- **Maps to**: PUT /for-you/:id/click

### 5. CV Workspace Tools

#### Tool: `cv_generate`
- **Purpose**: Generate CV for a job application
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): target job ID
  - `language` (required): CV language
  - `template` (required): CV template
  - `sections` (optional): specific sections to include
- **Maps to**: POST /cv-workspace/generate

#### Tool: `cv_augment`
- **Purpose**: Enhance CV with AI suggestions
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `jobId` (required): target job ID
  - `content` (required): current CV content
  - `sections` (optional): sections to augment
- **Maps to**: POST /cv-workspace/augment

### 6. Analytics Tools

#### Tool: `user_metrics_get`
- **Purpose**: Get user's job search metrics
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `timeRange` (optional): period for metrics
- **Maps to**: GET /v1/users/:userId/metrics

#### Tool: `user_action_record`
- **Purpose**: Record user interaction with jobs
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `action` (required): action type (search, view, save, apply)
  - `metadata` (required): action-specific data
- **Maps to**: POST /v1/track

### 7. Location & Filter Tools

#### Tool: `locations_search`
- **Purpose**: Search for locations (municipalities, regions)
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `query` (required): location search term
  - `type` (optional): municipality or region
- **Maps to**: GET /locations/search

#### Tool: `occupations_list`
- **Purpose**: Get available occupations for filtering
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
  - `language` (optional): fi, sv, or en
- **Maps to**: GET /api/occupations

#### Tool: `industries_list`
- **Purpose**: Get available industries
- **Parameters**: 
  - `context` (required): `{ userId, requestId, source, reason }`
- **Maps to**: GET /api/industries

## Implementation Components

### 1. MCP Server Module Structure
```
src/mcp/
├── mcp.module.ts              # NestJS module definition
├── mcp.service.ts             # Core MCP server logic
├── auth/
│   ├── mcp-auth.guard.ts      # Authentication guard
│   └── context.service.ts      # User context validation
├── tools/
│   ├── job-search.tools.ts     # Search-related tools
│   ├── saved-jobs.tools.ts     # Saved jobs tools
│   ├── applications.tools.ts   # Application tools
│   ├── for-you.tools.ts        # Recommendation tools
│   ├── cv-workspace.tools.ts   # CV generation tools
│   ├── analytics.tools.ts      # Metrics and tracking tools
│   └── metadata.tools.ts       # Locations, occupations, etc.
├── middleware/
│   ├── rate-limit.middleware.ts # Rate limiting
│   └── audit.middleware.ts      # Enhanced audit logging
├── transport/
│   ├── http.adapter.ts         # Streamable HTTP adapter
│   └── mcp.controller.ts       # MCP endpoint controller
└── config/
    └── mcp.config.ts           # MCP configuration
```

### 2. Authentication Flow Implementation

#### Service Account Setup

1. **Create Service Account**:
   - Email: `<EMAIL>`
   - Role: `SYSTEM` with job service permissions
   - Long-lived refresh token (30-day expiry)

2. **Permission Scope**:
   - Read access to all job data
   - Write access to user-specific data (saved jobs, applications)
   - Analytics recording permissions
   - No access to modify job listings

#### Request Flow

1. **AI Agent → MCP Server**:
   ```typescript
   const transport = new HttpClientTransport({
     url: 'https://job-svc.neduai.com/mcp',
     headers: {
       'Authorization': `Bearer ${serviceToken}`,
       'X-Service-Account': 'job-agent'
     }
   });
   ```

2. **Tool Execution**:
   ```javascript
   await mcpClient.callTool('job_search', {
     context: {
       userId: '12345',
       requestId: 'req_xyz789',
       source: 'dify_agent',
       reason: 'User asked for software engineering jobs in Helsinki'
     },
     query: 'software engineer',
     filters: {
       location: { municipality: 'Helsinki' },
       skills: ['Python', 'React']
     }
   });
   ```

### 3. Audit Trail Implementation

Every MCP tool call is logged to UserActions:

```typescript
{
  userId: 12345,
  summary: "MCP Tool: job_search",
  actionType: "JOB_SEARCH",
  metadata: {
    toolName: "job_search",
    requestId: "req_xyz789",
    source: "dify_agent",
    reason: "User asked for software engineering jobs in Helsinki",
    serviceAccount: "<EMAIL>",
    arguments: {
      query: "software engineer",
      filters: { location: { municipality: "Helsinki" } }
    },
    results: {
      count: 42,
      cached: false
    }
  }
}
```

### 4. Error Handling Strategy

1. **Search Errors**: Return empty results with error message
2. **Rate Limit**: Include retry-after header
3. **Invalid Job ID**: Clear not found message
4. **Application Errors**: Detailed validation messages
5. **AI Service Errors**: Fallback to basic functionality

### 5. Performance Considerations

1. **Caching Strategy**:
   - Leverage existing Redis cache for search results
   - Cache location/occupation metadata
   - User-specific result caching

2. **Rate Limiting**:
   - Per-user: 60 searches/minute
   - Per-tool: Custom limits based on cost
   - AI operations: Stricter limits

3. **Batch Operations**:
   - Support bulk job detail fetching
   - Batch recommendation generation
   - Efficient pagination handling

## Deployment Strategy

### Phase 1: Foundation (Week 1-2)
1. Set up MCP module structure
2. Implement authentication flow
3. Create base tool framework
4. Add Streamable HTTP endpoint
5. Configure Cloud Run settings

### Phase 2: Core Search Tools (Week 3-4)
1. Implement job search tools
2. Add saved jobs management
3. Create basic analytics tools
4. Test with AI agents

### Phase 3: Application Tools (Week 5-6)
1. Implement application submission
2. Add CV workspace tools
3. Create recommendation tools
4. Add metadata query tools

### Phase 4: Optimization & Testing (Week 7-8)
1. Performance optimization
2. Comprehensive testing
3. Security audit
4. Documentation

## Monitoring & Observability

### Metrics to Track
- Tool usage frequency by type
- Search query patterns
- Application submission success rate
- Cache hit rates
- AI service latencies

### Dashboards
```sql
-- Top MCP operations
SELECT 
  metadata->>'toolName' as tool,
  COUNT(*) as calls,
  AVG((metadata->'duration')::int) as avg_ms
FROM user_actions
WHERE summary LIKE 'MCP Tool:%'
  AND created_at > NOW() - INTERVAL '24 hours'
GROUP BY 1
ORDER BY 2 DESC;

-- User engagement via MCP
SELECT 
  DATE(created_at) as date,
  COUNT(DISTINCT user_id) as unique_users,
  COUNT(*) as total_actions
FROM user_actions
WHERE metadata->>'source' = 'dify_agent'
GROUP BY 1
ORDER BY 1 DESC;
```

### Alerting
- High error rates (>5%)
- Slow response times (>2s)
- Rate limit violations
- Failed job applications
- AI service outages

## Security Considerations

### Data Access Control
1. **Read Permissions**:
   - Public job data accessible
   - User-specific data requires matching userId

2. **Write Permissions**:
   - Only user's own saved jobs
   - Only user's own applications
   - Validated analytics events

3. **Sensitive Data**:
   - No salary data in search results
   - No employer contact details
   - No other users' application data

### Input Validation
1. **Search Queries**: Sanitize and length limit
2. **Filters**: Validate against allowed values
3. **Pagination**: Enforce reasonable limits
4. **File Uploads**: Not supported via MCP

## Success Criteria

1. **Functional**:
   - All major job operations accessible
   - Successful AI agent integration
   - Complete audit trail

2. **Performance**:
   - Search response < 1s
   - Support 500+ concurrent agents
   - 99% uptime

3. **User Experience**:
   - Relevant search results
   - Accurate recommendations
   - Seamless application flow

## Future Enhancements

1. **Advanced Search**:
   - Natural language queries
   - Multi-criteria optimization
   - Saved search alerts

2. **Batch Operations**:
   - Bulk application status checks
   - Mass job detail fetching
   - Parallel search queries

3. **Intelligent Features**:
   - Application success prediction
   - Salary negotiation insights
   - Career path recommendations

4. **Integration Expansion**:
   - LinkedIn job import
   - Direct employer APIs
   - Application tracking

## Risk Mitigation

1. **Performance Impact**: 
   - Use existing caching layers
   - Implement request queuing
   - Monitor resource usage

2. **Data Consistency**:
   - Rely on existing validation
   - Use transactions where needed
   - Regular data audits

3. **AI Service Reliability**:
   - Implement circuit breakers
   - Fallback to basic search
   - Cache AI results

4. **Security Threats**:
   - Regular security audits
   - Penetration testing
   - Anomaly detection

## Example Implementation

### Tool Registration
```typescript
// src/mcp/tools/job-search.tools.ts
export const jobSearchTool = {
  name: 'job_search',
  description: 'Search for jobs with AI-powered ranking',
  inputSchema: {
    type: 'object',
    properties: {
      context: contextSchema,
      query: { 
        type: 'string', 
        description: 'Search query text' 
      },
      filters: {
        type: 'object',
        properties: {
          location: {
            type: 'object',
            properties: {
              country: { type: 'string' },
              region: { type: 'string' },
              municipality: { type: 'string' }
            }
          },
          occupation: { type: 'string' },
          skills: {
            type: 'array',
            items: { type: 'string' }
          },
          employmentType: {
            type: 'string',
            enum: ['FULL_TIME', 'PART_TIME', 'CONTRACT']
          }
        }
      },
      page: { 
        type: 'number', 
        minimum: 1 
      },
      limit: { 
        type: 'number', 
        minimum: 1, 
        maximum: 50 
      }
    },
    required: ['context']
  },
  handler: async (args: JobSearchArgs) => {
    // Validate context
    const context = validateContext(args.context);
    
    // Build search request
    const searchRequest = {
      userId: context.userId,
      search: args.query,
      ...args.filters,
      page: args.page || 1,
      limit: args.limit || 10
    };
    
    // Execute search
    const results = await jobSearchService.search(searchRequest);
    
    // Log action
    await userActionService.create(context.userId, {
      summary: 'MCP Tool: job_search',
      actionType: 'JOB_SEARCH',
      metadata: {
        toolName: 'job_search',
        ...context,
        arguments: sanitizeArgs(args),
        results: {
          count: results.total,
          page: results.page
        }
      }
    });
    
    return {
      content: [{
        type: 'text',
        text: JSON.stringify(results, null, 2)
      }]
    };
  }
};
```

This implementation plan provides a comprehensive roadmap for integrating MCP Server with the Job Service, enabling AI Agents to effectively help users with job search, applications, and career management while maintaining security, performance, and audit requirements.