# Phase 1 Quick Start: MCP Foundation Implementation

This guide helps you start implementing Phase 1 of the MCP Server immediately.

## Prerequisites

1. Install the MCP SDK:
```bash
npm install @modelcontextprotocol/sdk
```

2. Install additional dependencies:
```bash
npm install zod undici
```

## Step 1: Create Module Structure

Create the following directory structure:

```
src/mcp/
├── mcp.module.ts
├── mcp.service.ts
├── config/
│   └── mcp.config.ts
├── auth/
│   ├── mcp-auth.guard.ts
│   └── context.service.ts
├── transport/
│   ├── http.adapter.ts
│   └── mcp.controller.ts
├── tools/
│   └── base-tool.interface.ts
└── middleware/
    └── audit.middleware.ts
```

## Step 2: Basic Module Setup

### src/mcp/mcp.module.ts
```typescript
import { Module } from '@nestjs/common';
import { McpService } from './mcp.service';
import { McpController } from './transport/mcp.controller';
import { ContextService } from './auth/context.service';
import { ConfigModule } from '@nestjs/config';
import mcpConfig from './config/mcp.config';

@Module({
  imports: [
    ConfigModule.forFeature(mcpConfig),
  ],
  controllers: [McpController],
  providers: [
    McpService,
    ContextService,
  ],
  exports: [McpService],
})
export class McpModule {}
```

### src/mcp/config/mcp.config.ts
```typescript
import { registerAs } from '@nestjs/config';

export default registerAs('mcp', () => ({
  serviceAccount: {
    email: process.env.MCP_SERVICE_ACCOUNT_EMAIL || '<EMAIL>',
    token: process.env.MCP_SERVICE_ACCOUNT_TOKEN,
  },
  transport: {
    endpoint: '/mcp',
    timeout: 30000, // 30 seconds
  },
  rateLimit: {
    windowMs: 60000, // 1 minute
    maxRequests: 60,
  },
}));
```

## Step 3: Context Service

### src/mcp/auth/context.service.ts
```typescript
import { Injectable } from '@nestjs/common';
import { z } from 'zod';

export const contextSchema = z.object({
  userId: z.string().min(1),
  requestId: z.string().min(1),
  source: z.string().min(1),
  reason: z.string().min(1),
});

export type ToolContext = z.infer<typeof contextSchema>;

@Injectable()
export class ContextService {
  validateContext(context: unknown): ToolContext {
    return contextSchema.parse(context);
  }

  createAuditEntry(context: ToolContext, toolName: string, args: any, result: any) {
    return {
      userId: context.userId,
      summary: `MCP Tool: ${toolName}`,
      actionType: this.mapToolToActionType(toolName),
      metadata: {
        toolName,
        requestId: context.requestId,
        source: context.source,
        reason: context.reason,
        serviceAccount: '<EMAIL>',
        arguments: this.sanitizeArgs(args),
        result: this.sanitizeResult(result),
      },
    };
  }

  private mapToolToActionType(toolName: string): string {
    const mapping: Record<string, string> = {
      job_search: 'JOB_SEARCH',
      job_get_details: 'JOB_VIEW',
      saved_job_add: 'JOB_SAVE',
      saved_job_remove: 'JOB_UNSAVE',
      job_application_submit: 'JOB_APPLY',
    };
    return mapping[toolName] || 'MCP_TOOL_CALL';
  }

  private sanitizeArgs(args: any): any {
    // Remove sensitive data from arguments
    const { context, ...safeArgs } = args;
    return safeArgs;
  }

  private sanitizeResult(result: any): any {
    // Extract key metrics from result
    if (result?.data) {
      return {
        count: result.data.length,
        success: true,
      };
    }
    return { success: true };
  }
}
```

## Step 4: Base Tool Interface

### src/mcp/tools/base-tool.interface.ts
```typescript
import { z } from 'zod';
import { ToolContext } from '../auth/context.service';

export interface McpTool<T = any> {
  name: string;
  description: string;
  inputSchema: z.ZodSchema<T>;
  handler: (args: T, context: ToolContext) => Promise<McpToolResult>;
}

export interface McpToolResult {
  content: Array<{
    type: 'text' | 'image' | 'resource';
    text?: string;
    data?: any;
    mimeType?: string;
  }>;
}

export function createTool<T>(config: {
  name: string;
  description: string;
  inputSchema: z.ZodSchema<T>;
  handler: (args: T, context: ToolContext) => Promise<any>;
}): McpTool<T> {
  return {
    ...config,
    handler: async (args: T, context: ToolContext) => {
      const result = await config.handler(args, context);
      
      // Ensure result is in MCP format
      if (result.content) {
        return result;
      }
      
      // Convert to MCP format
      return {
        content: [{
          type: 'text',
          text: JSON.stringify(result, null, 2),
        }],
      };
    },
  };
}
```

## Step 5: MCP Service Core

### src/mcp/mcp.service.ts
```typescript
import { Injectable, Logger } from '@nestjs/common';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { McpTool } from './tools/base-tool.interface';
import { ContextService, contextSchema } from './auth/context.service';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class McpService {
  private readonly logger = new Logger(McpService.name);
  private server: Server;
  private tools = new Map<string, McpTool>();

  constructor(
    private contextService: ContextService,
    private configService: ConfigService,
  ) {
    this.initializeServer();
  }

  private initializeServer() {
    this.server = new Server(
      {
        name: 'job-service-mcp',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Set up tool handler
    this.server.setRequestHandler('tools/call', async (request) => {
      const { name, arguments: args } = request.params;
      
      const tool = this.tools.get(name);
      if (!tool) {
        throw new Error(`Tool ${name} not found`);
      }

      try {
        // Validate context
        const context = this.contextService.validateContext(args.context);
        
        // Parse and validate arguments
        const validatedArgs = tool.inputSchema.parse(args);
        
        // Execute tool
        const result = await tool.handler(validatedArgs, context);
        
        // Log action
        await this.logToolCall(context, name, validatedArgs, result);
        
        return result;
      } catch (error) {
        this.logger.error(`Tool ${name} failed`, error);
        throw error;
      }
    });

    // List available tools
    this.server.setRequestHandler('tools/list', async () => {
      const tools = Array.from(this.tools.values()).map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: tool.inputSchema._def,
      }));
      
      return { tools };
    });
  }

  registerTool(tool: McpTool) {
    this.tools.set(tool.name, tool);
    this.logger.log(`Registered tool: ${tool.name}`);
  }

  async handleRequest(request: any): Promise<any> {
    // This will be called by the HTTP transport
    return this.server.handleRequest(request);
  }

  private async logToolCall(context: any, toolName: string, args: any, result: any) {
    // TODO: Integrate with UserActionService
    const auditEntry = this.contextService.createAuditEntry(
      context,
      toolName,
      args,
      result
    );
    
    this.logger.log('Tool call', auditEntry);
  }
}
```

## Step 6: HTTP Controller

### src/mcp/transport/mcp.controller.ts
```typescript
import { Controller, Post, Body, UseGuards, Headers } from '@nestjs/common';
import { McpService } from '../mcp.service';
import { McpAuthGuard } from '../auth/mcp-auth.guard';

@Controller('mcp')
@UseGuards(McpAuthGuard)
export class McpController {
  constructor(private readonly mcpService: McpService) {}

  @Post()
  async handleMcpRequest(
    @Body() request: any,
    @Headers('x-service-account') serviceAccount: string,
  ) {
    // Add service account to request context
    if (request.params?.arguments) {
      request.params.arguments.serviceAccount = serviceAccount;
    }
    
    return this.mcpService.handleRequest(request);
  }
}
```

## Step 7: Auth Guard

### src/mcp/auth/mcp-auth.guard.ts
```typescript
import { Injectable, CanActivate, ExecutionContext, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class McpAuthGuard implements CanActivate {
  constructor(private configService: ConfigService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers.authorization;
    const serviceAccount = request.headers['x-service-account'];

    if (!authHeader || !serviceAccount) {
      throw new UnauthorizedException('Missing authentication');
    }

    const token = authHeader.replace('Bearer ', '');
    const expectedToken = this.configService.get('mcp.serviceAccount.token');
    const expectedAccount = this.configService.get('mcp.serviceAccount.email');

    if (token !== expectedToken || serviceAccount !== expectedAccount) {
      throw new UnauthorizedException('Invalid credentials');
    }

    return true;
  }
}
```

## Step 8: Register Module

Add to `src/app.module.ts`:

```typescript
import { McpModule } from './mcp/mcp.module';

@Module({
  imports: [
    // ... other modules
    McpModule,
  ],
})
export class AppModule {}
```

## Step 9: Environment Variables

Add to `.env`:

```env
# MCP Configuration
MCP_SERVICE_ACCOUNT_EMAIL=<EMAIL>
MCP_SERVICE_ACCOUNT_TOKEN=your-secure-token-here
```

## Step 10: First Tool Implementation

Create a simple health check tool to test the setup:

### src/mcp/tools/health.tool.ts
```typescript
import { z } from 'zod';
import { createTool } from './base-tool.interface';
import { contextSchema } from '../auth/context.service';

export const healthCheckTool = createTool({
  name: 'health_check',
  description: 'Check MCP server health',
  inputSchema: z.object({
    context: contextSchema,
  }),
  handler: async (args, context) => {
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      context: {
        userId: context.userId,
        requestId: context.requestId,
      },
    };
  },
});
```

Register in McpModule:

```typescript
import { healthCheckTool } from './tools/health.tool';

@Module({
  // ...
})
export class McpModule {
  constructor(private mcpService: McpService) {}
  
  onModuleInit() {
    // Register tools
    this.mcpService.registerTool(healthCheckTool);
  }
}
```

## Testing the Setup

1. Start the server:
```bash
npm run start:dev
```

2. Test the health check:
```bash
curl -X POST http://localhost:3000/mcp \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-secure-token-here" \
  -H "X-Service-Account: <EMAIL>" \
  -d '{
    "jsonrpc": "2.0",
    "method": "tools/call",
    "params": {
      "name": "health_check",
      "arguments": {
        "context": {
          "userId": "test-user",
          "requestId": "req-123",
          "source": "test",
          "reason": "Testing MCP setup"
        }
      }
    },
    "id": 1
  }'
```

## Next Steps

1. Implement UserActionService integration in `logToolCall`
2. Add more comprehensive error handling
3. Set up rate limiting middleware
4. Create integration tests
5. Begin implementing actual job search tools

This foundation provides everything needed to start building MCP tools for the job service!