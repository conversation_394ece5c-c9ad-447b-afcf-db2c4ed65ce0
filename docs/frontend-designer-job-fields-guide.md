# Job Service Frontend Designer Guide

This document provides a comprehensive overview of all available fields for the Job List, Job View, and Smart Match (ForYou) pages.

## Table of Contents
1. [Job List Page](#job-list-page)
2. [Job Detail View Page](#job-detail-view-page)
3. [Smart Match (ForYou) Page](#smart-match-foryou-page)
4. [Field Type Reference](#field-type-reference)
5. [Design Considerations](#design-considerations)

---

## Job List Page

**Endpoint**: `POST /jobs/search`

The Job List displays search results in a simplified format optimized for performance.

### Available Fields for Each Job Card

#### Essential Information
- **title** - Job title (e.g., "Senior Frontend Developer")
- **employer.name** - Company name
- **location.city** - City where the job is located
- **location.isRemote** - Boolean flag for remote work availability

#### Employment Type Badges
- **employment.working_time** - Values: "FULL_TIME", "PART_TIME", etc.
- **employment.continuity** - Values: "PERMANENT", "TEMPORARY", "CONTRACT"
- **timing.is_new** - Boolean to show "NEW" badge
- **timing.closing_soon** - Boolean to show "CLOSING SOON" badge

#### Salary Display
- **salary.has_salary** - Whether to show salary information
- **salary.lower_bound** & **salary.upper_bound** - Salary range (when available)

#### Skills & Requirements
- **skills[]** - Array of required skills (e.g., ["React", "TypeScript", "Node.js"])
- **languages[]** - Required languages (e.g., ["Finnish", "English"])
- **seniority** - Level required: "JUNIOR", "SENIOR", "NONE"

#### Metadata
- **timing.published_date** - Publication date for "Posted X days ago"
- **timing.expires_at** - Expiration date
- **source** - Job source platform (e.g., "Jobly", "Tyomarkkinatori")

#### User Interaction Indicators (Authenticated Users Only)
- **is_viewed** - Show if user has viewed this job
- **is_liked** - Show if user has favorited this job
- **rankingScore** - Relevance score (for sorting/display)

### Pagination Controls
- **meta.total** - Total number of results
- **meta.page** - Current page
- **meta.limit** - Results per page
- **meta.has_next** - Whether to show "Next" button

---

## Job Detail View Page

**Endpoint**: `GET /jobs/:extId`

The detail view includes all fields from the list view PLUS additional rich content.

### Additional Sections Available

#### Job Summary Section
- **summary.elevator_pitch** - Brief, compelling job description (1-2 sentences)
- **summary.short_description** - Detailed job description
- **summary.primary_tasks** - Main responsibilities (string or array)

#### Enhanced Skill Information
- **skill_contexts[]** - Detailed context for each skill requirement
  - Example: "React - 3+ years building responsive web applications"

#### Salary Insights Section (when available)
- **market_salary.job_role** - Comparable role title
- **market_salary.median_salary** - Market median
- **market_salary.salary_lower_bound** - Market range minimum
- **market_salary.salary_upper_bound** - Market range maximum
- **market_salary.data_reliability** - "High", "Medium", "Low"
- **market_salary.data_source** - Source of salary data

#### Future Outlook Section (when available)
- **evolution.evolution_summary** - How this role is changing
- **evolution.automation_impact** - Impact of AI/automation
- **evolution.relevance_prediction** - Future demand prediction

#### Application Section
- **application** - Application URL or instructions
- **hasApplied** - Whether current user has applied (authenticated only)

#### Complete Location Details
- **location.region** - State/Province
- **location.country** - Country

#### Complete Employment Details
- **employment.working_hours** - Specific working hours description
- **industry** - Industry classification
- **occupation** - Occupation category

---

## Smart Match (ForYou) Page

**Endpoint**: `POST /jobs/for-you`

Returns personalized job recommendations with additional match information.

### All Standard Job Fields
Includes all fields from the Job List (see above)

### Additional Smart Match Fields

#### Match Information
- **score** - Match percentage (0-100)
- **why[]** - Array of up to 5 reasons for the match
  - Example reasons:
    - "Your React experience matches perfectly"
    - "Located in your preferred city"
    - "Salary range meets your expectations"
    - "Company culture aligns with your values"
    - "Growth opportunities in your field"

#### Recommendation Metadata
- **recommended_at** - When this job was recommended
- **ranking_score** - Personalized ranking score

### Response Structure
```json
{
  "success": true,
  "data": {
    "jobs": [ /* Array of job objects with all fields above */ ]
  },
  "message": "Successfully retrieved recommendations",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

---

## Field Type Reference

### Working Time Values
- `FULL_TIME` - Full-time position
- `PART_TIME` - Part-time position
- `FLEXIBLE` - Flexible hours
- `SHIFT_WORK` - Shift-based work

### Continuity Values
- `PERMANENT` - Permanent position
- `TEMPORARY` - Temporary/Fixed-term
- `CONTRACT` - Contract position
- `INTERNSHIP` - Internship

### Seniority Levels
- `JUNIOR` - Entry level
- `SENIOR` - Senior level
- `NONE` - No specific level

### Data Reliability Indicators
- `High` - Verified/reliable data
- `Medium` - Moderately reliable
- `Low` - Limited data available

---

## Design Considerations

### 1. Responsive Display
- **Mobile**: Prioritize title, company, location, and key badges
- **Desktop**: Show full information including skills and salary

### 2. Loading States
- Search results are paginated - implement infinite scroll or pagination
- Single job details may take longer to load due to AI enrichment

### 3. Empty States
- Handle cases where optional fields are missing
- Salary information may not be available for all jobs
- Evolution/market data is AI-generated and may be absent

### 4. Localization
- Fields marked as "translatable" auto-translate based on user language
- Use `Accept-Language` header for automatic translation
- Fallback to English if translation unavailable

### 5. Interactive Elements
- Save/Like functionality (authenticated users)
- Apply button/link
- Share functionality
- Back to search results

### 6. Performance Tips
- Use simplified search results for list views
- Only fetch full details when user clicks on a job
- Implement result caching for better UX
- Consider virtual scrolling for large result sets

### 7. Accessibility
- Ensure job cards are keyboard navigable
- Use semantic HTML for job sections
- Provide alt text for employer logos
- Announce dynamic content changes

### 8. Visual Hierarchy
1. **Primary**: Job title, Company name
2. **Secondary**: Location, Employment type, Salary
3. **Tertiary**: Skills, Posted date, Other metadata

### 9. Smart Match Specific
- Highlight match percentage prominently
- Show "why" reasons as bullet points or tags
- Consider progressive disclosure for match details
- Sort by score by default

### 10. Error Handling
- Handle API errors gracefully
- Show appropriate messages for:
  - No results found
  - Network errors
  - Invalid search parameters
  - Expired job listings