# Job Service Features

This document outlines the key features of the Job Service microservice.

## Available Features

### 1. Job Search and Discovery
* **Job Listings**: Fetch job details by ID or search using filters
  * Get job by ID: `GET /jobs/:extId`
  * Search jobs: `POST /jobs/search`
* **Top Jobs**: Get top matching jobs based on user profile
  * `POST /jobs/top`

### 2. Personalized Recommendations ("For You")
* **Personalized Job Recommendations**: Tailored job listings based on user profile and preferences
  * Generate recommendations: `POST /for-you/generate`
  * Fetch recommendations: `POST /for-you/recommendations`
  * Check recommendation status: `POST /for-you/status`
* **Job Interaction Tracking**:
  * Mark jobs as viewed: `POST /for-you/view`
  * Rate jobs (like/dislike): `POST /for-you/rate`
  * Store job classification data: `POST /for-you/classification`

### 3. Job Applications
* **Job Application Tracking**: Track applications and associated documents
  * Create application: `POST /v1/job-applications`
  * Get applications by user: `GET /v1/job-applications?userId=<id>`
  * Get application by ID: `GET /v1/job-applications/:id`

### 4. Saved Jobs
* **Job Bookmarking**: Save and manage bookmarked jobs
  * Save a job: `POST /v1/saved-jobs/:extId`
  * Get saved jobs: `GET /v1/saved-jobs`
  * Remove a saved job: `DELETE /v1/saved-jobs/:extId`

### 5. Career Data and Insights
* **Industry Data**: Information about various industries
* **Competence Management**: Track and manage user skills
* **Program Information**: Educational program details
* **Location Services**: Geographic data for job searching

### 6. AI-Powered Features
* **Job Classification**: Classify jobs based on skills and keywords
  * Classify job: `POST /jobs/classify`
* **Smart Profile Integration**: Uses user's smart profile for recommendations
* **Embedding Generation**: Generate text embeddings for search
  * `POST /jobs/embedding`

### 7. Administrative Features
* **Job Import/Export**: Administrative endpoints for job data management
  * Import jobs: `GET /jobs/import`
  * Export jobs in batches: `POST /jobs/export/batch`
* **Job Processing**: Process job data for search indexing
  * Process jobs: `GET /jobs/processing`
  * Update expired jobs: `POST /jobs/updateExpired`
* **Pinecone Integration**: Vector database operations for job search
  * Upload to Pinecone: `POST /jobs/pinecone/upload`
  * Update Pinecone keywords: `POST /jobs/pinecone/updateKeywords`
