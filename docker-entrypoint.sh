#!/bin/sh

# Wait for database to be ready
echo "Waiting for database to be ready..."
until nc -z postgres 5432; do
  echo "Waiting for postgres..."
  sleep 2
done

# Run migrations
echo "Running migrations..."
npm run migration:run

# Seed database (optional)
echo "Seeding database..."
npm run seed

# Start the application
echo "Starting the application..."
if [ "$NODE_ENV" = "production" ]; then
  npm run start:prod
else
  npm run start:dev
fi
