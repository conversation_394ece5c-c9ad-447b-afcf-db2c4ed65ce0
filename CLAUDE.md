# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Development Commands

### Development & Testing
```bash
# Start development server with hot reload
npm run start:dev

# Run unit tests
npm run test

# Run specific test file
npm run test -- path/to/test.spec.ts

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:cov

# Run end-to-end tests
npm run test:e2e

# Debug mode
npm run start:debug
```

### Code Quality
```bash
# Run ESLint with auto-fix
npm run lint

# Format code with Prettier
npm run format
```

### Database Operations
```bash
# Generate migration from entity changes
npm run migration:generate -- -n DescriptiveMigrationName

# Run pending migrations
npm run migration:run

# Revert last migration
npm run migration:revert

# Seed database with initial data
npm run seed
```

### Build & Production
```bash
# Build for production
npm run build

# Start production server
npm run start:prod

# Generate Swagger documentation
npm run swagger:generate
```

## High-Level Architecture

### Core Technology Stack
- **NestJS v11**: Enterprise-grade Node.js framework with TypeScript
- **PostgreSQL + TypeORM**: Primary database with code-first migrations
- **Redis**: Caching layer via cache-manager
- **SuperTokens**: Authentication and session management
- **Google Cloud Platform**: Pub/Sub messaging, Vertex AI, Logging
- **AI Services**: OpenAI (embeddings), Gemini (metadata extraction), Pinecone (vector search)

### Domain-Driven Module Structure

The application follows a modular architecture where each domain has its own module with consistent structure:
```
module/
├── controllers/     # HTTP endpoints
├── services/       # Business logic
├── repository/     # Database access
├── dto/           # Data transfer objects
└── entities/      # TypeORM entities
```

### Key Architectural Components

#### 1. Job Search Infrastructure (`src/app/jobs/services/search/`)
- **Hybrid Search**: Combines PostgreSQL full-text search with Pinecone vector embeddings
- **Multi-level Caching**: Redis caching for search results and job data
- **Pagination**: Cursor-based pagination with result caching
- **Query Builder**: Complex search query construction with filters

#### 2. Job Import Pipeline
- **Multiple Sources**: Jobly API and Tyomarkkinatori (Finnish job market)
- **Batch Processing**: Efficient bulk imports with progress tracking
- **AI Enhancement**: Automatic metadata extraction using Gemini
- **Employer Matching**: Intelligent matching without requiring business IDs

#### 3. Translation System (`src/app/translation/`)
- **On-demand Translation**: Store English content, translate as needed
- **AI-powered**: Uses Vertex AI for context-aware translations
- **Response Decorators**: Automatic response translation via interceptors
- **Caching**: Translation results cached to minimize API calls

#### 4. Authentication & Authorization (`src/auth/`)
- **SuperTokens Integration**: Session-based authentication
- **Guards**: Route protection with @UseGuards(AuthGuard)
- **Middleware**: Global auth middleware for session validation
- **PubSub Auth**: Separate authentication for Pub/Sub handlers

#### 5. Monitoring & Observability
- **Structured Logging**: GCP-integrated logging with correlation IDs
- **Error Tracking**: Sentry integration with source maps
- **Request Tracking**: Enhanced logging interceptor
- **Performance Metrics**: User action tracking and analytics

### Database Schema Highlights

Key entities and their relationships:
- **Job**: Core entity with location, employer, and metadata relationships
- **Employer**: Company information with optional business ID
- **JobLocation**: Hierarchical location data (municipality, region)
- **JobMetadata**: JSONB storage for flexible AI-extracted data
- **ForYou**: Personalized job recommendations
- **UserAction**: Analytics and interaction tracking

### API Patterns

- **RESTful Design**: Consistent resource-based endpoints
- **DTO Validation**: Zod + class-validator for input validation
- **Swagger Documentation**: Auto-generated OpenAPI specs
- **Global Exception Handling**: Centralized error responses
- **Interceptors**: Logging, translation, date serialization

### Recent Jobly Integration

The codebase recently added Jobly as a new job data source with:
- Custom API client for paginated job feeds
- Flexible employer matching without business IDs
- AI-powered field extraction for missing data
- Automatic job expiry based on feed presence
- Batch processing with configurable limits

### Testing Strategy

- **Unit Tests**: Service and controller level tests
- **Integration Tests**: Database and API integration tests
- **E2E Tests**: Full request/response cycle testing
- **Test Database**: Separate test database configuration
- **Mocking**: Jest mocks for external services

### Environment Configuration

- `.env` file for environment variables
- TypeORM configuration in `typeorm.config.ts`
- Module-specific configuration via ConfigService
- Docker support with custom entrypoint script