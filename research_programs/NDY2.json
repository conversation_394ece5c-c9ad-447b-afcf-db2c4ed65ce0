{"code": "NDY2", "degree_name": "Liiketalouden ammattikorkeakoulututkinto", "degree_title": "Tradenomi, tietojenkäsittely (AMK)", "modules": [{"name": "CORE COMPETENCE", "modules": [{"name": "CUSTOMER-ORIENTED ACTOR IN A BUSINESS ENVIRONMENT", "objective": "During the module, you develop your competence in business and project work and your interactive skills. \n \nAfter completing the module, the students are able to identify and describe the opportunities of the business environment and the role of marketing in business operations. The student is able to act in a customer-oriented manner in projects and work with partners in different interaction and management situations. \n \nThe student can communicate in Finnish and English to the extent needed in business activities. Students also achieve the level of competence in the second national language required from public servants.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- analyse and develop their own activity as a member of the work community and a manager \n- apply labour legislation and collective agreements \n- analyse the work community and promote wellbeing at work</P>", "name": "Management and Leadership (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- communicate in oral and written Swedish/English in work life situations in their own sector at proficiency level B1 of  the Common European Framework of Reference for Languages (CEFR)  and at the level required under section 7 of the Government Decree on Universities of Applied Sciences (1129/2014)  \n-tell about the studies and tasks of Bachelor of Business Administration \n- act as a representative of a company/organisation in Nordic operating environments \n- use the key Swedish terminology used by professionals in the sector \n- develop their interaction, communication, information search and reporting skills in Swedish in professional situations in their own field \n- develop their professional language skills by using different types of learning techniques and tools</P>\n\n<h4>Further information</h4>\n<p>The study unit is compulsory for those whose language of education is Finnish.</P>", "name": "Swedish for Bachelors of Business Administration (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- communicate in oral and written English in work life situations in their own sector at proficiency level B2 of the Common European Framework of Reference for Languages \n- tell about their BBA studies and working life tasks in English \n- act as a representative of a company/organisation in international and multicultural operating environments \n- use research information and topical information of their field in English \n- develop their professional language skills by using different types of learning techniques and tools</P>", "name": "Professional Communication in English (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe opportunities, actors and networks in the business environment while taking sustainable development into account \n- describe key business processes \n- assess the significance of the competitive situation for the company’s success \n- describe the factors that contribute to profitable business and the basic concepts of financial management</P>", "name": "Business Environments (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the basic terms of marketing \n- apply the marketing mix framework to an organization responsibly \n- acquire customer information, define target groups and describe the customer journey \n- negotiate, act and communicate in a customer-oriented manner in service situations \n- use tools to manage customer and sales data \n- complete a marketing development project</P>", "name": "Customer Experience and Marketing (10 cr)"}}, {"course": {"descriptions": "<h4>Further information</h4>\n<p>At the beginning of their studies, all students who have English and/or Swedish qualifications or prior certificates required for studies at Laurea, or who have received previous instruction in English and/or Swedish take initial tests in these languages. The initial tests are the same for all fields of study and may only be taken once. Students who transfer between degrees within Laurea do not have to retake the test. The test is used to check the students' starting level before language studies begin. Students who fail the test must take preparatory studies in the language at the start of their studies. The preparatory studies will be included in the complementary competence studies.</P>", "name": "Initial Test in English Skills (0 cr)"}}, {"course": {"descriptions": "<h4>Initial Test in Swedish Skills,\n       0 op</h4>\n<p></P>\n\n<h4>Grading scale</h4>\n<p>Approved/Failed</P>", "name": "Initial Test in Swedish Skills (0 cr)"}}]}, {"name": "COMPETENT <PERSON><PERSON><PERSON><PERSON>ER IN A DIGITAL OPERATING ENVIRONMENT", "objective": "The aim of the module is to develop basic professional competence in the ICT sector. Students will learn about the operating environment of the ICT sector, areas of software development, infrastructure solutions and related information security. \n \nAfter completing their studies, the student is able to build simple applications to meet the user's needs and is familiar with software development methods. The student can create simple database solutions and programmes using basic programming structures. In addition, the student is able to utilise suitable infrastructure solutions, taking information security perspectives into account.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the opportunities of digital services as part of the digitalisation of society \n- explain the areas of software development and software development methods  \n- explain the software development processes and supporting automation solutions \n- improve the quality of digital services by improving user experience, usability and accessibility</P>", "name": "Basics of Digital Service (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- explain the operating principle of the computer and the role of the operating system as part of computer hardware \n- explain the operating principles of information networks, the Internet and cloud services \n- explain the role of information security, data protection and cyber security as part of the ICT operating environment \n- utilise the basic functionality of virtualization solutions and cloud services \n- utilize basic OS command-line functions</P>", "name": "Basics of ICT Infrastructure and Cloud Services (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design, implement and publish web sites and web-based user interfaces using common web development tools and standards \n- evaluate web site functionality, accessibility and development needs</P>", "name": "Web Site Development (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the structure and operation of data networks in relation to the following terms: Local area networks, wireless networks, internet \n- describe the functionality of IP-networks and key internet protocols \n- identify and describe common information security threats faced by organizations data networks</P>", "name": "Data Networks and Information Security (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- solve logical problems with algorithmic thinking \n- use the fundamental syntax and building blocks of programming languages \n- plan, implement and test small-scale programs in accordance with the best practices of programming</P>", "name": "Basics of Programming (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design, use, manage and implement databases while considering the best information security and data protection practices \n- use database query language for handling table structure and data processing \n- compare different, available database solutions \n- make use of both local and cloud-based database solutions \n- use different data storage formats depending on the context of use</P>", "name": "Database Design and Implementation (5 cr)"}}]}, {"name": "DESIGNING CUSTOMER CENTRIC DIGITAL SERVICES", "objective": "The student strengthens their capabilities for developing digital services during the module. The students learn to take business objectives and sustainable development into account in development. \n \nAfter completing the module, the student is able to utilise service design methods when designing customer-centred ICT services and developing digital marketing. After completing the module, the student is able to utilise analytics and monitoring to support decision-making. In addition, the student understands the planning and development processes of ICT services and is able to promote sustainable development.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyze traffic in digital channels \n- define marketing and sales for digital channels \n- use digital tools in marketing and sales \n- apply regulations for digital business</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Digital Marketing (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- describe how value is created for the customers through service \n- describe the service design process \n- recognize and apply service design tools and methods in practice</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Service Design Process (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use planning, analysis and decision-making tools and techniques in strategic planning \n- analyze business and usage analytics and use them as a basis for service development delivery as well as sales decisions \n- make data-based business decisions in various business contexts \n- apply data in decision-making processes \n- visualize data to support decision making processes \n- identify and utilize essential data sources for IT service development and delivery</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Data-driven IT (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- implement a goal-oriented development project in the working life network \n- make use of their team in expert tasks \n- use the tools and methods for research-based development work \n- take responsibility for their own actions and their consequences \n- make decisions in unpredictable situations \n- solve problems in a creative manner and develop working methods \n- seek customer-oriented, sustainable and economically viable solutions</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Digital Service Project (10 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyze environmental, social and economic responsibilities in service business \n- develop sustainable practises in organisations</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Corporate Social Responsibility (5 cr)"}}]}, {"name": "EXPERT IN A WORK COMMUNITY", "objective": "During the module, students deepen their expertise in their chosen field. They learn how to apply for jobs that support their career plan and to act responsibly in national and international positions requiring professional expertise. Students develop the capacity to develop the activities of the organisation and their own expertise.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- evaluate their competencies and the requirements of working life \n- develop and deepen their business competencies information technology competencies \n- analyse and develop the content of their work and the operation of the organisation as a member of the work community \n- establish personal working life connections and act in networks \n- evaluate their competencies and their development \n- plan their study path and career</P>", "name": "Placement 2 (15 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The students are able to \n- apply into practice what they have learned \n- analyse and develop the content and methods of their own work \n- participate in the development of practices and business processes in the company in which the placement is completed \n- develop their business competencies \n- evaluate their competencies and their development</P>", "name": "Placement 1 (15 cr)"}}]}, {"name": "EXPERT IN DEVELOPMENT AND WOR<PERSON><PERSON><PERSON>E INNOVATION", "objective": "The aim of the module is to strengthen the student’s sense of joint agency and ability to share their competence, develop their study and learning skills , and support the development of their competence identity. The module helps the student formulate a holistic approach to their future. At the start of their studies, the student will acquire the tools, communication skills, and project work-related competences necessary for their higher education studies and the Learning by Developing process. During the module, the student will develop their competence in research-based development work. The module includes a thesis. \n \nAfter completing the module, the student will be able to assess and develop their competence while taking into account the competence needs and development prospects of working life. They will also be able to assess the professional opportunities that are available to them. The student will be able to articulate their skills and be more prepared for the job application process. The student will be better prepared to look after their well-being and resilience. The student will be able to reference professionally researched information in their communications in different communication situations. They will be able to identify development targets in working life and plan, implement and evaluate development projects in collaboration with various working life actors. They will know how to use appropriate, research-based development methods and project management techniques. They will be able to critically assess information, the results of development work, and their own activities. The student will be able to seek creative and innovative solutions to development challenges in the workplace. The student will also know how to report on the process and results of their research-based development work in their thesis. \n own field.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- present justified suggestions for development in relation to working life \n- conceptualize phenomena related to working life based on research results \n- use appropriate research and development methods in order to produce new knowledge \n- act in development projects cooperatively and in a responsible manner with working life and other partners \n- evaluate personal activities and solutions critically \n- understand concepts in an extensive scale and present topics logically with justification</P>\n\n<h4>Further information</h4>\n<p>Thesis is graded by using the assessment criteria described in the thesis guidelines.</P>", "name": "Thesis (15 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- critically assess research-based knowledge in their own field \n- select appropriate methods for research-based development work and justify their choices \n- collect data as a basis for research-based development work and analyze it \n- interpret the results of research-based development work and draw conclusions based on them \n- conduct research-based development work in accordance with data protection legislation, good scientific practices, and data management models \n- communicate in situations required for research-based development work and to produce a research-oriented text</P>", "name": "Methods for Research-based Development Work (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- write text that is based on research results, using reliable field-specific sources and referencing \n- communicate in writing and orally, taking the target group and needs into consideration \n- work methodically, responsibly and in a goal-oriented manner in a project \n- utilise IT tools in communication and project work \n- identify and describe the future needs and skills of their field</P>", "name": "Professional Communications and Project Work (5 cr)"}}], "modules": [{"name": "Development of Expertise", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyse and structure their competence identity and starting points as a learner \n- assess their study abilities skills \n- set comprehensive goals for their personal and professional growth \n- act and reflect, individually and together with others</P>", "name": "Building One’s Competence Identity (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- articulate their personal competences and set career goals  \n- strengthen their study skills \n- reflect on their agency as a collaborator in diverse and network and networked  working environments  \n- utilise the skills of others and share their own</P>", "name": "Developing as an Expert (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- develop themselves as a professional and lifelong learner \n- network and participate in expert discussions in their professional field \n- act constructively in their work community and promote their professional ability to work effectively</P>", "name": "Future-oriented Working Life Expert (1 cr)"}}]}]}]}, {"name": "COMPLEMENTARY COMPETENCE", "objective": "Degree certificate in Degree Programme Business Information Technology is granted only if the student has completed at least 30 credits (ECTS) complementary competence studies related to the field of study (total amount of complementary competence studies is 60 credits).", "modules": [{"name": "COMPLEMENTARY COMPETENCE STUDIES RELATED TO THE FIELD OF STUDY", "modules": [{"name": "CLOUD DEVELOPER", "objective": "The aim of the module is for the student to learn how to act as an expert in application development. \n \nAfter completing the module, the student is able to develop digital applications and services using cloud services. The student is able to work in software development using the methods and tools of web application development, integration development and software testing. The student is able to use and manage cloud services.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- structure and describe the functional and non-functional requirements of the software \n- specify and prioritize requirements based on project goals and resources \n- describe the functionality and technical solutions of the software \n- apply software production methods and tools \n- design the software's technical architecture and components \n- communicate professionally with customers in the different phases of the requirements definition work</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the first-year studies in information technology or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Software Design Process (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the role of front-end applications and their benefits in web development \n- compare the features and suitability of the most popular technologies for different needs \n- install a development environment for developing JavaScript applications \n- design and implement dynamic and interactive web applications using JavaScript \n- build the application publishing process from the local development environment to cloud services</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the first-year studies in information technology or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Web Development 1 (Front End) (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the key technologies of the operating environment of full stack application development \n- set up the development environment and the necessary tools \n- design and implement modern web applications and REST interfaces \n- utilise databases and API interfaces in full stack applications</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit Web Development 1 (Front End) or you must have equivalent knowledge</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Web Development 2 (<PERSON> Stack) (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design and implement interactive applications deepening previous knowledge \n- develop and maintain applications with modern tools \n- utilise application frameworks and external libraries during the development process \n- build the application release pipeline utilizing DevOPS methods in publishing the application to the cloud</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Web Development 2 (Full Stack) or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Application Development Project (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- explain the basic concepts, principles and terms of testing \n- distinguish the different roles and tasks related to testing and understand their meaning \n- design test cases and functionally test software  \n- utilise different testing methods and testing techniques \n- report testing results and findings in a planned and comprehensible manner \n- automate testing</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the first-year studies in information technology or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Fundamentals of Funtional Testing (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- explain the role of integrations and APIs as part of organizations' business \n- describe the possibilities how the API and platform economy can be used as part of digital business \n- plan and describe new integrations and related functionalities \n- develop integrations and API interfaces using modern tools</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the first-year studies in information technology or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Developing Integrations (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe cloud computing services and models \n- describe developing on public cloud \n- explain how to build secure applications with containers \n- identify and describe best practice for deploying applications \n- develop code that interacts with public cloud services</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the first-year studies in information technology or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "<PERSON> Developer (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to\t \n- make architectural decisions based on cloud architectural principles and best practices \n- use public cloud services to make infrastructure scalable, reliable, and highly available \n- use managed cloud services to enable greater flexibility and resiliency in an infrastructure \n- increase performance and reduce cost of a cloud infrastructure</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the first-year studies in information technology or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Cloud Architect (5 cr)"}}]}, {"name": "DIGITAL SERVICE DESIGNER", "objective": "The aim of the module is for the student to learn to act as an expert in the development of digital services. \n \nAfter completing the module, the student is able to plan and develop digital services in a user- and customer-oriented manner. The student is able to improve the user experience and design user-friendly user interfaces and utilise solutions that support digital marketing. The student is able to consider business processes and the importance of the industry in the development of services.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- apply the methods and techniques of usability research and testing to the system being tested \n- plan, organize, analyze and report usability testing \n- evaluate and test the accessibility of the media product \n- evaluate the usability of the digital media product</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Basics of Digital Service or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Usability Testing and Evaluation (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use different tools and methods for planning and modeling digital services \n- prepare digital service planning documents \n- model digital services using interactive prototypes  \n- evaluate the usability of prototypes implemented during the study unit and provide suggestions for improving the user experience</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Basics of Digital Service or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Modeling and Prototyping a Digital Service (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- plan and implement a small-scale digital media project  \n- use appropriate applications when producing video, audio material, graphics and animations  \n- make use of the equipment most commonly used in the production process \n- share audio and video files according to the requirements set by different media</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Basics of Digital Service or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Digital Media Production (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- explains the basic concepts, principles and terms of software robotics \n- install and implement automation tools and software \n- implement automation solutions using software robotics \n- analyze the importance of automation as a whole \n- maintain automated processes, taking care of their data security, following good practices</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the first-year studies in information technology or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Software Robotics and RPA (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the features of no/low-code platforms over traditional approaches \n- identify and describe the key functionalities of a website, such as e-commerce capabilities  \n- select and set up a no/low-code platform, and create a functional website layout \n- apply SEO strategies and create content tailored to the target audience  \n- utilize analytics tools to track and analyze website performance</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Building Website Using No/low-code Platform (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the key components of marketing technology (MarTech) and their functions \n- utilise marketing technology tools at different stages of the customer journey \n- plan the company's marketing goals and choose the appropriate marketing technology solutions</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Digital Marketing or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Digital Marketing Technologies (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- utilise Information technology service management tools (ITSM) as part of good information management practices  \n- describe the key concepts of IT service management     \n- utilise overall architecture models in the development of IT services \n- describe the importance of the overall architecture in the business development of the company</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Basics of Digital Service or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "IT Service Management and Architecture (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- utilise the fundamental concepts and principles of AI, machine learning and data science tools \n- utilise technology to effectively visualise data, create interactive dashboards and communicate insights to stakeholders \n- develop skills in programming for data analysis, manipulation, visualisation and implementation of machine learning models \n- apply advanced AI and machine learning techniques and methodologies to analyse complex data sets and develop predictive models</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Data-driven IT or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Advanced Data Science (5 cr)"}}]}, {"name": "COMPLEMENTARY ICT SKILLS", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with C (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with C# (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with PHP (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Basics of Programming with C++ (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- act ethically as a member of study group and community \n- recognize and comprehend the importance of confidentiality, \nintegrity and availability model for the information and \ncybersecurity \n- recognize and comprehend different threats, attacks and \nvulnerabilities \n- comprehend and describe security technologies and tools \n- comprehend and describe security architectures and designs \n- comprehend and describe identity and access management \napproaches \n- comprehend, describe and apply risk management principles \n- comprehend and describe cryptography and PKI concepts \n- differentiate cybersecurity domains and subdomains from each \nother \n- comprehend and explain the importance of the cybersecurity in \nthe modern society \n- reflect and develop their own learning process</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites:  \nFor tiko students: study units R0240 ICT-toimintaympäristö, R0241 Tiedonhallinta ja tietokannat &amp; R0242 Tietoverkot ja tietoturva \nFor BIT students: study units R0277 ICT Environment and Infrastructure, R0279 Information Management and Databases &amp; R0280 Data Networks and Information Security \nOr equivalent competence.</P>", "name": "Introduction to Information Security (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- explain what is Subscription economy and Software as a Service delivery model \n- explain the implications of subscription economy in everyday life \n-  identify the role of metrics in a value creation process \n- identify new business opportunities enabled by subscription economy \n- apply an understanding of digital marketing to build growth</P>", "name": "Fundamentals of Subscription Economy (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- explain the concepts, principles, and dimensions of DevOps \n- describe the lean software development process and recognize the use agile implementation practices \n- describe DevOps strategies and practices that can be applied to streamline automation in all phases of software development life cycle and list the major steps of DevOps pipeline \n- list the various elements of a DevOps culture- analyze current trends in the most common and popular DevOps tools \n- outline how to achieve DevOps transformation to leverage continuous integration and continuous delivery</P>", "name": "DevOps Fundamentals (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- install the Linux operating system and use Linux machines operating in the cloud service \n- use the most common command line programs and desktop applications to perform basic operating system functions \n- create command line scripts to automate repetitive tasks \n- schedule operations with Linux scheduling tools \n- maintain the system by installing programs and updates using package management tools \n- monitor system performance and resource usage \n- design system information security solutions</P>\n\n<h4>Qualifications</h4>\n<p>To participate in the study unit, you must have completed the study unit Basics of ICT Infrastructure and Cloud Services or have equivalent knowledge.</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Fundamentals of Linux Operating System (5 cr)"}}]}]}, {"name": "OTHER COMPLEMENTARY COMPETENCE"}]}]}