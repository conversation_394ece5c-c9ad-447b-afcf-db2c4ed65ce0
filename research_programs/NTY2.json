{"code": "NTY2", "degree_name": "Liiketalouden ammattikorkeakoulututkinto", "degree_title": "Tradenomi, tietojenkäsittely (AMK)", "modules": [{"name": "CORE COMPETENCE", "modules": [{"name": "COMPETENCE IN BUSINESS AND ENTRE<PERSON><PERSON>EURSHIP", "objective": "During the module, interaction skills and competences in business, entrepreneurship and project work are developed. \n \nAfter completing the module, students are able to recognise and evaluate opportunities in the business environment, and analyse and assess the prerequisites for sustainable and successful business. Students are able to apply marketing principles and act in an entrepreneurial and customer-oriented manner in accordance with the regulatory environment. They are able to work in projects, create networks and collaborate with Finnish and international partners. Students also achieve the level of competence in their second national language required from public servants.", "courses": [{"course": {"descriptions": "<h4>Initial Test in Swedish Skills,\n       0 op</h4>\n<p></P>\n\n<h4>Grading scale</h4>\n<p>Approved/Failed</P>", "name": "Initial Test in Swedish Skills (0 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to  \n- describe the structures and phenomena of the national economy \n- describe the factors that contribute to profitable business and the basic concepts of financial management \n- interpret and use financial information for decision making \n- describe opportunities, actors and networks in the business environment considering sustainable development* \n- describe the significance of the competitive situation for the company’s success  \n- describe key business processes \n \n*The addition 'considering sustainable development' comes into effect on August 1, 2024. The verb has also been changes to 'describe' (earlier verb was recognise).</P>", "name": "Understanding Business and the Business Environment (10 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to  \n- use the basic terms of marketing \n- define the goals and metrics for marketing and sales \n- apply the marketing mix framework to an organization responsibly* \n- acquire customer information, define target groups, and describe the customer journey \n- act and communicate in a customer-oriented manner in sales situations  \n- carry out a marketing development project** \n \n**The addition 'responsibly' comes into effect on August 1, 2024. \n** The translation has been corrected and modified to exclude teamwork from the learning outcome. The change comes into effect on August 1, 2024.</P>", "name": "Introduction to Marketing (10 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- communicate in Swedish both orally and in writing in various field-specific work and communication tasks \n- take into consideration the special characteristics of the Nordic operating environment \n- communicate in Swedish at the B1.2 level in accordance with the Common European Framework of Reference for Languages and pass the civil service language proficiency certificate</P>\n\n<h4>Further information</h4>\n<p>The study unit is compulsory for those whose language of education is Finnish. After passing the study unit, students meet the language skills requirements for Swedish as the second official language in public administration and the civil service language proficiency certificate will be registered in student’s records.</P>", "name": "Swedish for Bachelors of Business Administration (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- analyse and develop his/her activity as a member of the work community and a manager \n- apply labour legislation and collective agreements \n- analyse the work community and promote wellbeing at work</P>", "name": "Leadership and Management (5 cr)"}}]}, {"name": "COMPETENCE IN ICT", "objective": "During this module, students learn about the operating environment of the ICT sector and issues related to information security. Students are able to create software using basic programming structures. Students learn how to build simple applications and user interfaces and can explain the structures of an information network. They are able to communicate in English to perform the tasks required in their field.", "courses": [{"course": {"descriptions": "<h4>Further information</h4>\n<p>At the beginning of their studies, all students who have English and/or Swedish qualifications on prior certificates required for studies at Laurea, or who have received previous instruction in English and/or Swedish take initial tests in these languages. The initial tests are the same for all fields of study and may only be taken once. Students who transfer between degrees within Laurea do not have to retake the test. The test is used to check the students' starting level before language studies begin. Students who fail the test must take preparatory studies in the language at the start of their studies. Having passed the preparatory studies, students obtain credits for inclusion in elective studies.</P>", "name": "Initial Test in English Skills (0 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of programming languages \n- plan, implement and test small-scale programs in accordance with the best practices of programming</P>", "name": "Fundamentals of Programming (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the processes of information systems development, implementation and maintenance \n- describe the structure and operational environment of information systems \n- identify typical threats related to information security</P>", "name": "The ICT Environment and Infrastructure (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design and implement databases \n- manage and use databases \n- use query languages to search and modify data in a database</P>", "name": "Information Management and Databases (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the structure and operation of data networks in relation to the following terms: Local area networks, wireless networks, internet \n- describe the functionality of IP-networks and key internet protocols \n- implement and maintain basic services in a local area network \n- justify the importance of information security according to the CIA model (Confidentiality, Integrity and Availability) \n- identify common information security threats faced by organizations \n- implement basic level information security safeguards for local area network</P>", "name": "Data Networks and Information Security (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- communicate as a professional in field-specific tasks and intercultural contexts \n- produce effective spoken and written texts using an appropriate style and register \n- demonstrate knowledge of essential terminology needed in working life \n- apply principles of interpersonal and organizational communication</P>", "name": "Professional Communication in English (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design and implement web sites using fundamental web development tools and techniques \n- design, create, and publish www content \n- design and implement web site layouts according to customer needs \n- evaluate web site development needs</P>", "name": "Foundations of Web Development (5 cr)"}}]}, {"name": "SUSTAINABILITY AND BUSINESS ANALYSIS", "objective": "During the module student´s competence in service business is strengthened. After completing the module student is able to create value for customers in service business. Student is able to use various tools in service design and digital marketing. Student is able to advance sustainability, corporate responsibility and circular business in organizations. Student is able to make business decisions based on data.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyze traffic in digital channels \n- plan digital customer experience \n- plan marketing and sales for digital channels  \n- use digital tools in marketing and sales \n- apply regulations for digital business</P>\n\n<h4>Further information</h4>\n<p>The last study unit implementation for security, safety and risk management students will be arranges on spring semester 2025. In case you have not completed the study unit latest by then, you can attend the study unit implementation targeted for the business management students.</P>", "name": "Digital Marketing and Sales (10 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use planning, analysis and decision-making tools and techniques in strategic planning \n- analyze financial statements and business reports and use them as a basis for decisions \n- make business decisions in various business contexts \n- apply data in decision making</P>", "name": "Data-driven Decision Making (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe how value is created for the customers through service \n- recognize the essence of Service Design thinking and methods \n- plan, implement and evaluate a Service Design project using service design methods \n- apply creative problem-solving and develop working methods individually and in teams \n- foster entrepreneurship through service design</P>", "name": "Service Design (10 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyze environmental, social and economic responsibilities in service business \n- develop sustainable practises in organisations</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Corporate Social Responsibility (5 cr)"}}]}, {"name": "EXPERT IN A WORK COMMUNITY", "objective": "During the module, students deepen their expertise in their chosen field. They learn how to apply for jobs that support their career plan and to act responsibly in national and international positions requiring professional expertise. Students develop the capacity to develop the activities of the organisation and their own expertise.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The students are able to \n- apply into practice what they have learned \n- analyse and develop the content and methods of their own work \n- participate in the development of practices and business processes in the company in which the placement is completed \n- develop their business competencies \n- evaluate their competencies and their development</P>", "name": "Placement 1 (15 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- evaluate their competencies and the requirements of working life \n- develop and deepen their business competencies \n- analyse and develop the content of their work and the operation of the organisation as a member of the work community \n- establish personal working life connections and act in networks \n- evaluate their competencies and their development \n- plan their study path and career</P>", "name": "Placement 2 (15 cr)"}}]}, {"name": "EXPERT IN DEVELOPMENT AND WOR<PERSON><PERSON><PERSON>E INNOVATION", "objective": "The objective of the module is to develop the students’ abilities to study individually and in teams, to prepare to carry out research and development work, and to support career development and the creation of a work identity. \n \nAt the beginning of their studies students acquire familiarity with the tools required in higher education and the operating methods of development-based learning. After completing the module, students are able to work responsibly both independently and as team members. They are able to assess and develop themselves as learners and are capable of identifying development targets in the workplace. \n \nStudents are able to plan, implement and evaluate a project in cooperation with different networks in the workplace. They are able to employ appropriate research methods and the methods of development activities and project work. Students are able to identify creative and innovative solutions for the challenges of workplace development. \n \nStudents are able to report the results of their development work using a style of writing characteristic of research. Students are able to plan their studies, development and career with an eye on the development prospects of their own field.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- present justified suggestions for development in relation to working life \n- conceptualize phenomena related to working life based on research results \n- use appropriate research and development methods in order to produce new knowledge \n- act in development projects cooperatively and in a responsible manner with working life and other partners \n- evaluate personal activities and solutions critically \n- understand concepts in an extensive scale and present topics logically with justification</P>\n\n<h4>Further information</h4>\n<p>Thesis is graded by using the assessment criteria described in the thesis guidelines.</P>", "name": "Thesis (15 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- plan, implement and evaluate a practical project in multidisciplinary cooperation \n- work in an appropriate way in multicultural organisations and networks \n- acquire and use information to support development, and use ICT tools \n- communicate appropriately and in a goal-oriented way in the communication and documenting situations required by the project</P>", "name": "Project Management and Communication (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- critically assess research-based knowledge \n- select appropriate research and development methods for their research or development work and justify their choices \n- collect empirical data and analyse it \n- interpret the results of research and development work and draw conclusions based on them \n- assess the ethics and reliability of research and development work  \n- communicate in situations required for research and development work and to produce a research text</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes are assessed by using level 2 of Laurea's common assessment criteria.</P>", "name": "Research and Development Methods (5 cr)"}}], "modules": [{"name": "From a Student to a Professional", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- plan their studies, taking into account prior learning and employment opportunities in the field  \n- understand the significance of internationality in the job market \n- obtain information about opportunities for developing competences \n- apply different learning techniques \n- assess their personal well-being and its significance for their professional resilience \n- assess their need for guidance and support \n- identify and evaluate personal strengths and development needs</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes are assessed by using level 1 of Laurea's common assessment criteria.</P>", "name": "Study Skills and Professional Orientation (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- create a personal career plan \n- look into employment opportunities and prerequisites for entrepreneurship in their own field \n- build and utilize professional networks  \n- describe their own competence, apply for jobs in the field and understand the significance of social media in the job seeking process \n- anticipate future trends in the job market and their likely impact on their career  \n- promote personal well-being</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes are assessed by using level 1 of Laurea's common assessment criteria.</P>", "name": "Professional Development (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- continuously develop their competences \n- look into further study opportunities  \n- describe their own competence and build a professional profile for the job market \n- make good use of different employment opportunities \n- understand the key labour legislation principles in their field and comply with them \n- promote their well-being at work in order to cope with the workload</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes are assessed by using level 1 of Laurea's common assessment criteria.</P>", "name": "Starting a Successful Career (1 cr)"}}]}]}]}, {"name": "COMPLEMENTARY COMPETENCE", "objective": "Degree certificate in Degree Programme Business Information Technology is granted only if the student has completed at least 30 credits (ECTS) complementary competence studies related to the field of study (total amount of complementary competence studies is 60 credits).", "modules": [{"name": "COMPLEMENTARY COMPETENCE STUDIES RELATED TO THE FIELD OF STUDY", "modules": [{"name": "DESIGN OF APPLICATIONS AND DIGITAL SERVICES", "objective": "The aim of the module is that the student is able to work in software or digital service design tasks. The student is able to apply (user-centred) the key methods and tools of the (software and) digital service development planning process.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- apply user-centered design principles and methodologies in the development of digital services \n- analyse the opportunities of user-centered design and implementation processes in the development of digital services \n- conduct end-user research according to the ethical norms of the field</P>", "name": "User-centered Design of Digital Services (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>Student is able \n- to employ the user-centric techniques and description methods for designing and modelling a digital service \n- to model digital services \n- to conceptualise and produce appropriate and viable prototypes with reference to the design documents for the digital service</P>", "name": "Modelling a Digital Service (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able \n- to apply methods of software engineering \n- to communicate with customers in order to map needs and determine user requirements \n- to document software requirements \n- to design web applications \n- to work responsibly in a software development team</P>", "name": "Defining and Designing a Software Product (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- differentiate the app ecosystem and their pros and cons \n- describe the key technologies to develop applications for the major mobile platforms \n- identify and apply a systematic approach for mobile app design \n- implement app ideas with design principles: user experience (UX), user-centered design and mobile constraints \n- work in a team to generate mobile app wireframe and paper prototype \n- configure and use a development environment for a chosen mobile platform \n- develop a simple mobile application prototype</P>", "name": "Introduction to Mobile App Design and Development (5 cr)"}}]}, {"name": "APPLICATION DEVELOPMENT", "objective": "The aim of the module is that the student is able to work in the design and development tasks of software or digital services. The students are able to apply key methods and tools for application development.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- develop and implement interactive web applications using PHP and relational databases \n- use the PHP programming language to implement server-side scripts that interact with a relational database \n- use online collaborative tools to build and deploy PHP based web applications \n- manage the deployment process of PHP based web applications</P>", "name": "Web App Development with PHP/SQL (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- identify the role of advanced web content management systems and explain how they deliver value \n - compare the features, benefits and applicability of major web content management systems available in the market \n- set up a development environment associated with a selected web content management system \n- design, develop and implement websites using a selected web content management system</P>", "name": "Web Content Management Systems (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- plan and document the complete development process of a mobile application \n- apply appropriate methods and technologies to build and deploy cross platform mobile apps \n- apply collaborative development with online tools (Github) and version control \n- differentiate production and maintenance process of Web and Mobile apps \n- learn and describe monetizing with mobile and web apps</P>", "name": "Building and Deploying Cross Platform Mobile Apps (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with C (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with C# (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with Python (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with PHP (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Programming with <PERSON> (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Basics of Programming with C++ (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the fundamental syntax and building blocks of the given programming language \n- plan, implement and test small-scale programs with the given programing language</P>", "name": "Object-oriented Programming with C++ (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to  \n - identify the role of client-side web technologies and explain how they deliver value  \n - compare the features, benefits and applicability of major client-side web technologies available in the market  \n - set up a development environment for implementing Javascript based web applications  \n - design, develop and implement dynamic and interactive web applications using Javascript based technologies</P>", "name": "Dynamic Web Applications with Javascript (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- describe the key technologies of the FullStack application development operating environment \n- install the development environment and the necessary tools \n- design and implement SPA applications and REST interfaces \n- Utilizes databases and APIs in FullStack applications</P>", "name": "Full Stack Development (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- apply the criteria of object-oriented programming \n- design applications using modeling methods \n- develop applications with the help of ready-made application libraries \n- build graphical user interfaces \n- utilize databases as part of applications</P>", "name": "Object-oriented Programming (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design and implement interactive applications by applying their previous competences \n- develop, implement and maintain applications with modern tools \n- utilize application frameworks and external libraries during the development process</P>", "name": "Application Development Project (5 cr)"}}]}, {"name": "TESTING AND QUALITY ASSURANCE", "objective": "After completing the module, the student is able to act in different roles in the test processes. The student will understand and apply the methods and tools that are central for application tests.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- apply usability testing methods and techniques in the design and implementation of research and development projects that support product and service planning \n- plan, organize, analyse and report a usability test \n- explain the principles of attainability \n- evaluate the usability of a digital media product</P>", "name": "Usability Testing and Evaluation (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to:  \n- understand basics of software testing and quality assurance \n- use common software testing terminology \n- create the test process, understand the major testing activities and  work products \n- describe the different test levels and test types \n- apply a review technique to a work product to find defects \n- apply test techniques to derive test cases \n-handle test management and defect documentation \n- learn purpose of various test tools</P>", "name": "Fundamentals of Software Testing (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- explain basic concepts, principles and terms of testing \n- design test cases in different testing environments \n- run functional and non-functional testing \n- apply different testing methods and techniques \n- report the results of testing reasonably \n- analyze the relevance of testing \n- sort different roles and tasks in software testing and understand their meaning</P>", "name": "Functional Testing of Software (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- explain the basic concepts, principles and terms of software robotics \n- install and deploy automation tools and software \n- use various software robotics automation tools \n- write command line scripts \n- automate and schedule activities \n- design and write automated tests \n- apply different automation methods and techniques \n- analyze the importance of automation as a whole \n- maintain automated processes, ensuring their security in accordance with good practices.</P>", "name": "Robotic Process Automation (5 cr)"}}]}, {"name": "OTHER COMPLEMENTARY COMPETENCES IN ICT", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- install Linux operating system \n- modify OS according to requirements \n- use desktop and command line interface \n- create, modify and delete files and folders \n- create command line scripts \n- automate and schedule scripts \n- maintain the system and its data security</P>\n\n<h4>Content</h4>\n<p><PERSON><PERSON><PERSON><PERSON> perehdytään Linux-käyttöjärjestelmän rooliin sekä mahdollisuuksiin osana yritysten tietojärjestelmiä. Kurssin aikana opiskelija tutustuu Linux-pohjaisten työasemien ja palvelimien käyttöön ja hallintaan. Aiheita ovat mm. käyttöjärjestelmän asentaminen ja konfigurointi, erilaisten palveluiden/palvelimien asennus ja ylläpito, tavallisimpien sovellusten käyttö, varmuuskopiointi, tietoturva-asiat sekä yksinkertaisten komentoriviohjelmien tuottaminen.</P>", "name": "Introduction to Linux Operating System (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- create, edit and save audiovisual material \n- utilize common production devices \n- recognize distribution requirements for audiovisual material using different media forms \n- design and manuscript audiovisual production</P>", "name": "Audiovisual Production (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- creatively implement media elements using digital image, audio and video \n- create vector graphics and animation and make use of them in digital media \n- attach functionality and interaction to animation \n- use common tools and methods in production process \n- utilize latest technologies in creating digital media</P>", "name": "Planning and Implementation of Media Elements (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- act ethically as a member of study group and community \n- recognize and comprehend the importance of confidentiality, \nintegrity and availability model for the information and \ncybersecurity \n- recognize and comprehend different threats, attacks and \nvulnerabilities \n- comprehend and describe security technologies and tools \n- comprehend and describe security architectures and designs \n- comprehend and describe identity and access management \napproaches \n- comprehend, describe and apply risk management principles \n- comprehend and describe cryptography and PKI concepts \n- differentiate cybersecurity domains and subdomains from each \nother \n- comprehend and explain the importance of the cybersecurity in \nthe modern society \n- reflect and develop their own learning process</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites:  \nFor tiko students: study units R0240 ICT-toimintaympäristö, R0241 Tiedonhallinta ja tietokannat &amp; R0242 Tietoverkot ja tietoturva \nFor BIT students: study units R0277 ICT Environment and Infrastructure, R0279 Information Management and Databases &amp; R0280 Data Networks and Information Security \nOr equivalent competence.</P>", "name": "Introduction to Information Security (5 cr)"}}]}]}, {"name": "OTHER COMPLEMENTARY COMPETENCES"}]}]}