{"code": "NKA2", "degree_name": "Liiketalouden ammattikorkeakoulututkinto", "degree_title": "Tradenomi, tietojenkäsittely (AMK)", "modules": [{"name": "CORE COMPETENCE", "modules": [{"name": "CUSTOMER-ORIENTED ACTOR IN A BUSINESS ENVIRONMENT", "objective": "During the module, you develop your competence in business and project work and your interactive skills. \n \nAfter completing the module, the students are able to identify and describe the opportunities of the business environment and the role of marketing in business operations. The student is able to act in a customer-oriented manner in projects and work with partners in different interaction and management situations. \n \nThe student can communicate in Finnish and English to the extent needed in business activities. Students also achieve the level of competence in the second national language required from public servants.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- analyse and develop their own activity as a member of the work community and a manager \n- apply labour legislation and collective agreements \n- analyse the work community and promote wellbeing at work</P>", "name": "Management and Leadership (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- communicate in oral and written Swedish/English in work life situations in their own sector at proficiency level B1 of  the Common European Framework of Reference for Languages (CEFR)  and at the level required under section 7 of the Government Decree on Universities of Applied Sciences (1129/2014)  \n-tell about the studies and tasks of Bachelor of Business Administration \n- act as a representative of a company/organisation in Nordic operating environments \n- use the key Swedish terminology used by professionals in the sector \n- develop their interaction, communication, information search and reporting skills in Swedish in professional situations in their own field \n- develop their professional language skills by using different types of learning techniques and tools</P>\n\n<h4>Further information</h4>\n<p>The study unit is compulsory for those whose language of education is Finnish.</P>", "name": "Swedish for Bachelors of Business Administration (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- communicate in oral and written English in work life situations in their own sector at proficiency level B2 of the Common European Framework of Reference for Languages \n- tell about their BBA studies and working life tasks in English \n- act as a representative of a company/organisation in international and multicultural operating environments \n- use research information and topical information of their field in English \n- develop their professional language skills by using different types of learning techniques and tools</P>", "name": "Professional Communication in English (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe opportunities, actors and networks in the business environment while taking sustainable development into account \n- describe key business processes \n- assess the significance of the competitive situation for the company’s success \n- describe the factors that contribute to profitable business and the basic concepts of financial management</P>", "name": "Business Environments (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use the basic terms of marketing \n- apply the marketing mix framework to an organization responsibly \n- acquire customer information, define target groups and describe the customer journey \n- negotiate, act and communicate in a customer-oriented manner in service situations \n- use tools to manage customer and sales data \n- complete a marketing development project</P>", "name": "Customer Experience and Marketing (10 cr)"}}, {"course": {"descriptions": "<h4>Further information</h4>\n<p>At the beginning of their studies, all students who have English and/or Swedish qualifications or prior certificates required for studies at Laurea, or who have received previous instruction in English and/or Swedish take initial tests in these languages. The initial tests are the same for all fields of study and may only be taken once. Students who transfer between degrees within Laurea do not have to retake the test. The test is used to check the students' starting level before language studies begin. Students who fail the test must take preparatory studies in the language at the start of their studies. The preparatory studies will be included in the complementary competence studies.</P>", "name": "Initial Test in English Skills (0 cr)"}}, {"course": {"descriptions": "<h4>Initial Test in Swedish Skills,\n       0 op</h4>\n<p></P>\n\n<h4>Grading scale</h4>\n<p>Approved/Failed</P>", "name": "Initial Test in Swedish Skills (0 cr)"}}]}, {"name": "COMPETENT <PERSON><PERSON><PERSON><PERSON>ER IN A DIGITAL OPERATING ENVIRONMENT", "objective": "The aim of the module is to develop basic professional competence in the ICT sector. Students will learn about the operating environment of the ICT sector, areas of software development, infrastructure solutions and related information security. \n \nAfter completing their studies, the student is able to build simple applications to meet the user's needs and is familiar with software development methods. The student can create simple database solutions and programmes using basic programming structures. In addition, the student is able to utilise suitable infrastructure solutions, taking information security perspectives into account.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the opportunities of digital services as part of the digitalisation of society \n- explain the areas of software development and software development methods  \n- explain the software development processes and supporting automation solutions \n- improve the quality of digital services by improving user experience, usability and accessibility</P>", "name": "Basics of Digital Service (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- explain the operating principle of the computer and the role of the operating system as part of computer hardware \n- explain the operating principles of information networks, the Internet and cloud services \n- explain the role of information security, data protection and cyber security as part of the ICT operating environment \n- utilise the basic functionality of virtualization solutions and cloud services \n- utilize basic OS command-line functions</P>", "name": "Basics of ICT Infrastructure and Cloud Services (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design, implement and publish web sites and web-based user interfaces using common web development tools and standards \n- evaluate web site functionality, accessibility and development needs</P>", "name": "Web Site Development (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- describe the structure and operation of data networks in relation to the following terms: Local area networks, wireless networks, internet \n- describe the functionality of IP-networks and key internet protocols \n- identify and describe common information security threats faced by organizations data networks</P>", "name": "Data Networks and Information Security (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- solve logical problems with algorithmic thinking \n- use the fundamental syntax and building blocks of programming languages \n- plan, implement and test small-scale programs in accordance with the best practices of programming</P>", "name": "Basics of Programming (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- design, use, manage and implement databases while considering the best information security and data protection practices \n- use database query language for handling table structure and data processing \n- compare different, available database solutions \n- make use of both local and cloud-based database solutions \n- use different data storage formats depending on the context of use</P>", "name": "Database Design and Implementation (5 cr)"}}]}, {"name": "DESIGNING CUSTOMER CENTRIC DIGITAL SERVICES", "objective": "The student strengthens their capabilities for developing digital services during the module. The students learn to take business objectives and sustainable development into account in development. \n \nAfter completing the module, the student is able to utilise service design methods when designing customer-centred ICT services and developing digital marketing. After completing the module, the student is able to utilise analytics and monitoring to support decision-making. In addition, the student understands the planning and development processes of ICT services and is able to promote sustainable development.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyze traffic in digital channels \n- define marketing and sales for digital channels \n- use digital tools in marketing and sales \n- apply regulations for digital business</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Digital Marketing (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- describe how value is created for the customers through service \n- describe the service design process \n- recognize and apply service design tools and methods in practice</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Service Design Process (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- use planning, analysis and decision-making tools and techniques in strategic planning \n- analyze business and usage analytics and use them as a basis for service development delivery as well as sales decisions \n- make data-based business decisions in various business contexts \n- apply data in decision-making processes \n- visualize data to support decision making processes \n- identify and utilize essential data sources for IT service development and delivery</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Data-driven IT (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- implement a goal-oriented development project in the working life network \n- make use of their team in expert tasks \n- use the tools and methods for research-based development work \n- take responsibility for their own actions and their consequences \n- make decisions in unpredictable situations \n- solve problems in a creative manner and develop working methods \n- seek customer-oriented, sustainable and economically viable solutions</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Digital Service Project (10 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyze environmental, social and economic responsibilities in service business \n- develop sustainable practises in organisations</P>\n\n<h4>Further information</h4>\n<p>The learning outcomes of the study unit/project are evaluated based on the requirement level 2 of Laurea's common evaluation criteria.</P>", "name": "Corporate Social Responsibility (5 cr)"}}]}, {"name": "EXPERT IN A WORK COMMUNITY", "objective": "During the module, students deepen their expertise in their chosen field. They learn how to apply for jobs that support their career plan and to act responsibly in national and international positions requiring professional expertise. Students develop the capacity to develop the activities of the organisation and their own expertise.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- evaluate their competencies and the requirements of working life \n- develop and deepen their business competencies information technology competencies \n- analyse and develop the content of their work and the operation of the organisation as a member of the work community \n- establish personal working life connections and act in networks \n- evaluate their competencies and their development \n- plan their study path and career</P>", "name": "Placement 2 (15 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The students are able to \n- apply into practice what they have learned \n- analyse and develop the content and methods of their own work \n- participate in the development of practices and business processes in the company in which the placement is completed \n- develop their business competencies \n- evaluate their competencies and their development</P>", "name": "Placement 1 (15 cr)"}}]}, {"name": "EXPERT IN DEVELOPMENT AND WOR<PERSON><PERSON><PERSON>E INNOVATION", "objective": "The aim of the module is to strengthen the student’s sense of joint agency and ability to share their competence, develop their study and learning skills , and support the development of their competence identity. The module helps the student formulate a holistic approach to their future. At the start of their studies, the student will acquire the tools, communication skills, and project work-related competences necessary for their higher education studies and the Learning by Developing process. During the module, the student will develop their competence in research-based development work. The module includes a thesis. \n \nAfter completing the module, the student will be able to assess and develop their competence while taking into account the competence needs and development prospects of working life. They will also be able to assess the professional opportunities that are available to them. The student will be able to articulate their skills and be more prepared for the job application process. The student will be better prepared to look after their well-being and resilience. The student will be able to reference professionally researched information in their communications in different communication situations. They will be able to identify development targets in working life and plan, implement and evaluate development projects in collaboration with various working life actors. They will know how to use appropriate, research-based development methods and project management techniques. They will be able to critically assess information, the results of development work, and their own activities. The student will be able to seek creative and innovative solutions to development challenges in the workplace. The student will also know how to report on the process and results of their research-based development work in their thesis.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- present justified suggestions for development in relation to working life \n- conceptualize phenomena related to working life based on research results \n- use appropriate research and development methods in order to produce new knowledge \n- act in development projects cooperatively and in a responsible manner with working life and other partners \n- evaluate personal activities and solutions critically \n- understand concepts in an extensive scale and present topics logically with justification</P>\n\n<h4>Further information</h4>\n<p>Thesis is graded by using the assessment criteria described in the thesis guidelines.</P>", "name": "Thesis (15 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- critically assess research-based knowledge in their own field \n- select appropriate methods for research-based development work and justify their choices \n- collect data as a basis for research-based development work and analyze it \n- interpret the results of research-based development work and draw conclusions based on them \n- conduct research-based development work in accordance with data protection legislation, good scientific practices, and data management models \n- communicate in situations required for research-based development work and to produce a research-oriented text</P>", "name": "Methods for Research-based Development Work (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- write text that is based on research results, using reliable field-specific sources and referencing \n- communicate in writing and orally, taking the target group and needs into consideration \n- work methodically, responsibly and in a goal-oriented manner in a project \n- utilise IT tools in communication and project work \n- identify and describe the future needs and skills of their field</P>", "name": "Professional Communications and Project Work (5 cr)"}}], "modules": [{"name": "Development of Expertise", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- analyse and structure their competence identity and starting points as a learner \n- assess their study abilities skills \n- set comprehensive goals for their personal and professional growth \n- act and reflect, individually and together with others</P>", "name": "Building One’s Competence Identity (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- articulate their personal competences and set career goals  \n- strengthen their study skills \n- reflect on their agency as a collaborator in diverse and network and networked  working environments  \n- utilise the skills of others and share their own</P>", "name": "Developing as an Expert (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- develop themselves as a professional and lifelong learner \n- network and participate in expert discussions in their professional field \n- act constructively in their work community and promote their professional ability to work effectively</P>", "name": "Future-oriented Working Life Expert (1 cr)"}}]}]}]}, {"name": "COMPLEMENTARY COMPETENCE", "objective": "Degree certificate in Degree Programme Business Information Technology (Cyber Security) is granted only if the student has completed at least 45 credits (ECTS) complementary competence studies related to the field of study (total amount of complementary competence studies is 60 credits).", "modules": [{"name": "COMPLEMENTARY COMPETENCE STUDIES RELATED TO THE FIELD OF STUDY", "modules": [{"name": "INFORMATION INFRASTRUCTURE AND SECURITY", "objective": "The objective of the module is to gain understanding of the infrastructure of and the operations in modern communication networks. After completing this module student is able to configure basic network services in a secure way and anticipate future security threats based on structural vulnerabilities in IP networks.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to: \n- develop and manage LAN services \n- identify common network applications for companies and the \nsecurity threats directed at them. \n- plan, design, develop and implement the secure ICT \ninfrastructure with needed network applications</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites:  \nFor tiko students: study units R0240 ICT-toimintaympäristö, R0241 Tiedonhallinta ja tietokannat &amp; R0242 Tietoverkot ja tietoturva \nFor BIT students: study units R0277 ICT Environment and Infrastructure, R0279 Information Management and Databases &amp; R0280 Data Networks and Information Security \nOr equivalent competence.</P>", "name": "Network Applications (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- comprehend and describe the operations and protocols in global \nIP networks \n- calculate IP subnets and supernets \n- comprehend and describe security vulnerabilities in IP network \ninfrastructure \n- compare and contrast IPv6 to IPv4 \n- comprehend and describe the functional concepts and security \nrisks in wireless networking \n- comprehend and describe the functional concepts and security \nrisks in cloud computing</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites:  \nFor tiko students: study units R0240 ICT-toimintaympäristö, R0241 Tiedonhallinta ja tietokannat &amp; R0242 Tietoverkot ja tietoturva \nFor BIT students: study units R0277 ICT Environment and Infrastructure, R0279 Information Management and Databases &amp; R0280 Data Networks and Information Security \nOr equivalent competence.</P>", "name": "Internet Infrastructure and Security (10 cr)"}}]}, {"name": "INFORMATION SECURITY MANAGEMENT", "objective": "The objective of the module is to learn comprehensive cybersecurity foundation skills including terms, taxonomies, concepts, technologies, processes and practices of the cybersecurity knowledge and practice domains. The content of this module will also provide the knowledge and skills for applying the principles of information security management, risk management, safeguard-security and continuity plan formulation in an organization considering people, processes and technologies.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- act ethically as a member of study group and community \n- recognize and comprehend the importance of confidentiality, \nintegrity and availability model for the information and \ncybersecurity \n- recognize and comprehend different threats, attacks and \nvulnerabilities \n- comprehend and describe security technologies and tools \n- comprehend and describe security architectures and designs \n- comprehend and describe identity and access management \napproaches \n- comprehend, describe and apply risk management principles \n- comprehend and describe cryptography and PKI concepts \n- differentiate cybersecurity domains and subdomains from each \nother \n- comprehend and explain the importance of the cybersecurity in \nthe modern society \n- reflect and develop their own learning process</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites:  \nFor tiko students: study units R0240 ICT-toimintaympäristö, R0241 Tiedonhallinta ja tietokannat &amp; R0242 Tietoverkot ja tietoturva \nFor BIT students: study units R0277 ICT Environment and Infrastructure, R0279 Information Management and Databases &amp; R0280 Data Networks and Information Security \nOr equivalent competence.</P>", "name": "Introduction to Information Security (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- act ethically as a member of study group and community \n- comprehend, explain and apply information security program \ndevelopment and management principles \n- comprehend, explain and apply risk management, incident \nmanagement and compliance principles \n- plan, implement and finalize risk assessment process \n- analyze typical information security management related \nproblems and draw solutions to them \n- reflect and develop their own learning process</P>\n\n<h4>Qualifications</h4>\n<p>TIKO and BIT students: Compulsory ICT-studies \nOr equivalent competence.</P>", "name": "Information Security Management (5 cr)"}}]}, {"name": "CYBERSECURITY TECHNOLOGIES", "objective": "The key objective of the module is developing comprehensive cybersecurity skills and practices. The students will learn important skills and practices used to protect data and enterprise systems against cyber threats. The students will also gain practical skills for \ndetecting and managing vulnerabilities, threats and risks within organizations’ information infrastructure, networks and cyberspace. The students gains the skills that enhances and build cyber resilience, cyber workforce capacity and cyber defense of the society.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- act ethically as a member of study group, community and \nworking-life partners \n- identify threats, vulnerabilities and risks associated web \napplications and web servers \n- outline common attack tactics, techniques used when hacking \nweb servers, applications and wireless networks \n- outline security controls for information systems against \ncommon threats \n- perform hacking exercises in virtualized training environment \n- reflect and develop their own learning process, working-life \nand practitioners skills</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites: \nR0318 Introduction to Information Security &amp; R0319 Information \nSecurity Management OR R0385 Information Infrastructure and \nSecurity &amp; A9185 Network Applications OR equivalent \ncompetence</P>", "name": "Enterprise Security and Practitioners (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>Learning targets: \nThe student is able to \n- act ethically as a member of study group, community and \nworking-life partners \n- explain network discovery, reconnaissance, harvesting and \nvulnerability analysis techniques \n- select appropriate tools for network discovery reconnaissance, \nharvesting and vulnerability analysis \n- analyze network vulnerabilities with network discovery, \nreconnaissance, harvesting and analyzing tools \n- reduce the attack surface of a network host \n- present the results of network reconnaissance and vulnerability \nanalysis in professional format \n- reflect and develop their own learning process working-life \nand practitioners skills</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites: \nR0318 Introduction to Information Security &amp; R0319 Information \nSecurity Management OR R0385 Information Infrastructure and \nSecurity &amp; A9185 Network Applications OR equivalent \ncompetence</P>", "name": "Cybersecurity Analyst (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- act ethically as a member of study group, community and \nworking-life partners \n- explain the role of ethical hacking in the offensive and defensive \nnetwork and applications security \n- plan penetration testing process including footprinting, \nreconnaissance, scanning networks, enumeration, vulnerability \nanalysis and system hacking \n- select tools and techniques used in penetration testing process \n- select appropriate security controls to network security based on \nvulnerability analysis \n- use the most common penetration testing tools in virtualized \ntraining environment \n- reflect and develop their own learning process and \nworking-life skills</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites: \nR0318 Introduction to Information Security &amp; R0319 Information \nSecurity Management OR R0385 Information Infrastructure and \nSecurity &amp; A9185 Network Applications OR equivalent \ncompetence</P>", "name": "Network and Applications Security (5 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- act ethically as a member of study group, community and \nworking-life partners \n- identify threats, vulnerabilities and risks associated with \norganization’s systems \n- recognize and apply the confidentiality, integrity and availability \nmodel for the information and cybersecurity in practice \n- comprehend differences regarding different cryptographic \nmethods, its applications and techniques \n- plan and apply the concepts of risk assessment, risk analysis and \nrisk management \n- outline security controls for workstation and server \nenvironments \n- implement and manage authentication and authorization \nmechanisms \n- reflect and develop their own learning process and \nworking-life skills</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites: \nR0318 Introduction to Information Security &amp; R0319 Information \nSecurity Management OR R0385 Information Infrastructure and \nSecurity &amp; A9185 Network Applications OR equivalent \ncompetence</P>", "name": "Systems Security (5 cr)"}}]}, {"name": "CYBERSECURITY WORK-<PERSON>IF<PERSON> PRACTICES", "objective": "The objective of the module is to gain cybersecurity working-life skills and practices. The students develops the practitioners’ approach in real working environment. The student will acquire the practitioner’s skills including planning and implementing a cybersecurity project and report the results. The project target can vary from research, innovation, development or business projects. The students are provided with an opportunities to take part in working-life events, cyber ranges and cyber drill trainings. The students gains the skills and practices that builds cyber resilience, cyber workforce capacity and cyber defense of the society.", "courses": [{"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- take sole responsibility for working as a member cybersecurity \nanalyst team (project target varies including research, innovation, \nbusiness, cyber ranges, cyber drill or cyber defense projects) \n- participate and act ethically as a member of team, community \nand working-life partners \n- select appropriate tools and strategies for network \nreconnaissance and vulnerability analysis project in real exercise \nor company environment \n- present the results of network reconnaissance and vulnerability \nanalysis in a professional format \n- analyze critically the outcome of the project \n- manifest cybersecurity professional practices and apply \npractitioners skills in the community</P>\n\n<h4>Content</h4>\n<p>Note: The learning targets can be advance studies in one or more \nof following (not limited to only those): cybersecurity practices, \ncyber ranges, cyber drills, cyber forensics, cyber war, cyber \ndefence, cybersecurity for AI and robotics, cybersecurity for \nblockchain, cybersecurity for supply chain management and \nrelevant advance cybersecurity topics.</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites: \nR0318 Introduction to Information Security &amp; R0319 Information \nSecurity Management AND R0385 Information Infrastructure and \nSecurity &amp; A9185 Network Applications OR equivalent \ncompetence</P>", "name": "Cybersecurity Hackathon Project (3 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- participate and act as a cybersecurity professional in workinglife events including industrial visits, seminars, workshops, \nhands-on, cyber ranges, cyber drill and cyber defense activities \n- act ethically as a member of team, community and working-life \npartners \n- network with other cybersecurity professionals \n- summarize and analyze critically the content or outcome of the \nevent \n- manifest cybersecurity professional practices and apply \npractitioners skills in the community</P>\n\n<h4>Content</h4>\n<p>The study unit is organized as a collaboration with Nixu and Hoxhunt. The preliminary idea is to arrange the series of workshops or webinars where students have possibility to meet cybersecurity staff from recruiters to topical experts. The topics will be decided before the study unit starts. \n \nNote: The learning targets can be advance studies in one or more \nof following (not limited to only those): company presentations, recruiters tips, cybersecurity practices, \ncyber ranges, cyber drills, cyber forensics, cyber war, cyber \ndefence, cybersecurity for AI and robotics, cybersecurity for \nblockchain, cybersecurity for supply chain management and \nrelevant advance cybersecurity topics.</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites: \nR0318 Introduction to Information Security &amp; R0319 Information \nSecurity Management AND R0385 Information Infrastructure and \nSecurity &amp; A9185 Network Applications OR equivalent \ncompetence</P>", "name": "Cybersecurity Working Life Practices (2 cr)"}}, {"course": {"descriptions": "<h4>Learning outcomes</h4>\n<p>The student is able to \n- take sole responsibility for working as a member cybersecurity project team (project target varies including research, innovation, business, cyber ranges, cyber drill or cyber defense projects) \n- act ethically as a member of project team, community and working-life partners \n- plan, implement and document cybersecurity research project \n- select and apply appropriate frameworks and methods for cybersecurity research project \n- present research results in the academic and business format \n- manifest cybersecurity professional practices and apply practitioners skills in the community</P>\n\n<h4>Content</h4>\n<p>Note: The learning targets can be advance studies in one or more \nof following (not limited to only those): cybersecurity practices, \ncyber ranges, cyber drills, cyber forensics, cyber war, cyber \ndefence, cybersecurity for AI and robotics, cybersecurity for \nblockchain, cybersecurity for supply chain management and \nrelevant advance cybersecurity topics.</P>\n\n<h4>Qualifications</h4>\n<p>Prerequisites: \nR0318 Introduction to Information Security &amp; R0319 Information \nSecurity Management AND R0385 Information Infrastructure and \nSecurity &amp; A9185 Network Applications OR equivalent \ncompetence</P>", "name": "Cybersecurity Project (5 cr)"}}]}]}, {"name": "OTHER COMPLEMENTARY COMPETENCES"}]}]}