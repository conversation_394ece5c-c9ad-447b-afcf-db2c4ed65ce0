{"openapi": "3.0.0", "paths": {"/education/industries/recommend": {"post": {"description": "\n      Returns a list of recommended industries based on the user's educational program.\n      The recommendations include:\n      - Industry name (translated based on responseLanguage)\n      - Industry code\n      - Projected growth rate\n      - Program alignment score\n      - Median salary range\n      \n      The response is sorted by alignment score in descending order.\n    ", "operationId": "IndustryController_getRecommendedIndustries", "parameters": [], "requestBody": {"required": true, "description": "Program code and language preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndustryRecommendationRequestDto"}}}}, "responses": {"200": {"description": "Successfully retrieved industry recommendations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/IndustryRecommendationResponseDto"}}}}}, "400": {"description": "Invalid request parameters"}}, "summary": "Get industry recommendations based on program code", "tags": ["Education Industries"]}}, "/education/industries/overview": {"get": {"description": "\n      Returns a detailed overview of a specific industry including:\n      - Industry name and description\n      - Required skills and certifications\n      - Popular jobs in the industry with:\n        - Salary information\n        - Experience requirements\n        - Top employers\n        - Historical salary trends\n      \n      The response is localized based on the responseLanguage parameter.\n    ", "operationId": "IndustryController_getIndustryOverview", "parameters": [{"name": "industryName", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "responseLanguage", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "programCode", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Successfully retrieved industry overview", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/IndustryViewDto"}}}}, "400": {"description": "Invalid request parameters"}, "404": {"description": "Industry not found"}}, "summary": "Get detailed overview of a specific industry", "tags": ["Education Industries"]}}, "/education/careers/paths": {"post": {"description": "\n      Returns a list of recommended career paths based on the user's educational program.\n      The recommendations include:\n      - Career title (translated based on responseLanguage)\n      - Description\n      - Required skills\n      - Growth potential score\n      - Program alignment score\n      - Salary range\n      \n      The response is sorted by alignment score in descending order.\n    ", "operationId": "CareerController_getCareerPaths", "parameters": [], "requestBody": {"required": true, "description": "Program code and language preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CareerPathsRequestDto"}}}}, "responses": {"200": {"description": "Successfully retrieved career path recommendations", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CareerPathResponseDto"}}}}}, "400": {"description": "Invalid request parameters"}}, "summary": "Get career path recommendations based on program code", "tags": ["Education Careers"]}}, "/education/careers/view": {"post": {"description": "\n      Returns a detailed analysis of a specific career path based on the user's educational program.\n      The analysis includes:\n      - Skills assessment\n      - Transferable skills analysis\n      - Career progression path\n      - Future challenges and opportunities\n    ", "operationId": "CareerController_getCareerView", "parameters": [], "requestBody": {"required": true, "description": "Program code, job role, and language preferences", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CareerViewRequestDto"}}}}, "responses": {"200": {"description": "Successfully retrieved career view analysis", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CareerViewResponseDto"}}}}, "400": {"description": "Invalid request parameters"}}, "summary": "Get detailed career view analysis", "tags": ["Education Careers"]}}, "/jobs/import": {"post": {"operationId": "JobsController_import", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Jobs"]}}, "/jobs/processing": {"post": {"operationId": "JobsController_processing", "parameters": [], "responses": {"201": {"description": ""}}, "tags": ["Jobs"]}}}, "info": {"title": "NEDU Job Service API", "description": "API documentation for the NEDU Job Service", "version": "1.0", "contact": {}}, "tags": [{"name": "Education Industries", "description": "Endpoints related to education and industry recommendations"}], "servers": [], "components": {"schemas": {"IndustryRecommendationRequestDto": {"type": "object", "properties": {"programCode": {"type": "string", "description": "The program code of the user", "example": "CS2021"}, "responseLanguage": {"type": "string", "description": "The preferred response language", "example": "en"}, "languagesSpoken": {"description": "Languages spoken by the user", "example": ["en", "fi"], "type": "array", "items": {"type": "string"}}}, "required": ["programCode", "responseLanguage", "languagesSpoken"]}, "IndustryRecommendationResponseDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the industry", "example": "Software Development"}, "code": {"type": "string", "description": "Industry code", "example": "IT001"}, "projectedIndustryGrowth": {"type": "number", "description": "Projected industry growth percentage", "example": 12.5}, "alignmentScore": {"type": "number", "description": "Alignment score between program and industry", "example": 85.5}, "medianSalary": {"type": "string", "description": "Median salary in the industry", "example": "€45,000 - €65,000"}}, "required": ["name", "code", "projectedIndustryGrowth", "alignmentScore", "medianSalary"]}, "SkillDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the skill"}, "isAquired": {"type": "boolean", "description": "Whether the skill is acquired"}, "type": {"type": "string", "description": "Type of skill"}}, "required": ["name", "isAquired", "type"]}, "CertificationDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the certification"}, "level": {"type": "string", "description": "Level of certification"}}, "required": ["name", "level"]}, "TopEmployerDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the employer"}, "location": {"type": "string", "description": "Location of the employer"}, "website": {"type": "string", "description": "Website URL of the employer"}, "payRange": {"type": "string", "description": "Salary range offered by the employer"}}, "required": ["name", "location", "website", "payRange"]}, "SalaryByYearDto": {"type": "object", "properties": {"year": {"type": "string", "description": "Year of the salary data"}, "salary": {"type": "number", "description": "Average salary for the year"}}, "required": ["year", "salary"]}, "SalaryByExperienceDto": {"type": "object", "properties": {"experience": {"type": "string", "description": "Experience level"}, "salary": {"type": "number", "description": "Average salary for the experience level"}}, "required": ["experience", "salary"]}, "PopularJobDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Job title"}, "salary": {"type": "string", "description": "Average salary range"}, "experience": {"type": "string", "description": "Required experience level"}, "popularityLevel": {"type": "string", "description": "Job popularity level"}, "topEmployers": {"description": "List of top employers", "type": "array", "items": {"$ref": "#/components/schemas/TopEmployerDto"}}, "salaryByYear": {"description": "Historical salary data by year", "type": "array", "items": {"$ref": "#/components/schemas/SalaryByYearDto"}}, "salaryByExperience": {"description": "Salary data by experience level", "type": "array", "items": {"$ref": "#/components/schemas/SalaryByExperienceDto"}}}, "required": ["name", "salary", "experience", "popularityLevel", "topEmployers", "salaryByYear", "salaryByExperience"]}, "IndustryViewDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the industry"}, "description": {"type": "string", "description": "Description of the industry"}, "skills": {"description": "List of relevant skills", "type": "array", "items": {"$ref": "#/components/schemas/SkillDto"}}, "certifications": {"description": "List of relevant certifications", "type": "array", "items": {"$ref": "#/components/schemas/CertificationDto"}}, "popularJobs": {"description": "List of popular jobs in the industry", "type": "array", "items": {"$ref": "#/components/schemas/PopularJobDto"}}}, "required": ["name", "description", "skills", "certifications", "popularJobs"]}, "CareerPathsRequestDto": {"type": "object", "properties": {"programCode": {"type": "string", "description": "Educational program code", "example": "CS-101"}, "languagesSpoken": {"description": "Languages spoken by the user", "example": ["en", "fr"], "type": "array", "items": {"type": "array"}}, "responseLanguage": {"type": "string", "description": "Preferred language for the response", "example": "en"}}, "required": ["programCode", "languagesSpoken", "responseLanguage"]}, "CareerSkill": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the skill", "example": "Problem Solving"}, "isAcquired": {"type": "boolean", "description": "Whether the skill is acquired through the degree program", "example": true}, "type": {"type": "string", "description": "Type of skill", "enum": ["Technical", "Soft", "Domain", "Practical"], "example": "Technical"}}, "required": ["name", "isAcquired", "type"]}, "CareerPathResponseDto": {"type": "object", "properties": {"role": {"type": "string", "description": "Job Role name", "example": "Software Developer"}, "popularityLevel": {"type": "string", "description": "Popularity and growth level of the role", "enum": ["High Growth", "Moderate", "Steady", "Declining", "<PERSON><PERSON>"], "example": "High Growth"}, "salaryRange": {"type": "string", "description": "Salary range for role in Finland", "example": "€45,000 - €60,000"}, "alignment": {"type": "string", "description": "Alignment of the role with the student's profile", "enum": ["Highly aligned", "Moderately aligned", "Partially aligned", "Not aligned"], "example": "Highly aligned"}, "skills": {"description": "Skills that will help succeed in this role", "maxItems": 4, "type": "array", "items": {"$ref": "#/components/schemas/CareerSkill"}}}, "required": ["role", "popularityLevel", "salaryRange", "alignment", "skills"]}, "CareerViewRequestDto": {"type": "object", "properties": {"programCode": {"type": "string", "description": "Educational program code", "example": "CS-101"}, "jobRole": {"type": "string", "description": "Job role to analyze", "example": "Software Developer"}, "languagesSpoken": {"description": "Languages spoken by the user", "example": ["en", "fr"], "type": "array", "items": {"type": "array"}}, "responseLanguage": {"type": "string", "description": "Preferred language for the response", "example": "en"}}, "required": ["programCode", "jobRole", "languagesSpoken", "responseLanguage"]}, "CareerSkillAssessment": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the skill", "example": "Python Programming"}, "isAquired": {"type": "boolean", "description": "Whether the skill is acquired", "example": true}, "comment": {"type": "string", "description": "Comment about the skill acquisition", "example": "Strong foundation from coursework"}}, "required": ["name", "isAquired", "comment"]}, "TransferableSkill": {"type": "object", "properties": {"name": {"type": "string", "description": "Name of the transferable skill", "example": "Problem Solving"}, "existing": {"type": "string", "description": "Current context of the skill", "example": "Academic projects"}, "destination": {"type": "string", "description": "How the skill applies to the target role", "example": "Production system debugging"}}, "required": ["name", "existing", "destination"]}, "CareerPathStep": {"type": "object", "properties": {"role": {"type": "string", "description": "Role title", "example": "Junior Developer"}, "yearsExperience": {"type": "string", "description": "Years of experience required", "example": "0-2 years"}, "skills": {"description": "Key skills for this step", "example": ["JavaScript", "Git", "Agile"], "type": "array", "items": {"type": "string"}}, "salary": {"type": "string", "description": "Expected salary range", "example": "€35,000 - €45,000"}, "icon": {"type": "string", "description": "Icon identifier for the role", "example": "developer"}}, "required": ["role", "yearsExperience", "skills", "salary", "icon"]}, "CareerProgressionPath": {"type": "object", "properties": {"steps": {"description": "Career progression steps", "type": "array", "items": {"$ref": "#/components/schemas/CareerPathStep"}}, "futureChallenges": {"type": "string", "description": "Potential future challenges", "example": "Rapid technology changes requiring continuous learning"}, "optimisticScenario": {"type": "string", "description": "Optimistic career scenario", "example": "Leading a development team within 5 years"}, "expectedPath": {"type": "string", "description": "Expected career progression", "example": "Steady growth with focus on technical expertise"}}, "required": ["steps", "futureChall<PERSON>es", "optimistic<PERSON><PERSON><PERSON><PERSON>", "expectedPath"]}, "CareerViewResponseDto": {"type": "object", "properties": {"role": {"type": "string", "description": "Job role being analyzed", "example": "Software Developer"}, "skills": {"description": "Skills assessment", "type": "array", "items": {"$ref": "#/components/schemas/CareerSkillAssessment"}}, "transferableSkills": {"description": "Transferable skills analysis", "type": "array", "items": {"$ref": "#/components/schemas/TransferableSkill"}}, "careerPath": {"description": "Career progression path", "allOf": [{"$ref": "#/components/schemas/CareerProgressionPath"}]}}, "required": ["role", "skills", "transferableSkills", "careerPath"]}}}}