# API CONFIG
PORT=
NODE_ENV=
WEBSITE_DOMAIN=
API_DOMAIN=

# DATABASE CONFIG
DATABASE_URL=

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=job_service

# WORKFLOW CONFIG
WORKFLOW_API_URL=
WORKFLOW_TRANSLATIONS_API_KEY=

# TRANSLATIONS CONFIG
TRANSLATIONS_LANGUAGES= # ARRAY STRING TO PARSE ["En", "Fi", "Sv"] -> JSON.parse(<ARRAY STRING>)
TRANSLATIONS_RATE_LIMIT=

# VERTEX AI CONFIG
VERTEX_AI_MODEL=
VERTEX_AI_MODEL_CONTENT_FILTER_ENABLED=
VERTEX_AI_MODEL_TEMPERATURE=
VERTEX_AI_MODEL_MAX_TOKEN_OUTPUT=

# JOB PROCESSING
JOB_PROCESSING_API_KEY=

# REDIS CONFIG
REDIS_HOST=
REDIS_PORT=
REDIS_DATABASE=

# JOB MARKET API CONFIG
JOB_MARKET_CLIENT_ID=
JOB_MARKET_CLIENT_SECRET=
JOB_MARKET_SCOPE=
JOB_MARKET_TOKEN_ENDPOINT=
JOB_MARKET_BASE_URL=
JOB_MARKET_MAX_RETRIES=
JOB_MARKET_TIMEOUT=
JOB_MARKET_PROXY_URL=
JOB_MARKET_RATE_LIMIT=
JOB_MARKET_RATE_WINDOW=

# GOOGLE CONFIG
GCP_PROJECT_ID=
GCP_LOCATION=

# GOOGLE_TRANSLATIONS_CLIENT_ID=764086051850-6qr4p6gpi6hn506pt8ejuq83di341hur.apps.googleusercontent.com
# GOOGLE_TRANSLATIONS_CLIENT_SECRET=d-FL95Q19q7MQmFpd7hHD0Ty
# GOOGLE_TRANSLATIONS_REFRESH_TOKEN=1//09mc7B6Pp3FjOCgYIARAAGAkSNwF-L9IrliJwOordDUrx-v8OPGHEHeHBP2p5YAFw9GTypKDNq_y5nC97Nqvk0EJZF8jjecLJOnE

# Pinecone Configuration
PINECONE_API_KEY=your_pinecone_api_key
PINECONE_API_REGION=your_pinecone_region
PINECONE_API_URL=your_pinecone_url

# PDF Services
RESTPACK_API_KEY=your_restpack_api_key
PDFBOLT_API_KEY=your_pdfbolt_api_key

# JOBLY INTEGRATION
JOBLY_API_ENDPOINT="https://jobly.almamedia.fi/aineistot/nedu_ai/jobs/jobly_jobs.json"
JOBLY_IMPORT_CACHE_KEY="jobly_import:processing"
JOBLY_IMPORT_COMPLETED_CACHE_KEY="jobly_import:completed"
JOBLY_CACHE_TTL=3600

# MCP (Model Context Protocol) Configuration
# Comma-separated list of valid service tokens for MCP authentication
MCP_SERVICE_TOKENS=
# Example: MCP_SERVICE_TOKENS=token1,token2,token3

# MCP Rate Limiting (optional)
MCP_RATE_LIMIT_MAX_REQUESTS=100
MCP_RATE_LIMIT_WINDOW_MS=60000
