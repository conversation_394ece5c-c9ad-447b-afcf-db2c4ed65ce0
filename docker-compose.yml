version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: job-service-app
    ports:
      - "3000:3000"
    env_file:
      - .env.local
    environment:
      - NODE_ENV=development
      - PORT=3000
      - API_DOMAIN=http://localhost:3000
      - WEBSITE_DOMAIN=http://localhost:3001
      - DATABASE_URL=********************************************/job_service
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=postgres
      - DB_DATABASE=job_service
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_DATABASE=0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    volumes:
      - .:/app
      - /app/node_modules
    command: npm run start:dev

  postgres:
    image: postgres:15-alpine
    container_name: job-service-postgres
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=job_service
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: job-service-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
