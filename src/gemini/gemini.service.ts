import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { GoogleGenAI, Type as GoogleGenAiType } from '@google/genai';
import { JobMetadataDto } from './dto/job-metadata.dto';

@Injectable()
export class GeminiService {
  private readonly logger = new Logger(GeminiService.name);
  private readonly genAI: GoogleGenAI;
  private readonly defaultModel: string;

  constructor(private readonly configService: ConfigService) {
    const apiKey = this.configService.get<string>('GEMINI_API_KEY');
    this.defaultModel = this.configService.get<string>(
      'GEMINI_DEFAULT_MODEL',
      'gemini-2.0-flash',
    );

    if (!apiKey) {
      this.logger.warn(
        'GEMINI_API_KEY is not provided. Gemini AI features will not work properly.',
      );
    }

    this.genAI = new GoogleGenAI({ api<PERSON><PERSON> });
  }

  /**
   * Predicts content using Gemini AI model
   * @param content The input content to send to Gemini
   * @param systemInstruction Optional system instruction to shape the response
   * @param additionalConfig Optional additional config fields for responseMimeType and responseSchema
   * @returns The generated text from Gemini
   */
  async predict(
    content: string,
    systemInstruction?: string,
    additionalConfig?: {
      responseMimeType?: string;
      responseSchema?: any;
    },
  ): Promise<string> {
    try {
      const response = await this.genAI.models.generateContent({
        model: this.defaultModel,
        contents: content,
        config: {
          ...(systemInstruction && {
            systemInstruction,
          }),
          temperature: 1.0,
          maxOutputTokens: 8192,
          ...(additionalConfig &&
            additionalConfig.responseMimeType && {
              responseMimeType: additionalConfig.responseMimeType,
            }),
          ...(additionalConfig &&
            additionalConfig.responseSchema && {
              responseSchema: additionalConfig.responseSchema,
            }),
        },
      });

      if (!response.text) {
        throw new Error('No text generated by Gemini');
      }

      return response.text;
    } catch (error) {
      this.logger.error(
        `Error in Gemini prediction: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to predict with Gemini: ${error.message}`);
    }
  }

  /**
   * Translates text from one language to another using Gemini AI
   * @param text The text to translate
   * @param targetLanguage The target language code (e.g., 'fi', 'en', 'sv')
   * @param sourceLanguage Optional source language code. If not provided, auto-detect will be used
   * @returns The translated text
   */
  async translate(
    text: string,
    targetLanguage: string,
    sourceLanguage?: string,
  ): Promise<string> {
    if (!text || !targetLanguage) {
      throw new Error('Text and target language are required for translation');
    }

    this.logger.log(
      `Translating text from ${sourceLanguage || 'auto-detected'} to ${targetLanguage}`,
    );

    try {
      const systemInstruction = sourceLanguage
        ? `You are an expert Translator. You are tasked to translate documents from ${sourceLanguage} to ${targetLanguage}. Please provide an accurate translation of this document and return translation text only.`
        : `You are an expert Translator. You are tasked to translate documents to ${targetLanguage}. Please provide an accurate translation of this document and return translation text only.`;

      const translatedText = await this.predict(text, systemInstruction, {
        responseMimeType: 'text/plain',
      });

      this.logger.log(
        `Successfully translated text to ${targetLanguage}. Length: ${translatedText.length} chars`,
      );

      return translatedText.trim();
    } catch (error) {
      this.logger.error(
        `Error translating text: ${error.message}`,
        error.stack,
      );
      throw new Error(`Failed to translate text: ${error.message}`);
    }
  }

  /**
   * Extracts structured metadata from a job posting description
   * @param title Job title
   * @param description Job description text
   * @param language Language of the job posting (default: 'en')
   * @returns JobMetadataDto object with extracted metadata
   */
  async extractJobMetadata(
    title: string,
    description: string,
    language: string = 'en', // language parameter is available but not directly used in this Gemini call logic
  ): Promise<JobMetadataDto> {
    this.logger.log(`Extracting metadata from job posting: ${title}`);

    try {
      const systemInstructionText = `
You are an AI assistant specialized in job market analysis. Your task is to extract structured data from job postings.
Extract only information that is explicitly mentioned in the job description. Do not infer information that is not present.
If a field is not mentioned, leave it as null or omit it.

Format your response as a valid JSON object with the following fields:
- expires_at: Application deadline in ISO format (YYYY-MM-DD) if mentioned
- application_url: URL to apply for the job if mentioned
- workplace_flexibility: One of "remote", "hybrid", or "onsite" if mentioned
- salary_type: One of "hourly", "monthly", or "yearly" if mentioned
- shift_type: One of "day", "evening", "night", or "rotating" if mentioned
- part_time_hours: Hours for part-time positions if mentioned (as a string, e.g. "20h per week")
- work_begins: Expected start date in ISO format (YYYY-MM-DD) if mentioned
- driver_license_codes: Array of driver's license codes if mentioned
- salary_range: Object with min, max, currency, and period if salary information is mentioned
- years_of_experience: String describing the years of experience required (e.g., "3 years", "5+ years", "3-5 years")
- education_requirements: Array of education requirements if mentioned
- benefits: Array of benefits mentioned in the job posting
- travel_requirements: String describing travel requirements if mentioned
- language_requirements: Array of objects with language and level properties
- certifications: Array of required certifications if mentioned
- technical_skills: Array of technical skills mentioned
- soft_skills: Array of soft skills mentioned
- contact_information: Object with name, email and phone properties if mentioned
- confidence_scores: Object mapping field names to confidence scores (0.0-1.0)

Return only the JSON object with no additional text.
    `;

      const userPromptText = `
Job Title: ${title}

Job Description:
${description}
    `;

      // Define the response schema for structured extraction
      const responseSchema = {
        type: GoogleGenAiType.OBJECT,
        properties: {
          expires_at: { type: GoogleGenAiType.STRING, format: 'date-time' },
          application_url: { type: GoogleGenAiType.STRING },
          workplace_flexibility: {
            type: GoogleGenAiType.STRING,
            enum: ['remote', 'hybrid', 'onsite'],
          },
          salary_type: {
            type: GoogleGenAiType.STRING,
            enum: ['hourly', 'monthly', 'yearly'],
          },
          shift_type: {
            type: GoogleGenAiType.STRING,
            enum: ['day', 'evening', 'night', 'rotating'],
          },
          part_time_hours: { type: GoogleGenAiType.STRING },
          work_begins: { type: GoogleGenAiType.STRING, format: 'date-time' },
          driver_license_codes: {
            type: GoogleGenAiType.ARRAY,
            items: { type: GoogleGenAiType.STRING },
          },
          salary_range: {
            type: GoogleGenAiType.OBJECT,
            properties: {
              min: { type: GoogleGenAiType.NUMBER },
              max: { type: GoogleGenAiType.NUMBER },
              currency: { type: GoogleGenAiType.STRING },
              period: { type: GoogleGenAiType.STRING },
            },
          },
          years_of_experience: { type: GoogleGenAiType.STRING },
          education_requirements: {
            type: GoogleGenAiType.ARRAY,
            items: { type: GoogleGenAiType.STRING },
          },
          benefits: {
            type: GoogleGenAiType.ARRAY,
            items: { type: GoogleGenAiType.STRING },
          },
          travel_requirements: { type: GoogleGenAiType.STRING },
          language_requirements: {
            type: GoogleGenAiType.ARRAY,
            items: {
              type: GoogleGenAiType.OBJECT,
              properties: {
                language: { type: GoogleGenAiType.STRING },
                level: { type: GoogleGenAiType.STRING },
              },
            },
          },
          certifications: {
            type: GoogleGenAiType.ARRAY,
            items: { type: GoogleGenAiType.STRING },
          },
          technical_skills: {
            type: GoogleGenAiType.ARRAY,
            items: { type: GoogleGenAiType.STRING },
          },
          soft_skills: {
            type: GoogleGenAiType.ARRAY,
            items: { type: GoogleGenAiType.STRING },
          },
          contact_information: {
            type: GoogleGenAiType.OBJECT,
            properties: {
              name: { type: GoogleGenAiType.STRING },
              email: { type: GoogleGenAiType.STRING },
              phone: { type: GoogleGenAiType.STRING },
            },
          },
          confidence_scores: {
            type: GoogleGenAiType.OBJECT,
            properties: {
              expires_at: { type: GoogleGenAiType.NUMBER },
              application_url: { type: GoogleGenAiType.NUMBER },
              workplace_flexibility: { type: GoogleGenAiType.NUMBER },
              salary_type: { type: GoogleGenAiType.NUMBER },
              shift_type: { type: GoogleGenAiType.NUMBER },
              part_time_hours: { type: GoogleGenAiType.NUMBER },
              work_begins: { type: GoogleGenAiType.NUMBER },
              driver_license_codes: { type: GoogleGenAiType.NUMBER },
              salary_range: { type: GoogleGenAiType.NUMBER },
              years_of_experience: { type: GoogleGenAiType.NUMBER },
              education_requirements: { type: GoogleGenAiType.NUMBER },
              benefits: { type: GoogleGenAiType.NUMBER },
              travel_requirements: { type: GoogleGenAiType.NUMBER },
              language_requirements: { type: GoogleGenAiType.NUMBER },
              certifications: { type: GoogleGenAiType.NUMBER },
              technical_skills: { type: GoogleGenAiType.NUMBER },
              soft_skills: { type: GoogleGenAiType.NUMBER },
              contact_information: { type: GoogleGenAiType.NUMBER },
            },
          },
        },
      };

      // Request Gemini to generate the structured data, following the 'predict' method's pattern
      const result = await this.genAI.models.generateContent({
        model: this.defaultModel,
        contents: userPromptText, // Main prompt goes here
        config: {
          systemInstruction: systemInstructionText,
          temperature: 0.1, // Very low temperature for deterministic, fact-based extraction
          maxOutputTokens: 4096, // Or 8192 as in predict, adjust as needed
          responseMimeType: 'application/json',
          responseSchema: responseSchema,
        },
      });

      this.logger.log(`Gemini raw full response text: ${result.text}`);

      if (!result.text) {
        this.logger.error('No text content in Gemini response.', {
          response: result,
        });
        throw new Error(
          'Failed to extract JSON from Gemini response: No text content.',
        );
      }

      const rawJson = result.text;
      
      // Log only first 1000 chars to avoid massive logs
      const logPreview = rawJson.length > 1000 ? `${rawJson.substring(0, 1000)}... (truncated)` : rawJson;
      this.logger.log(`Gemini extracted JSON: ${logPreview}`);

      const jobMetadata = JSON.parse(rawJson) as JobMetadataDto;
      
      // Log successful extraction of key fields
      if (jobMetadata.expires_at) {
        this.logger.log(`Successfully extracted expires_at: ${jobMetadata.expires_at}`);
      }
      
      return jobMetadata;
    } catch (error) {
      this.logger.error(
        `Error extracting job metadata: ${error.message}`,
        error.stack,
      );
      // Return an empty object in case of errors to prevent failures in the import process
      return new JobMetadataDto();
    }
  }
}
