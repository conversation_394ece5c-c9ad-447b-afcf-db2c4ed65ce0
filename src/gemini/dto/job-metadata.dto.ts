import { ApiProperty } from '@nestjs/swagger';

/**
 * Data transfer object for job metadata extracted by Gemini AI
 */
export class JobMetadataDto {
  // Job entity fields
  @ApiProperty({
    description: 'Application deadline for the job',
    example: '2025-12-31',
    required: false,
  })
  expires_at?: string;

  @ApiProperty({
    description: 'URL to apply for the job',
    example: 'https://example.com/apply',
    required: false,
  })
  application_url?: string;

  @ApiProperty({
    description: 'Workplace flexibility information (remote/hybrid/onsite)',
    example: 'remote',
    required: false,
    enum: ['remote', 'hybrid', 'onsite'],
  })
  workplace_flexibility?: string;

  @ApiProperty({
    description: 'Type of salary (hourly/monthly/yearly)',
    example: 'monthly',
    required: false,
    enum: ['hourly', 'monthly', 'yearly'],
  })
  salary_type?: string;

  @ApiProperty({
    description: 'Type of shift (day/evening/night/rotating)',
    example: 'day',
    required: false,
    enum: ['day', 'evening', 'night', 'rotating'],
  })
  shift_type?: string;

  @ApiProperty({
    description: 'Hours for part-time positions',
    example: '20h per week',
    required: false,
  })
  part_time_hours?: string;

  @ApiProperty({
    description: 'Expected start date',
    example: '2025-01-15',
    required: false,
  })
  work_begins?: string;

  @ApiProperty({
    description: "Required driver's license codes",
    example: ['B', 'C'],
    required: false,
    type: [String],
  })
  driver_license_codes?: string[];

  // Additional metadata fields
  @ApiProperty({
    description: 'Estimated salary range',
    required: false,
    example: {
      min: 3000,
      max: 4500,
      currency: 'EUR',
      period: 'month',
    },
  })
  salary_range?: {
    min?: number;
    max?: number;
    currency?: string;
    period?: string;
  };

  @ApiProperty({
    description: 'Required years of experience',
    example: '3 years',
    required: false,
  })
  years_of_experience?: string;

  @ApiProperty({
    description: 'Education requirements',
    example: ["Bachelor's degree in Computer Science"],
    required: false,
    type: [String],
  })
  education_requirements?: string[];

  @ApiProperty({
    description: 'Company benefits mentioned in the job posting',
    example: ['Health insurance', 'Remote work options', 'Flexible hours'],
    required: false,
    type: [String],
  })
  benefits?: string[];

  @ApiProperty({
    description: 'Travel requirements mentioned in the job',
    example: '25% travel required',
    required: false,
  })
  travel_requirements?: string;

  @ApiProperty({
    description: 'Language requirements for the job',
    example: [
      { language: 'English', level: 'fluent' },
      { language: 'Finnish', level: 'basic' },
    ],
    required: false,
    type: [Object],
  })
  language_requirements?: {
    language: string;
    level: string;
  }[];

  @ApiProperty({
    description: 'Required certifications for the job',
    example: ['AWS Certified Developer', 'Scrum Master'],
    required: false,
    type: [String],
  })
  certifications?: string[];

  @ApiProperty({
    description: 'Technical skills mentioned in the job posting',
    example: ['Java', 'Spring Boot', 'SQL'],
    required: false,
    type: [String],
  })
  technical_skills?: string[];

  @ApiProperty({
    description: 'Soft skills mentioned in the job posting',
    example: ['Communication', 'Teamwork', 'Problem-solving'],
    required: false,
    type: [String],
  })
  soft_skills?: string[];

  @ApiProperty({
    description: 'Contact information extracted from the job posting',
    example: {
      name: 'John Doe',
      email: '<EMAIL>',
      phone: '+358 50 123 4567',
    },
    required: false,
  })
  contact_information?: {
    name?: string;
    email?: string;
    phone?: string;
  };

  @ApiProperty({
    description: 'Confidence scores for extracted metadata (0-1)',
    example: {
      expires_at: 0.95,
      application_url: 0.98,
      salary_range: 0.75,
    },
    required: false,
  })
  confidence_scores?: Record<string, number>;
}