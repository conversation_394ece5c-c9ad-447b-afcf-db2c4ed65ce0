import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CvWorkspaceController } from './controllers/cv-workspace.controller';
import { CvWorkspaceService } from './services/cv-workspace.service';
import { UserModule } from '../user/user.module';
import { JobsModule } from '../jobs/jobs.module';
import { Job } from '../../entities/job.entity';
import { JobLocation } from '../../entities/job-location.entity';
import { AuthModule } from '../../auth/auth.module';
import { CacheModule } from '../../cache/cache.module';
import { PdfService } from '../../util/PDFUtil';

@Module({
  imports: [
    ConfigModule,
    TypeOrmModule.forFeature([Job, JobLocation]),
    UserModule,
    JobsModule,
    AuthModule,
    CacheModule,
  ],
  controllers: [CvWorkspaceController],
  providers: [CvWorkspaceService, PdfService],
  exports: [CvWorkspaceService],
})
export class CvWorkspaceModule {}
