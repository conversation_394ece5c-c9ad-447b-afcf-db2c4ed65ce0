import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
  UnprocessableEntityException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  AugmentRequestType,
  DifyRequestDto,
  RelevanceResponseDto,
} from '../dto/augment.dto';
import { CvGenerationDto } from '../dto/cv-generation.dto'; // Import CvGenerationDto
import { UserApiService } from '../../user/services/user-api.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Job } from '../../../entities/job.entity';
import { JobLocation } from '../../../entities/job-location.entity';
import { DifyService } from '../../jobs/services/helpers/dify.service';
import { AuthService } from '../../../auth/services/auth.service';
import { normalizeJobLanguage } from 'src/app/jobs/services/utils/language-utils';
import { CacheService } from '../../../cache/cache.service';
import { Request } from 'express';
import { plainToInstance } from 'class-transformer'; // Import plainToInstance from class-transformer
import { validate } from 'class-validator'; // Import validate from class-validator
import { PdfService } from '../../../util/PDFUtil'; // Import PdfService
import { JobApplicationService } from '../../jobs/services/job-application.service';
import { JobApplicationDto } from '../../jobs/dtos/job-application.dto';

@Injectable()
export class CvWorkspaceService {
  private readonly logger = new Logger(CvWorkspaceService.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly userApiService: UserApiService,
    private readonly difyService: DifyService,
    private readonly authService: AuthService,
    private readonly cacheService: CacheService,
    private readonly pdfService: PdfService, // Inject PdfService
    private readonly jobApplicationService: JobApplicationService, // Inject JobApplicationService
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    @InjectRepository(JobLocation)
    private readonly jobLocationRepository: Repository<JobLocation>,
  ) {}

  /**
   * Augments CV content based on the request type and data
   * @param reqType The type of augmentation request (ABOUT_ME, RELEVANCE, CV, CL, FIT)
   * @param data The data to be augmented
   * @returns Augmented data as a Record<string, any>
   */
  async augmentContent(
    reqType: AugmentRequestType,
    data: Record<string, any>,
  ): Promise<Record<string, any>> {
    this.logger.log(`Augmenting content for request type: ${reqType}`);

    try {
      // Process the data based on request type
      switch (reqType) {
        case AugmentRequestType.ABOUT_ME:
          return await this.augmentAboutMe(data);
        case AugmentRequestType.RELEVANCE:
          return await this.augmentRelevance(data);
        case AugmentRequestType.CV:
          return await this.augmentCV(data);
        case AugmentRequestType.CL:
          return await this.augmentCoverLetter(data);
        case AugmentRequestType.FIT:
          return await this.augmentFit(data);
        default:
          throw new Error(`Unsupported request type: ${reqType}`);
      }
    } catch (error) {
      this.logger.error(
        `Error augmenting content for request type ${reqType}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Augments the "About Me" section
   * @param data Input data for augmentation
   * @returns Augmented "About Me" content
   */
  private async augmentAboutMe(
    data: Record<string, any>,
  ): Promise<Record<string, any>> {
    // Implementation for "About Me" augmentation
    // In a real implementation, this might call an LLM API or use a specific algorithm

    // For now, returning a placeholder response
    return {
      augmentedContent: 'Enhanced "About Me" content would appear here',
      suggestions: [
        'Consider adding more details about your technical skills',
        'Highlight your most significant achievements',
      ],
    };
  }

  /**
   * Augments content for relevance to a job
   * @param data Input data containing job extId and session token
   * @returns Augmented relevance content with score, strong points, and improvement areas
   */
  private async augmentRelevance(
    data: Record<string, any>,
  ): Promise<RelevanceResponseDto> {
    this.logger.log(`Augmenting relevance for job: ${data.extId}`);

    // Validate extId presence
    if (!data.extId) {
      throw new BadRequestException('Job external ID (extId) is required');
    }

    // Fetch job by extId early to fail fast if job doesn't exist
    const job = await this.jobRepository.findOne({
      where: { ext_id: data.extId },
      relations: ['employer'],
    });

    // Return early if job doesn't exist
    if (!job) {
      throw new NotFoundException(
        `Job with external ID ${data.extId} not found`,
      );
    }

    // Get session token from data (passed from controller)
    const sessionToken = data.sessionToken;
    if (!sessionToken) {
      throw new BadRequestException('Session token is required');
    }

    // Get user ID from session token
    const sessionInfo = await this.authService.getUserSessionInfo({
      headers: {
        authorization: `Bearer ${sessionToken}`,
      },
    } as unknown as Request);

    if (!sessionInfo.userId) {
      throw new BadRequestException(
        'Unable to identify user from session token',
      );
    }

    const userId = sessionInfo.userId;
    this.logger.log(`Retrieved user ID: ${userId} from session token`);

    // Create cache key
    const cacheKey = `relevance:${userId}:${data.extId}`;

    // Try to get from cache first
    try {
      const cachedResponse =
        await this.cacheService.get<RelevanceResponseDto>(cacheKey);
      if (cachedResponse) {
        this.logger.log(`Cache hit for ${cacheKey}`);
        return cachedResponse;
      } else {
        this.logger.log(`Cache miss for ${cacheKey}`);
      }
    } catch (cacheError) {
      this.logger.error(
        `Failed to retrieve from cache: ${cacheError.message}`,
        cacheError.stack,
      );
    }

    this.logger.log(`Generating new response for ${cacheKey}`);

    try {
      // Get user's smart profile
      const smartProfile = await this.userApiService.getUserSmartProfile(
        sessionToken,
        false,
      );

      // Cache the user's smart profile with 30-minute TTL
      try {
        const profileCacheKey = `smartProfile:${userId}`;
        await this.cacheService.set(profileCacheKey, smartProfile.data, '30m');
        this.logger.log(
          `Cached smart profile at ${profileCacheKey} with 30m TTL`,
        );
      } catch (cacheError) {
        this.logger.error(
          `Failed to cache smart profile: ${cacheError.message}`,
          cacheError.stack,
        );
        // Continue even if caching fails
      }

      // Format instructions
      const instructions = {
        language: normalizeJobLanguage(data.selectedLanguage ?? 'en'), // Default to English, can be made dynamic
      };

      // Create formatted data for Dify API
      const difyRequest = this.createDifyRequest(
        job,
        smartProfile.data,
        instructions,
      );

      // Call Dify API
      const difyKey = this.configService.get<string>('DIFY_AUGMENT_CLIENT_KEY');
      if (!difyKey) {
        throw new Error('DIFY_AUGMENT_CLIENT_KEY is not configured');
      }

      const response = (await this.difyService.callDifyApi(
        '', // This parameter isn't used with inputs, but is required
        {
          apiKey: difyKey,
          responseProperty: 'relevance',
          errorMessage: 'Failed to analyze job relevance',
          fallbackMessage: 'Unable to determine job relevance',
        },
        difyRequest,
        true,
        userId,
      )) as Record<string, any>;

      this.logger.log(
        `Relevance response: ${JSON.stringify(response, null, 2)}`,
      );

      // Transform the response
      const responseDto = {
        relevanceScore: response.relevance_score || 0,
        breakdown: response.breakdown || [],
        // Only include improvementAreas if provided by the API
        ...(response.improvement_areas && {
          improvementAreas: response.improvement_areas,
        }),
      };

      // Cache the response with 24-hour TTL (86400 seconds)
      try {
        await this.cacheService.set(cacheKey, responseDto, '24h'); // Using string format for time
        this.logger.log(`Cached response at ${cacheKey} with 24h TTL`);
      } catch (cacheError) {
        this.logger.error(
          `Failed to cache response: ${cacheError.message}`,
          cacheError.stack,
        );
        // Continue even if caching fails - don't block the response
      }

      return responseDto;
    } catch (error) {
      this.logger.error(
        `Error augmenting relevance for job ${data.extId}: ${error.message}`,
        error.stack,
      );

      // Return a default response in case of error
      return {
        relevanceScore: 0,
        breakdown: [{ Error: 'Could not analyze profile against this job' }],
      };
    }
  }

  /**
   * Creates a formatted request for the Dify API
   * @param job The job entity
   * @param smartProfile The user's smart profile
   * @returns Formatted DifyRequestDto
   */
  private createDifyRequest(
    job: Job,
    smartProfile: any,
    instructions: any,
    reqType: AugmentRequestType = AugmentRequestType.RELEVANCE,
  ): DifyRequestDto {
    // Format job information
    const jobInfo = {
      job_description: job.description || '',
      company_name: job.employer?.name || job.employer_name || 'Unknown',
      job_title: job.title || '',
    };

    // Format user information (extract relevant parts from smart profile)
    const userInfo = {
      // Contact information
      firstName: smartProfile.firstName || null,
      lastName: smartProfile.lastName || null,
      email: smartProfile.email || null,
      phone: smartProfile.phone || null,
      // Skills and experience
      skills: smartProfile.skills || [],
      work_experience: smartProfile.workExperiences || [],
      education: smartProfile.educations || [],
      certifications: smartProfile.certifications || [],
      hobbies: smartProfile.hobbies || [],
      interests: smartProfile.interests || [],
      languages: smartProfile.languages || [],
    };

    // Create and return the Dify request
    return {
      action: reqType,
      job_info: JSON.stringify(jobInfo),
      user_info: JSON.stringify(userInfo),
      instructions: JSON.stringify(instructions),
    };
  }

  /**
   * Augments CV content
   * @param data Input data for augmentation
   * @returns Augmented CV content
   */
  private async augmentCV(
    data: Record<string, any>,
  ): Promise<Record<string, any>> {
    // Extract CV generation data using the CvGenerationDto
    const cvGenerationData = plainToInstance(CvGenerationDto, data);

    // Validate the extracted data
    const errors = await validate(cvGenerationData);
    if (errors.length > 0) {
      this.logger.error('Invalid CV generation data', errors);
      throw new BadRequestException('Invalid CV generation data');
    }

    this.logger.log(`Augmenting CV content for ${cvGenerationData.extId}`);

    // Get session token from data (passed from controller)
    const sessionToken = data.sessionToken;
    if (!sessionToken) {
      throw new BadRequestException('Session token is required');
    }

    // If job extId is provided, validate it exists
    let job: Job | null = null;
    if (cvGenerationData.extId) {
      // Fetch job by extId to fail fast if job doesn't exist
      job = await this.jobRepository.findOne({
        where: { ext_id: cvGenerationData.extId },
        relations: ['employer'],
      });

      // Return early if job doesn't exist
      if (!job) {
        throw new NotFoundException(
          `Job with external ID ${cvGenerationData.extId} not found`,
        );
      }
    }

    // Get user ID from session token
    const sessionInfo = await this.authService.getUserSessionInfo({
      headers: {
        authorization: `Bearer ${sessionToken}`,
      },
    } as unknown as Request);

    if (!sessionInfo.userId) {
      throw new BadRequestException(
        'Unable to identify user from session token',
      );
    }

    const userId = sessionInfo.userId;
    this.logger.log(`Retrieved user ID: ${userId} from session token`);

    // Try to get cached smart profile
    let smartProfile: any = null;
    try {
      const profileCacheKey = `smartProfile:${userId}`;
      smartProfile = await this.cacheService.get(profileCacheKey);
      if (smartProfile) {
        this.logger.log(`Cache hit for smart profile at ${profileCacheKey}`);
      } else {
        this.logger.log(`Cache miss for smart profile at ${profileCacheKey}`);
        // Fetch smart profile if not in cache
        const profileResponse = await this.userApiService.getUserSmartProfile(
          sessionToken,
          false,
        );
        smartProfile = profileResponse.data;

        // Cache the newly fetched smart profile
        await this.cacheService.set(profileCacheKey, smartProfile, '30m');
        this.logger.log(
          `Cached newly fetched smart profile at ${profileCacheKey} with 30m TTL`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error getting smart profile: ${error.message}`,
        error.stack,
      );
      // Continue even if there's an error getting the smart profile
    }

    // Use the validated DTO properties
    const language = normalizeJobLanguage(
      cvGenerationData.selectedLanguage ?? 'en',
    );

    // Add normalized language to cvGenerationData
    cvGenerationData.language = language;

    // Format instructions based on selected language
    const instructions = {
      language: language,
      // documentType: cvGenerationData.documentType,
      // template: cvGenerationData.selectedTemplate,
      sections: cvGenerationData.sections.map((section) => {
        // Create a new object without the isRequired property
        const { isRequired, ...sectionWithoutIsRequired } = section;
        return sectionWithoutIsRequired;
      }),
    };

    // Implementation for CV augmentation using the validated data
    // Now we can use smartProfile data if available

    // Only call Dify if we have a smart profile
    if (!smartProfile) {
      this.logger.warn('No smart profile available for CV augmentation');
      return {};
    }

    try {
      // Create formatted data for Dify API - only pass job if available
      const difyRequest = this.createDifyRequest(
        job!,
        smartProfile,
        instructions,
        AugmentRequestType.CV,
      );

      // Call Dify API
      const difyKey = this.configService.get<string>('DIFY_AUGMENT_CLIENT_KEY');
      if (!difyKey) {
        throw new Error('DIFY_AUGMENT_CLIENT_KEY is not configured');
      }

      const response = (await this.difyService.callDifyApi(
        '', // This parameter isn't used with inputs, but is required
        {
          apiKey: difyKey,
          responseProperty: 'cv',
          errorMessage: 'Failed to generate CV content',
          fallbackMessage: 'Unable to generate CV content',
        },
        difyRequest,
        true,
        userId,
      )) as Record<string, any>;

      // Check if response is empty
      if (
        !response ||
        (typeof response === 'string' && (response as string).trim() === '') ||
        (Array.isArray(response) && response.length === 0) ||
        (typeof response === 'object' && Object.keys(response).length === 0)
      ) {
        throw new UnprocessableEntityException({
          message: 'Failed to generate CV content - received empty response',
          details: 'The CV generation service returned an empty response',
        });
      }

      // Process the response
      // Extract the CV content from the response
      this.logger.log(`CV raw generation response: ${response}`);
      const rawResponse = Array.isArray(response)
        ? response.join('\n')
        : response;

      console.log('\n\n');

      // Clean up the response by removing any JSON or HTML code block markers
      const generatedCV = rawResponse
        .replace(/```html\s*/g, '')
        .replace(/```\s*$/g, '')
        .replace(/\\"/g, "'") // Replace escaped quotes with single quotes for HTML attributes
        .trim();
      this.logger.log(`CV generation response: ${generatedCV}`);

      // Convert HTML to PDF using PDFBolt
      const pdfResult = await this.pdfService.convertHtmlToPdf(
        generatedCV,
        'generated-cv.pdf',
      );

      // Save the job application
      try {
        // Create job application DTO
        const jobApplicationData: JobApplicationDto = {
          userId: userId,
          cvHtml: generatedCV, // Store the HTML directly
          cvUrl: pdfResult.s3Url, // Use the S3 URL for storage
          jobExtId: cvGenerationData.extId,
          selectedLanguage: cvGenerationData.selectedLanguage,
          language: cvGenerationData.language,
          documentType: cvGenerationData.documentType,
          selectedTemplate: cvGenerationData.selectedTemplate,
          sections: cvGenerationData.sections,
          generatedAboutMe: cvGenerationData.generatedAboutMe,
          keyFitPoints: cvGenerationData.keyFitPoints,
        };

        // Check if user has already applied to this job
        const hasApplied = await this.jobApplicationService.hasUserAppliedToJob(
          userId,
          cvGenerationData.extId,
        );

        // If the user hasn't applied, save the application
        if (!hasApplied) {
          this.logger.log(
            `Saving job application for user ${userId} to job ${cvGenerationData.extId}`,
          );
          await this.jobApplicationService.createJobApplication(
            jobApplicationData,
          );
        } else {
          this.logger.log(
            `User ${userId} has already applied to job ${cvGenerationData.extId}`,
          );
        }
      } catch (error) {
        // Log error but don't fail the entire operation
        this.logger.error(
          `Error saving job application: ${error.message}`,
          error.stack,
        );
        // We continue with returning CV data even if application saving fails
      }

      // Return augmented content
      return { cvHtml: generatedCV, cvUrl: pdfResult.s3Url };
    } catch (error) {
      this.logger.error(
        `Error in CV augmentation: ${error.message}`,
        error.stack,
      );

      // Throw an appropriate HTTP exception instead of returning an error object
      throw new UnprocessableEntityException({
        message: 'An error occurred during CV generation',
        details: error.message,
      });
    }
  }

  /**
   * Augments cover letter content
   * @param data Input data for augmentation
   * @returns Augmented cover letter content
   */
  private async augmentCoverLetter(
    data: Record<string, any>,
  ): Promise<Record<string, any>> {
    // Implementation for cover letter augmentation

    return {
      augmentedContent: 'Enhanced cover letter content would appear here',
      suggestions: [
        'Tailor the introduction more specifically to the company',
        'Connect your experience more directly to the job requirements',
      ],
    };
  }

  /**
   * Augments fit assessment between candidate and job
   * @param data Input data for augmentation
   * @returns Augmented fit assessment
   */
  private async augmentFit(
    data: Record<string, any>,
  ): Promise<Record<string, any>> {
    // Implementation for fit assessment

    return {
      overallFit: 'strong',
      cultureFit: 'medium',
      skillsFit: 'high',
      experienceFit: 'high',
      recommendations: [
        'Emphasize team collaboration experiences',
        'Highlight adaptability to company work culture',
      ],
    };
  }
}
