import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsObject } from 'class-validator';

/**
 * Enum for request types in the CV augmentation process
 */
export enum AugmentRequestType {
  ABOUT_ME = 'ABOUT_ME',
  RELEVANCE = 'RELEVANCE',
  CV = 'CV',
  CL = 'CL',
  FIT = 'FIT',
}

/**
 * DTO for CV augmentation requests
 */
export class AugmentRequestDto {
  @ApiProperty({
    description: 'The type of augmentation request',
    enum: AugmentRequestType,
    example: AugmentRequestType.ABOUT_ME,
  })
  @IsEnum(AugmentRequestType)
  @IsNotEmpty()
  reqType: AugmentRequestType;

  @ApiProperty({
    description: 'The data to be augmented',
    example: {
      experience: 'I have worked as a software developer for 5 years.',
      skills: ['JavaScript', 'TypeScript', 'React', 'Node.js'],
    },
  })
  @IsObject()
  @IsNotEmpty()
  data: Record<string, any>;
}

/**
 * DTO for Relevance augmentation request data
 */
export class RelevanceRequestDto {
  @ApiProperty({
    description: 'External ID of the job to check relevance against',
    example: 'job-123456',
  })
  @IsNotEmpty()
  extId: string;
}

/**
 * DTO for Dify API requests
 */
export class DifyRequestDto {
  @ApiProperty({
    description: 'Action to perform',
    example: 'analyze_relevance',
  })
  action: string;

  @ApiProperty({
    description: 'JSON string containing job information',
    example: '{"job_description":"Software Engineer position...","company_name":"Tech Corp","job_title":"Senior Developer"}',
  })
  job_info: any;

  @ApiProperty({
    description: 'JSON string containing user profile information',
    example: '{"summary":"Experienced developer...","skills":["JavaScript","TypeScript"]}',
  })
  user_info: any;

  @ApiProperty({
    description: 'JSON string containing instruction details',
    example: '{"language":"en","sections":["all"],"style":"professional"}',
  })
  instructions: any;
}

/**
 * DTO for job relevance response
 */
export class RelevanceResponseDto {
  @ApiProperty({
    description: 'Relevance score from 0-100',
    example: 85,
  })
  relevanceScore: number;

  @ApiProperty({
    description: 'Breakdown of profile matching against job requirements',
    example: [
      {
        'Technical skills match': 'Your profile shows strong technical skills in the required areas.',
      },
      {
        'Experience level': 'You have sufficient experience for this role.',
      },
    ],
  })
  breakdown: Record<string, string>[];

  @ApiProperty({
    description: 'Areas for improvement to better match the job',
    example: ['Consider adding more domain-specific experiences'],
    required: false,
  })
  improvementAreas?: string[];
}

/**
 * DTO for CV augmentation responses
 */
export class AugmentResponseDto {
  @ApiProperty({
    description: 'Indicates whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'The augmented data',
    example: {
      augmentedText: 'I am a seasoned software developer with 5 years of experience...',
      suggestions: ['Add more details about your React projects', 'Highlight your leadership experience'],
    },
  })
  data: Record<string, any>;

  @ApiProperty({
    description: 'A descriptive message about the operation',
    example: 'CV content successfully augmented',
  })
  message: string;

  @ApiProperty({
    description: 'The timestamp of the response',
    example: '2025-03-24T03:19:28.000Z',
  })
  timestamp: string;
}
