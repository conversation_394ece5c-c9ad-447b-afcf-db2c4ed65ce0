import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum for document types in CV generation
 */
export enum DocumentType {
  CV_ONLY = 'cvOnly',
  CL_ONLY = 'clOnly',
  CV_AND_CL = 'cvAndCl',
}

/**
 * Enum for CV generation steps
 */
export enum CvGenerationStep {
  LANGUAGE_AND_OPTIONS = 'languageAndOptions',
  TEMPLATE_SELECTION = 'templateSelection',
  SECTION_CUSTOMIZATION = 'sectionCustomization',
  REVIEW = 'review',
  GENERATION = 'generation',
}

/**
 * DTO for CV section configuration
 */
export class CvSectionDto {
  @ApiProperty({
    description: 'Unique identifier for the section',
    example: 'experience',
  })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Display name for the section',
    example: 'Work Experience',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'Indicates if the section is required',
    example: true,
  })
  @IsBoolean()
  isRequired: boolean;

  @ApiProperty({
    description: 'Indicates if the section is enabled',
    example: true,
  })
  @IsBoolean()
  isEnabled: boolean;

  @ApiProperty({
    description: 'Order of the section in the document',
    example: 2,
  })
  @IsNumber()
  order: number;
}

/**
 * DTO for CV key fit points
 */
export class KeyFitPointDto {
  @ApiProperty({
    description: 'Description of the fit point',
    example: 'Your experience with React matches the job requirements',
  })
  @IsString()
  description: string;
}

/**
 * DTO for CV generation request/state
 */
export class CvGenerationDto {
  @ApiProperty({
    description: 'External ID for the CV generation',
    example: '013c9f4a-54ea-4b65-9e97-1fbf1c4a4606',
  })
  @IsString()
  @IsNotEmpty()
  extId: string;

  @ApiProperty({
    description: 'Current step in the CV generation process',
    example: CvGenerationStep.LANGUAGE_AND_OPTIONS,
    enum: CvGenerationStep,
  })
  @IsEnum(CvGenerationStep)
  currentStep: string;

  @ApiProperty({
    description: 'Selected language for the CV',
    example: 'en',
  })
  @IsString()
  @IsNotEmpty()
  selectedLanguage: string;

  @ApiProperty({
    description: 'Normalized language name (Finnish, Swedish, English)',
    example: 'English',
    required: false,
  })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiProperty({
    description: 'Type of document to generate',
    example: DocumentType.CV_ONLY,
    enum: DocumentType,
  })
  @IsEnum(DocumentType)
  documentType: string;

  @ApiProperty({
    description: 'Selected CV template',
    example: 'Modern Minimal',
  })
  @IsString()
  @IsNotEmpty()
  selectedTemplate: string;

  @ApiProperty({
    description: 'Sections to include in the CV',
    type: [CvSectionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CvSectionDto)
  sections: CvSectionDto[];

  @ApiProperty({
    description: 'AI-generated About Me section content',
    example: 'I am a passionate software developer with over 5 years of experience...',
    required: false,
  })
  @IsString()
  @IsOptional()
  generatedAboutMe: string;

  @ApiProperty({
    description: 'Key points highlighting fit with job position',
    type: [KeyFitPointDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => KeyFitPointDto)
  @IsOptional()
  keyFitPoints: KeyFitPointDto[];
}
