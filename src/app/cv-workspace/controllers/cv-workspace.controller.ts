import {
  Body,
  Controller,
  Logger,
  Post,
  Req,
  UnprocessableEntityException,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import {
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { CvWorkspaceService } from '../services/cv-workspace.service';
import { AugmentRequestDto, AugmentResponseDto } from '../dto/augment.dto';
import { AuthGuard } from '../../../auth/guards/auth.guard';
import { Request } from 'express';

@ApiTags('CV Workspace')
@Controller('v1/cv-workspace')
@UseGuards(AuthGuard)
export class CvWorkspaceController {
  private readonly logger = new Logger(CvWorkspaceController.name);

  constructor(private readonly cvWorkspaceService: CvWorkspaceService) {}

  /**
   * Augments CV content based on the request type and data
   *
   * This endpoint processes different types of CV content augmentation requests,
   * including About Me sections, relevance assessments, CV/resume content,
   * cover letters, and job fit analyses.
   *
   * @param requestDto The request containing the data to be augmented and the request type
   * @returns Augmented content in JSON format
   */
  @Post('augment')
  @ApiOperation({
    summary: 'Augment CV content',
    description: 'Processes and enhances CV content based on the request type',
  })
  @ApiOkResponse({
    description: 'Content successfully augmented',
    type: AugmentResponseDto,
    schema: {
      example: {
        success: true,
        data: {
          augmentedContent: 'Enhanced content...',
          suggestions: [
            'Add more details about your technical skills',
            'Highlight your achievements with measurable results',
          ],
        },
        message: 'Content successfully augmented',
        timestamp: '2025-03-24T05:19:28+02:00',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input format',
    schema: {
      example: {
        statusCode: 400,
        message: [
          'reqType must be a valid enum value',
          'data must be an object',
        ],
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error',
    schema: {
      example: {
        success: false,
        data: {},
        message: 'Failed to augment content: Service unavailable',
        timestamp: '2025-03-24T05:19:28+02:00',
      },
    },
  })
  async augmentContent(
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
        transform: true,
      }),
    )
    requestDto: AugmentRequestDto,
    @Req() request: Request,
  ): Promise<AugmentResponseDto> {
    this.logger.log(`Received augment request of type: ${requestDto.reqType}`);

    try {
      // Extract session token from request
      const sessionToken = this.extractSessionToken(request);

      // Add session token to data for the service to use
      const dataWithSession = {
        ...requestDto.data,
        sessionToken,
      };

      // Call the service to process the augmentation
      const augmentedData = await this.cvWorkspaceService.augmentContent(
        requestDto.reqType,
        dataWithSession,
      );

      // Return successful response
      return {
        success: true,
        data: augmentedData,
        message: 'Content successfully augmented',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error augmenting content: ${error.message}`,
        error.stack,
      );

      // Return error response
      throw new UnprocessableEntityException(
        `Failed to augment content: ${error.message}`,
      );
    }
  }

  /**
   * Extract the session token from the request's Authorization header
   * @param request Express request object
   * @returns Session token string
   */
  private extractSessionToken(request: Request): string {
    const authHeader = request.headers.authorization;
    if (!authHeader) {
      throw new Error('No authorization header found');
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      throw new Error('Invalid authorization header format');
    }

    return parts[1];
  }
}
