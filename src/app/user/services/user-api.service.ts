import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { SmartProfileResponseDto } from './smart-profile-response.dto';

interface UserApiConfig {
  apiDomain: string;
}

/**
 * Keywords data from the user's smart profile
 */
export interface KeywordsDataDto {
  /**
   * List of keywords associated with the user profile
   * @example ['typescript', 'react', 'nestjs']
   */
  keywords: string[];

  /**
   * List of skill contexts associated with the keywords
   * @example ['web development', 'frontend', 'backend']
   */
  skill_contexts: string[];
}

/**
 * Response structure for keywords data
 */
export interface KeywordsDataResponseDto {
  /**
   * Indicates if the operation was successful
   * @example true
   */
  success: boolean;

  /**
   * The keywords data
   */
  data: KeywordsDataDto;

  /**
   * Message describing the result of the operation
   * @example 'Keywords data retrieved successfully'
   */
  message: string;

  /**
   * Timestamp when the response was generated
   * @example '2025-03-15T14:22:46+02:00'
   */
  timestamp: string;
}

/**
 * Service for communicating with the User API
 * Uses the Authorization header from the supertoken session
 *
 * Security note: For MVP, we're directly passing the session token to external services.
 * In a production environment, consider using a more secure approach like:
 * - Service-to-service authentication with dedicated API keys
 * - Token exchange to limit the scope of the forwarded token
 */
@Injectable()
export class UserApiService {
  private readonly logger = new Logger(UserApiService.name);
  private readonly config: UserApiConfig;

  constructor(private readonly configService: ConfigService) {
    this.config = this.getConfig();
  }

  private getConfig(): UserApiConfig {
    return {
      apiDomain: this.configService.getOrThrow('API_DOMAIN'),
    };
  }

  /**
   * Make a request to the User API
   * @param session The session container from supertoken
   * @param path The API path (without leading slash)
   * @param method The HTTP method
   * @param body The request body (for POST, PUT, PATCH)
   * @returns The response data
   */
  async request<T>(
    session: string,
    path: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH' = 'GET',
    body?: unknown,
  ): Promise<T> {
    const url = `${this.config.apiDomain}/${path}`;

    try {
      // Get the session token
      const authHeader = `Bearer ${session}`;

      const options: AxiosRequestConfig = {
        method,
        url,
        headers: {
          'Content-Type': 'application/json',
          Authorization: authHeader,
        },
      };

      if (body && ['POST', 'PUT', 'PATCH'].includes(method)) {
        options.data = body;
      }

      const response: AxiosResponse<T> = await axios(options);
      return response.data;
    } catch (error) {
      this.logger.error(
        `Error calling User API: ${error.message}`,
        error.stack,
      );

      if (axios.isAxiosError(error)) {
        const status = error.response?.status;
        const statusText = error.response?.statusText;
        const responseData = error.response?.data;

        this.logger.error(
          `API Error Details: Status ${status} ${statusText}`,
          JSON.stringify(responseData),
        );
      }

      throw error;
    }
  }

  /**
   * Get the current user's profile
   * @param session The session container from supertoken
   * @returns The user profile data
   */
  async getUserProfile<T>(session: string): Promise<T> {
    return this.request<T>(session, 'users/profile');
  }

  /**
   * Update the current user's profile
   * @param session The session container from supertoken
   * @param data The profile data to update
   * @returns The updated user profile
   */
  async updateUserProfile<T>(session: string, data: unknown): Promise<T> {
    return this.request<T>(session, 'users/profile', 'PUT', data);
  }

  /**
   * Get the user's preferences
   * @param session The session container from supertoken
   * @returns The user preferences
   */
  async getUserPreferences<T>(session: string): Promise<T> {
    return this.request<T>(session, 'users/preferences');
  }

  /**
   * Update the user's preferences
   * @param session The session container from supertoken
   * @param data The preferences data to update
   * @returns The updated user preferences
   */
  async updateUserPreferences<T>(session: string, data: unknown): Promise<T> {
    return this.request<T>(session, 'users/preferences', 'PUT', data);
  }

  /**
   * Get the user's keywords data from smart profile
   * @param session The session container from supertoken
   * @returns The keywords data response containing keywords and skill contexts
   */
  async getUserKeywordsData(session: string): Promise<KeywordsDataResponseDto> {
    return this.request<KeywordsDataResponseDto>(
      session,
      'users/me/keywords-data',
    );
  }

  /**
   * Get the authenticated user's smart profile
   * @param session The session container from supertoken
   * @param overview Optional flag to include profile overview (default: false)
   * @returns The user's smart profile data
   */
  async getUserSmartProfile(
    session: string,
    overview: boolean = false,
  ): Promise<SmartProfileResponseDto> {
    const queryParams = overview ? '?overview=true' : '?overview=false';
    return this.request<SmartProfileResponseDto>(
      session,
      `users/me/smart-profile${queryParams}`,
    );
  }
}
