import { ApiProperty } from '@nestjs/swagger';
import { SkillType } from '../../../entities/skill-type.enum';

/**
 * DTO representing a skill in the user's profile
 */
export class SkillDto {
  @ApiProperty({
    description: 'Name of the skill',
    example: 'JavaScript',
  })
  name: string;

  @ApiProperty({
    description: 'Proficiency level of the skill',
    example: 'advanced',
  })
  proficiency: string;

  @ApiProperty({
    description: 'Type of skill (TECHNICAL, SOFT, DOMAIN, PRACTICAL)',
    example: 'TECHNICAL',
  })
  type: SkillType;

  @ApiProperty({
    description: 'Flag indicating if this is an ESCO-registered skill',
    example: true,
  })
  isEscoSkill: boolean;
}

/**
 * DTO representing a work experience in the user's profile
 */
export class WorkExperienceDto {
  @ApiProperty({
    description: 'ID of the work experience record',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Company or organization name',
    example: 'Acme Corporation',
  })
  company: string;

  @ApiProperty({
    description: 'Job title or position held',
    example: 'Senior Software Engineer',
  })
  title: string;

  @ApiProperty({
    description: 'Start date of employment',
    example: '2020-01-15T00:00:00.000Z',
  })
  startDate: string;

  @ApiProperty({
    description: 'End date of employment (null if current position)',
    example: '2023-05-30T00:00:00.000Z',
    required: false,
  })
  endDate: string | null;

  @ApiProperty({
    description: 'Brief description of the role',
    example: 'Led development of cloud infrastructure',
  })
  description: string;

  @ApiProperty({
    description: 'List of job responsibilities',
    example: ['Led a team of 5 developers', 'Implemented CI/CD pipeline'],
    type: [String],
  })
  responsibilities: string[];

  @ApiProperty({
    description: 'List of notable achievements',
    example: ['Increased system performance by 40%', 'Reduced bug count by 60%'],
    type: [String],
  })
  achievements: string[];
}

/**
 * DTO representing an education record in the user's profile
 */
export class EducationDto {
  @ApiProperty({
    description: 'ID of the education record',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'ID of the educational institution',
    example: 123,
    required: false,
  })
  institutionId: number | null;

  @ApiProperty({
    description: 'Name of the educational institution',
    example: 'Stanford University',
  })
  institution: string;

  @ApiProperty({
    description: 'Name of the educational institution (duplicate for frontend compatibility)',
    example: 'Stanford University',
  })
  institutionName: string;

  @ApiProperty({
    description: 'Type of degree earned',
    example: 'Bachelor of Science',
  })
  degree: string;

  @ApiProperty({
    description: 'Field of study',
    example: 'Computer Science',
  })
  fieldOfStudy: string;

  @ApiProperty({
    description: 'Start date of education',
    example: '2018-09-01T00:00:00.000Z',
  })
  startDate: string;

  @ApiProperty({
    description: 'End date of education (null if still studying)',
    example: '2022-06-15T00:00:00.000Z',
    required: false,
  })
  endDate: string | null;

  @ApiProperty({
    description: 'Description of the education',
    example: 'Specialized in artificial intelligence and machine learning',
  })
  description: string;
}

/**
 * DTO representing a certification in the user's profile
 */
export class CertificationDto {
  @ApiProperty({
    description: 'Unique identifier of the certification',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'Title or name of the certification',
    example: 'AWS Certified Solutions Architect',
  })
  title: string;

  @ApiProperty({
    description: 'Organization that issued the certification',
    example: 'Amazon Web Services',
  })
  organization: string;

  @ApiProperty({
    description: 'Date when the certification was issued',
    example: '2022-03-15T00:00:00.000Z',
  })
  issueDate: string;

  @ApiProperty({
    description: 'Date when the certification expires (null if no expiry)',
    example: '2025-03-15T00:00:00.000Z',
    required: false,
  })
  expiryDate: string | null;

  @ApiProperty({
    description: 'Unique identifier for the credential',
    example: 'CERT-123456',
    required: false,
  })
  credentialId: string | null;

  @ApiProperty({
    description: 'URL to verify the credential',
    example: 'https://example.com/verify/CERT-123456',
    required: false,
  })
  credentialUrl: string | null;
}

/**
 * DTO representing the data portion of the smart profile response
 */
export class SmartProfileDataDto {
  @ApiProperty({
    description: 'ID of the user this profile belongs to',
    example: 1,
  })
  userId: number;

  @ApiProperty({
    description: "URL to the user's profile picture",
    example: 'https://example.com/profile.jpg',
    required: false,
  })
  profilePictureUrl: string | null;

  @ApiProperty({
    description: "User's current occupation",
    example: 'Software Engineer',
    required: false,
  })
  occupation: string | null;

  @ApiProperty({
    description: 'ID of the occupation in the ESCO database',
    example: 2512,
    required: false,
  })
  occupationId: number | null;

  @ApiProperty({
    description: "ID or code of the user's current occupation",
    example: "2512.1",
    required: false,
  })
  occupationCode?: string | null;

  @ApiProperty({
    description: "IDs or codes of the user's other preferred occupations",
    example: ["2513.3", "2519.4"],
    required: false,
    type: [String],
  })
  otherOccupationCodes?: string[] = [];

  @ApiProperty({
    description: 'Whether the user is a migrant',
    example: false,
  })
  isMigrant: boolean;

  @ApiProperty({
    description: 'List of skills the user has',
    type: [SkillDto],
  })
  skills: SkillDto[];

  @ApiProperty({
    description: 'List of work experiences',
    type: [WorkExperienceDto],
  })
  workExperiences: WorkExperienceDto[];

  @ApiProperty({
    description: 'Count of work experiences (only present in overview mode)',
    example: 3,
    required: false,
  })
  workExperiencesCount?: number;

  @ApiProperty({
    description: 'List of education history',
    type: [EducationDto],
  })
  educations: EducationDto[];

  @ApiProperty({
    description: 'Count of educations (only present in overview mode)',
    example: 2,
    required: false,
  })
  educationsCount?: number;

  @ApiProperty({
    description: 'List of certifications',
    type: [CertificationDto],
  })
  certifications: CertificationDto[];

  @ApiProperty({
    description: 'Count of certifications (only present in overview mode)',
    example: 5,
    required: false,
  })
  certificationsCount?: number;

  @ApiProperty({
    description: 'List of hobbies',
    example: ['Reading', 'Hiking', 'Photography'],
    type: [String],
  })
  hobbies: string[];

  @ApiProperty({
    description: 'List of interests',
    example: ['AI', 'Machine Learning', 'Web Development'],
    type: [String],
  })
  interests: string[];

  @ApiProperty({
    description: 'List of languages the user knows',
    example: ['English', 'Spanish', 'French'],
    type: [String],
  })
  languages: string[];

  @ApiProperty({
    description: 'Structured keywords data containing keywords and skill contexts',
    example: { keywords: ['typescript', 'react'], skill_contexts: ['web development', 'frontend'] },
    required: false,
  })
  keywordsData?: { keywords: string[], skill_contexts: string[] };

  @ApiProperty({
    description: 'Student-specific profile information (if applicable)',
    required: false,
  })
  studentProfile?: any;

  @ApiProperty({
    description: 'Job seeker-specific profile information (if applicable)',
    required: false,
  })
  jobSeekerProfile?: any;

  @ApiProperty({
    description: 'Career switcher-specific profile information (if applicable)',
    required: false,
  })
  careerSwitcherProfile?: any;

  @ApiProperty({
    description: 'Job search and career preferences',
    required: false,
  })
  smartPreference?: any;

  @ApiProperty({
    description: 'List of occupations the user is interested in pursuing',
    example: ['Software Developer', 'Data Scientist'],
    type: [String],
    required: false,
  })
  interestedOccupations?: string[];

  @ApiProperty({
    description: 'List of facts about the user',
    example: ['Has 5 years of experience in web development', 'Proficient in 3 programming languages'],
    type: [String],
    required: false,
  })
  facts?: string[];

  @ApiProperty({
    description: "User's email address",
    example: '<EMAIL>',
    required: false,
  })
  email?: string;

  @ApiProperty({
    description: "User's first name",
    example: 'John',
    required: false,
  })
  firstName?: string | null;

  @ApiProperty({
    description: "User's last name",
    example: 'Doe',
    required: false,
  })
  lastName?: string | null;

  @ApiProperty({
    description: "User's phone number",
    example: '+1234567890',
    required: false,
  })
  phone?: string | null;
}

/**
 * DTO for the complete smart profile response
 */
export class SmartProfileResponseDto {
  @ApiProperty({
    description: 'Indicates if the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'The smart profile data',
    type: SmartProfileDataDto,
  })
  data: SmartProfileDataDto;

  @ApiProperty({
    description: 'Message describing the result of the operation',
    example: 'Profile retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Timestamp when the response was generated',
    example: '2025-03-15T14:22:46+02:00',
  })
  timestamp: string;
}
