/**
 * PubSub topic constants
 * 
 * These constants define the topics used throughout the application
 * Using environment variables with fallbacks for development
 */

import { ConfigService } from '@nestjs/config';

export class PubSubTopics {
  // Static method to get topics that will be used in decorators
  static getTopics(): { [key: string]: string } {
    return {
      JOB_IMPORT: process.env.JOB_IMPORT_TOPIC || 'test-event',
    };
  }

  // Instance method for use in services
  constructor(private configService: ConfigService) {}

  get JOB_IMPORT(): string {
    return this.configService.get('JOB_IMPORT_TOPIC') || 'test-event';
  }
}

// Export constants for use in decorators
export const PUBSUB_TOPICS = PubSubTopics.getTopics();
