import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { DiscoveryService, Reflector } from '@nestjs/core';
import { PubSub } from '@google-cloud/pubsub';

import { PUBSUB_PATTERN } from '../../../auth/decorators/pubsub.decorator';
import { PUBSUB_TOPICS, PubSubTopics } from './pubsub.constants';

@Injectable()
export class PubSubService {
  private readonly logger = new Logger(PubSubService.name);
  private pubSubClient: PubSub;
  private parseMessage: boolean = true;
  private subscriptions: Map<string, boolean>;
  private pubSubTopics: PubSubTopics;

  constructor(
    private readonly config: ConfigService,
    private readonly reflector: Reflector,
    private readonly discoveryService: DiscoveryService,
  ) {
    const projectId = this.config.getOrThrow('GCP_PROJECT_ID');

    const credConfig = {
      client_id: this.config.getOrThrow('GCP_CLIENT_ID'),
      client_secret: this.config.getOrThrow('GCP_CLIENT_SECRET'),
      refresh_token: this.config.getOrThrow('GCP_REFRESH_TOKEN'),
      account: '',
      type: 'authorized_user',
      quota_project_id: projectId,
      universe_domain: 'googleapis.com',
    };

    this.subscriptions = new Map<string, boolean>();
    this.pubSubClient = new PubSub({ projectId, credentials: credConfig });
    this.parseMessage = !!this.config.get('PUB_SUB_PARSE_MESSAGE');
    this.pubSubTopics = new PubSubTopics(config);
  }

  async onModuleInit() {
    const controllers = this.discoveryService.getControllers();
    if (!controllers) return void 0;

    // Get the actual topics from environment variables
    const configuredTopics = {
      [PUBSUB_TOPICS.JOB_IMPORT]:
        this.config.get('JOB_IMPORT_TOPIC') || 'test-event',
    };

    // Log the actual topic being used
    this.logger.log(
      `Configured PubSub topics: ${JSON.stringify(configuredTopics)}`,
    );

    for (const wrapper of controllers) {
      if (!wrapper.instance) continue;

      const prototype = Object.getPrototypeOf(wrapper.instance);

      Object.getOwnPropertyNames(prototype).forEach((methodName) => {
        const method = prototype[methodName];
        const topicName = this.reflector.get(PUBSUB_PATTERN, method);

        if (topicName) {
          // Use the configured topic if it matches a known topic key
          const actualTopicName = configuredTopics[topicName] || topicName;
          this.logger.log(
            `Subscribing method ${methodName} to topic: ${actualTopicName}`,
          );
          this.subscribeToTopic(actualTopicName, method.bind(wrapper.instance));
        }
      });
    }
  }

  async subscribeToTopic(topicName: string, handler: Function) {
    try {
      // Make sure the topic exists
      await this.ensureTopicExists(topicName);

      const subscriptionName = `${topicName}-sub`;

      // Ensure the subscription exists
      await this.ensureSubscriptionExists(topicName, subscriptionName);

      const subscription = this.pubSubClient.subscription(subscriptionName);

      const isSubscribed = this.subscriptions.get(subscriptionName);
      if (isSubscribed) return void 0;

      this.subscriptions.set(subscriptionName, true);

      subscription.on('message', (ctx) => {
        const ctxMessage = ctx.data.toString();
        const data = this.parseMessage ? JSON.parse(ctxMessage) : ctxMessage;

        handler(data, ctx);
        ctx.ack();
      });

      subscription.on('error', (err) => {
        console.error('Error in subscription: ', err);
      });

      this.logger.log(`Listening for messages on ${subscriptionName}`);
    } catch (error) {
      this.logger.error(
        `Failed to subscribe to topic ${topicName}: ${error.message}`,
      );
    }
  }

  async publishMessage(topicName: string, data: unknown) {
    try {
      // Ensure topic exists before publishing
      await this.ensureTopicExists(topicName);

      const topic = this.pubSubClient.topic(topicName);
      const dataBuffer = Buffer.from(JSON.stringify(data));
      const messageId = await topic.publishMessage({ data: dataBuffer });
      this.logger.log(
        `Published message to topic ${topicName} with ID: ${messageId}`,
      );
      return { success: true, messageId };
    } catch (error) {
      this.logger.error(
        `Error publishing message to ${topicName}: ${error.message}`,
      );
      return { success: false, error: error.message };
    }
  }

  /**
   * Ensures that a topic exists, creating it if it doesn't
   * @param topicName The name of the topic to ensure exists
   */
  private async ensureTopicExists(topicName: string): Promise<void> {
    try {
      const topic = this.pubSubClient.topic(topicName);
      const [exists] = await topic.exists();

      if (!exists) {
        this.logger.log(`Topic ${topicName} does not exist, creating it...`);
        await this.pubSubClient.createTopic(topicName);
        this.logger.log(`Topic ${topicName} created successfully`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to ensure topic ${topicName} exists: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Ensures that a subscription exists, creating it if it doesn't
   * @param topicName The name of the topic to subscribe to
   * @param subscriptionName The name of the subscription
   */
  private async ensureSubscriptionExists(
    topicName: string,
    subscriptionName: string,
  ): Promise<void> {
    try {
      const topic = this.pubSubClient.topic(topicName);
      const subscription = topic.subscription(subscriptionName);

      const [exists] = await subscription.exists();

      if (!exists) {
        this.logger.log(
          `Subscription ${subscriptionName} does not exist, creating it...`,
        );
        // Create the subscription with sensible defaults for MVP
        await topic.createSubscription(subscriptionName, {
          ackDeadlineSeconds: 60, // Give subscribers 60 seconds to acknowledge messages
          expirationPolicy: {
            // Don't expire the subscription
            ttl: null,
          },
          retainAckedMessages: false, // Don't keep messages after they've been processed
        });
        this.logger.log(
          `Subscription ${subscriptionName} created successfully`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to ensure subscription ${subscriptionName} exists: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get the job import topic name
   * @returns The job import topic name
   */
  getJobImportTopic(): string {
    return this.pubSubTopics.JOB_IMPORT;
  }
}
