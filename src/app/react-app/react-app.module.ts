import { forwardRef, Module } from '@nestjs/common';
import { ReactAppController } from './controllers/react-app.controller';
import { ReactAppService } from './services/react-app.service';
import { JobsModule } from '../jobs/jobs.module';

@Module({
  imports: [forwardRef(() => JobsModule)],
  controllers: [ReactAppController],
  providers: [ReactAppService],
  exports: [ReactAppService],
})
export class ReactAppModule {}
