import { forwardRef, Inject, Injectable, Logger } from '@nestjs/common';
import { JobsSearchService } from '../../jobs/services/jobs-search.service';
import { ClassifyJobDto, MatchedJobDto } from '../dto/matched-job.dto';
import {
  KeywordsData,
  SmartProfileSearchDto,
} from '../dto/smart-profile-search.dto';
import { ConfigService } from '@nestjs/config';
import { JobSearchParams } from '../../jobs/services/interfaces/job-search.interface';
import { DifyService } from '../../jobs/services/helpers/dify.service';

@Injectable()
export class ReactAppService {
  private readonly logger = new Logger(ReactAppService.name);
  private difyApiUrl: string;
  private readonly difySkillsApiKey: string;
  private readonly difyKeywordGenApiKey: string;
  private readonly difyClassifyApiKey: string;
  private readonly processingProfiles = new Map<number, boolean>();

  constructor(
    @Inject(forwardRef(() => JobsSearchService))
    private readonly jobsSearchService: JobsSearchService,
    private readonly configService: ConfigService,
    private readonly difyService: DifyService,
  ) {
    this.difyApiUrl = this.configService.get<string>('WORKFLOW_API_URL')!;
    this.difySkillsApiKey = this.configService.get<string>(
      'DIFY_SKILLS_GEN_CLIENT_KEY',
    )!;
    this.difyKeywordGenApiKey = this.configService.get<string>(
      'DIFY_KEYWORD_GEN_CLIENT_KEY',
    )!;
    this.difyClassifyApiKey = this.configService.get<string>(
      'DIFY_CLASSIFY_CLIENT_KEY',
    )!;
  }

  /**
   * Get jobs that match the provided smart profile
   * @param searchParams Search parameters including smart profile and pagination
   * @returns Matched jobs with skill information
   */
  async getMatchedJobs(searchParams: SmartProfileSearchDto): Promise<{
    jobs: MatchedJobDto[];
    meta: { total: number; page: number; limit: number; has_next: boolean };
  }> {
    this.logger.log(`Getting matched jobs with smart profile search params`);

    try {
      // Extract keywords from the structured smartProfile
      const { keywords, skill_contexts } = searchParams.smartProfile;

      // Call the JobsSearchService to search for jobs
      const jobSearchParams: JobSearchParams = {
        ...searchParams,
        smart_profile: { keywords, skill_contexts }, // Pass the keywords to the search service
        top20: true,
      };

      const searchResults =
        await this.jobsSearchService.getTopJobs(jobSearchParams);

      // Transform raw job data into matched jobs with skill information
      const matchedJobs = await this.transformToMatchedJobs(
        searchResults.jobs,
        keywords,
      );

      // Return formatted response
      return {
        jobs: matchedJobs,
        meta: {
          total: searchResults.meta.total,
          page: searchResults.meta.page,
          limit: searchResults.meta.limit,
          has_next: searchResults.meta.has_next,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting matched jobs: ${error.message}`,
        error.stack,
      );

      // Return empty results in case of error
      return {
        jobs: [],
        meta: {
          total: 0,
          page: searchParams.page || 1,
          limit: searchParams.limit || 20,
          has_next: false,
        },
      };
    }
  }

  /**
   * Extract skills from a smart profile object
   * @param profile Smart profile object
   * @returns Array of skill names
   */
  private extractSkillsFromProfile(profile: any): string[] {
    // For MVP, we'll assume the smart profile has a skills array
    // This should be adjusted based on the actual structure of the smart profile
    try {
      return Array.isArray(profile.skills)
        ? profile.skills.map((s) =>
            typeof s === 'string' ? s : s.name || s.skill || '',
          )
        : [];
    } catch (e) {
      this.logger.warn(`Error extracting skills from profile: ${e.message}`);
      return [];
    }
  }

  /**
   * Transform job search results to matched jobs with skill information
   * @param jobs Job search results
   * @param userSkills User skills from smart profile
   * @returns Transformed job objects with skill matching
   */
  async transformToMatchedJobs(
    jobs: { id: string; rankingScore?: number; skill_contexts: string[] }[],
    userSkills: string[],
  ): Promise<MatchedJobDto[]> {
    const promises = jobs.map(async (job) => {
      // Always use the classifyJobMatch functionality for consistent behavior
      const classifyJobDto = await this.classifyJobMatch(
        userSkills.join(', '),
        job.skill_contexts.join(', '),
      );

      return {
        extId: job.id,
        score: job.rankingScore || 0,
        ...classifyJobDto,
      } as MatchedJobDto;
    });

    // Wait for all promises to resolve before returning
    return Promise.all(promises);
  }

  /**
   * Generates skill suggestions for a user based on their smart profile data
   * @param smartProfileId ID of the smart profile to analyze
   * @param forceRefresh Whether to force a refresh of cached suggestions
   * @returns Array of suggested skills
   */
  async generateSkillSuggestions(
    smartProfile: string,
    forceRefresh = false,
  ): Promise<string[]> {
    if (!smartProfile) {
      throw new Error('Smart profile not found');
    }

    try {
      // Call Dify API for skill suggestions
      const skillSuggestions: string[] = (await this.difyService.callDifyApi(
        smartProfile,
        {
          apiKey: this.difySkillsApiKey,
          responseProperty: 'skills',
          errorMessage: 'Failed to generate skill suggestions',
          fallbackMessage:
            'Could not generate skill suggestions based on profile data',
        },
      )) as string[];

      return skillSuggestions;
    } catch (error) {
      this.logger.error(
        `Error generating skill suggestions for smart profile ID ${smartProfile.substring(0, 20)}:`,
        error,
      );
      throw new Error('Failed to generate skill suggestions');
    }
  }

  /**
   * Generates keyword data for a user based on their smart profile data
   * @param smartProfileId ID of the smart profile to analyze
   * @param smartProfile Smart profile data as a string or object
   * @returns Object containing keywords and skill contexts
   */
  async generateKeywordData(
    smartProfileId: number,
    smartProfile: string | Record<string, any>,
  ): Promise<{ keywords: string[]; skill_contexts: string[] }> {
    // Check if this profile is already being processed
    if (this.processingProfiles.get(smartProfileId)) {
      return { keywords: [], skill_contexts: [] };
    }

    // Mark this profile as currently processing
    this.processingProfiles.set(smartProfileId, true);

    try {
      // Prepare profile data for Dify
      const profileData =
        typeof smartProfile === 'string'
          ? smartProfile
          : JSON.stringify(smartProfile);

      try {
        // Call Dify API for keyword data
        const keywordsResponse: Record<string, any> =
          await this.difyService.callDifyApi(
            '',
            {
              apiKey: this.difyKeywordGenApiKey,
              responseProperty: 'keywords',
              errorMessage: 'Failed to generate keywords',
              fallbackMessage:
                'Could not generate keywords based on profile data',
            },
            { user_profile: profileData },
          );

        const keywordData = {
          keywords: keywordsResponse.keywords,
          skill_contexts: keywordsResponse.skill_contexts,
        };

        return keywordData;
      } catch (error) {
        this.logger.error(
          `Error generating keyword data for smart profile ID ${smartProfileId}:`,
          error,
        );
        // throw new Error('Failed to generate keyword data');
        return { keywords: [], skill_contexts: [] };
      }
    } finally {
      // Always remove the processing flag when done, even if there was an error
      this.processingProfiles.delete(smartProfileId);
      this.logger.log(
        `Completed keyword generation for smart profile ID ${smartProfileId}`,
      );
    }
  }

  /**
   * Classifies a job match by comparing resume keywords and job description keywords
   * @param resume_keywords Keywords extracted from the user's resume/profile
   * @param job_description_keywords Keywords extracted from the job description
   * @returns Classification results with match level and skill comparisons
   */
  async classifyJobMatch(
    resume_keywords: string,
    job_description_keywords: string,
    job_tasks?: string,
  ): Promise<ClassifyJobDto> {
    this.logger.log('Classifying job match with Dify API');

    try {
      // Call Dify API using the common method
      const rawClassification = await this.difyService.callDifyApi(
        '',
        {
          apiKey: this.difyClassifyApiKey,
          responseProperty: 'classification',
          errorMessage: 'Failed to classify job match',
          fallbackMessage: 'Could not classify the job match',
        },
        {
          resume_keywords,
          job_description_keywords,
          job_tasks,
        },
      );

      // Process the result
      let match: ClassifyJobDto['match'] = 'NO_MATCH';
      let mySkills: string[] = [];
      let missingSkills: string[] = [];
      let header: string | undefined;
      let note: string | undefined;

      // Try to extract data from the response
      try {
        // First item might contain JSON string with classification data
        const classification = rawClassification as ClassifyJobDto;

        // Try to parse as JSON if it's a string that looks like JSON
        match = classification.match;
        mySkills = classification.mySkills;
        missingSkills = classification.missingSkills;
        header = classification.header;
        note = classification.note;
      } catch (error) {
        this.logger.error('Error processing classification response:', error);
      }

      return {
        match,
        mySkills,
        missingSkills,
        header,
        note,
      };
    } catch (error) {
      this.logger.error(
        `Error classifying job match: ${error.message}`,
        error.stack,
      );
      // Return default response in case of error
      return {
        match: 'NO_MATCH',
        mySkills: [],
        missingSkills: [],
      };
    }
  }

  async classifySingleJob(keys: KeywordsData, extId: string) {
    try {
      // Validate inputs
      if (!extId) {
        this.logger.warn('Missing extId in classifySingleJob');
        return this.getDefaultClassification();
      }

      if (
        !keys ||
        (!Array.isArray(keys.keywords) && !Array.isArray(keys.skill_contexts))
      ) {
        this.logger.warn(`Invalid KeywordsData provided for job ${extId}`);
        return this.getDefaultClassification();
      }

      // Get data for classification
      let prepareData: {
        resume_keywords: string;
        job_description_keywords: string;
        job_tasks: string;
      };
      try {
        prepareData = await this.jobsSearchService.prepareForClassifier(
          extId,
          keys,
        );

        // Check if we got valid data back
        if (
          !prepareData ||
          !prepareData.resume_keywords ||
          !prepareData.job_description_keywords
        ) {
          this.logger.warn(`Missing classification data for job ${extId}`);
          return this.getDefaultClassification();
        }
      } catch (error) {
        this.logger.error(
          `Error preparing job data for classification (${extId}): ${error.message}`,
        );
        return this.getDefaultClassification();
      }

      // Perform classification with error handling
      try {
        return await this.classifyJobMatch(
          prepareData.resume_keywords,
          prepareData.job_description_keywords,
          prepareData.job_tasks,
        );
      } catch (error) {
        this.logger.error(
          `Error during job classification (${extId}): ${error.message}`,
        );
        return this.getDefaultClassification();
      }
    } catch (error) {
      this.logger.error(
        `Unexpected error in classifySingleJob for ${extId}: ${error.message}`,
      );
      return this.getDefaultClassification();
    }
  }

  /**
   * Returns a default classification result for error scenarios
   */
  private getDefaultClassification(): ClassifyJobDto {
    return {
      match: 'NO_MATCH',
      mySkills: [],
      missingSkills: [],
    };
  }
}
