import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString } from 'class-validator';
import { ClassifyJobDto } from './matched-job.dto';

/**
 * DTO for recording a job view
 */
export class JobViewDto {
  @ApiProperty({
    description: 'ID of the user viewing the job',
    example: 'user-123456',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID of the job being viewed',
    example: 12345,
  })
  @IsNotEmpty()
  extId: string;
}

/**
 * DTO for recording a job rating (like/dislike)
 */
export class JobRatingDto {
  @ApiProperty({
    description: 'ID of the user rating the job',
    example: 'user-123456',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID of the job being rated',
    example: 12345,
  })
  @IsNotEmpty()
  extId: string;

  @ApiProperty({
    description: 'Whether the job is liked (true) or disliked (false)',
    example: true,
  })
  @IsBoolean()
  isLiked: boolean;
}

/**
 * DTO for storing job classification data
 */
export class JobClassificationDto {
  @ApiProperty({
    description: 'ID of the user',
    example: 'user-123456',
  })
  @IsString()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID of the job',
    example: 12345,
  })
  @IsNotEmpty()
  extId: string;

  @ApiProperty({
    description: 'Classification data for the job',
    type: ClassifyJobDto,
  })
  @IsNotEmpty()
  classificationData: ClassifyJobDto;
}
