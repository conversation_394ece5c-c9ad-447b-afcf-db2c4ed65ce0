import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO for keyword data that contains keywords and skill contexts
 */
export class KeywordDataDto {
  @ApiProperty({
    description: 'List of keywords associated with the user profile',
    example: ['typescript', 'react', 'nestjs'],
  })
  keywords: string[];

  @ApiProperty({
    description: 'List of skill contexts associated with the keywords',
    example: ['web development', 'frontend', 'backend'],
  })
  skill_contexts: string[];
}

/**
 * DTO for keyword data response
 */
export class KeywordsResponseDto {
  @ApiProperty({
    description: 'Indicates if the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'The keywords data',
    type: KeywordDataDto,
  })
  data: KeywordDataDto;

  @ApiProperty({
    description: 'Message describing the result of the operation',
    example: 'Keywords data generated successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Timestamp when the response was generated',
    example: '2025-03-19T00:48:39+02:00',
  })
  timestamp: string;
}
