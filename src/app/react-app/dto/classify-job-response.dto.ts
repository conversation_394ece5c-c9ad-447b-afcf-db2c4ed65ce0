import { ApiProperty } from '@nestjs/swagger';
import { ClassifyJobDto } from './matched-job.dto';

/**
 * Response DTO for the job classification endpoint
 */
export class ClassifyJobResponseDto {
  @ApiProperty({ description: 'Whether the request was successful', example: true })
  success: boolean;

  @ApiProperty({
    description: 'The classification result data',
    type: ClassifyJobDto,
    example: {
      match: 'GOOD',
      mySkills: ['javascript', 'react', 'frontend development'],
      missingSkills: ['redux', 'css', 'html'],
    },
  })
  data: ClassifyJobDto;

  @ApiProperty({
    description: 'Message describing the result',
    example: 'Job classification completed successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Timestamp of the response',
    example: '2025-03-19T02:15:23+02:00',
  })
  timestamp: string;
}
