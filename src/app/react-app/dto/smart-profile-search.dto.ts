import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNumber, IsObject, IsOptional, IsString, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Class representing the structure of keywords data
 */
export class KeywordsData {
  @ApiProperty({ description: 'List of keywords', type: [String] })
  @IsArray()
  keywords: string[];

  @ApiProperty({ description: 'List of skill contexts', type: [String] })
  @IsArray()
  skill_contexts: string[];

  @ApiProperty({ description: 'List of Languages the user speaks', type: [String] })
  @IsArray()
  @IsOptional()
  languages?: string[];
}

/**
 * DTO for smart profile search parameters
 */
export class SmartProfileSearchDto {
  @ApiProperty({ 
    description: 'Smart profile with keywords and skill contexts', 
    required: true,
    example: {
      keywords: ['javascript', 'react', 'typescript'],
      languages: ['English', 'FI',],
      skill_contexts: ['web development', 'frontend']
    },
    type: KeywordsData
  })
  @IsObject()
  @ValidateNested()
  @Type(() => KeywordsData)
  smartProfile: KeywordsData;

  @ApiProperty({ description: 'Search query text', required: false })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiProperty({ description: 'Page number for pagination', required: false, default: 1 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number;

  @ApiProperty({ description: 'Number of items per page', required: false, default: 20 })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number;

  @ApiProperty({ description: 'Starting offset for pagination', required: false })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  offset?: number;

  @ApiProperty({ description: 'Field to sort by', required: false })
  @IsOptional()
  @IsString()
  sort_by?: string;

  @ApiProperty({ description: 'Sort direction', required: false, enum: ['asc', 'desc'], default: 'desc' })
  @IsOptional()
  @IsString()
  sort_direction?: 'asc' | 'desc';

  @IsOptional()
  occupation_code?: string;
  @IsOptional()
  industry_code?: string;
  @IsOptional()
  skills?: string[];
  @IsOptional()
  seniority?: string;
  @IsOptional()
  smart_profile?: any;

  // Location filters
  @ApiProperty({ description: 'Filter by municipality names', required: false, type: [String] })
  @IsOptional()
  @IsString({ each: true })
  municipality_names?: string | string[];

  @ApiProperty({ description: 'Filter by municipality codes', required: false, type: [String] })
  @IsOptional()
  @IsString({ each: true })
  municipality_codes?: string | string[];

  @ApiProperty({ description: 'Filter by region names', required: false, type: [String] })
  @IsOptional()
  @IsString({ each: true })
  region_names?: string | string[];

  @ApiProperty({ description: 'Filter by region codes', required: false, type: [String] })
  @IsOptional()
  @IsString({ each: true })
  region_codes?: string | string[];

  @ApiProperty({ description: 'Filter by country codes/names', required: false, type: [String] })
  @IsOptional()
  @IsString({ each: true })
  country?: string | string[];

  // Add any additional search parameters that getTopJobs supports
  [key: string]: any;
}
