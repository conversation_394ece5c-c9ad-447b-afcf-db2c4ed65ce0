import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';
import { Translatable } from '../../translation/decorators/translatable.decorator';

/**
 * DTO for skill suggestions request
 */
export class SkillSuggestionsRequestDto {
  @ApiProperty({ 
    description: 'JSON string representing a smart profile with skills, experience, and other professional information', 
    required: true,
    example: JSON.stringify({
      skills: ['JavaScript', 'HTML', 'CSS'],
      experience: [
        {
          title: 'Frontend Developer',
          company: 'Tech Company',
          years: 3,
          description: 'Developed responsive web applications using React and Redux'
        }
      ],
      education: [
        {
          degree: 'Bachelor of Science',
          field: 'Computer Science',
          institution: 'University of Technology'
        }
      ]
    }, null, 2)
  })
  @IsString()
  smartProfile: string;
}

/**
 * DTO for skill suggestions data
 */
export class SkillSuggestionsDataDto {
  @ApiProperty({
    description: 'List of suggested skills',
    example: [
      'JavaScript',
      'React',
      'Redux',
      'TypeScript',
      'Frontend Development',
      'Responsive Design',
      'UI/UX',
      'Web Performance Optimization',
      'REST API Integration',
      'Git'
    ]
  })
  @Translatable()
  skills: string[];
}

/**
 * DTO for skill suggestions response
 */
export class SkillSuggestionsResponseDto {
  @ApiProperty({ 
    example: true,
    description: 'Indicates whether the operation was successful'
  })
  success: boolean;

  @ApiProperty({
    description: 'Contains the generated skill suggestions',
    example: {
      skills: [
        'JavaScript',
        'React',
        'Redux',
        'TypeScript',
        'Frontend Development',
        'Responsive Design',
        'UI/UX',
        'Web Performance Optimization',
        'REST API Integration',
        'Git'
      ]
    }
  })
  data: SkillSuggestionsDataDto;

  @ApiProperty({ 
    example: 'Skill suggestions generated successfully',
    description: 'Human-readable message describing the result of the operation'
  })
  message: string;

  @ApiProperty({ 
    example: '2025-03-18T22:01:38+02:00',
    description: 'ISO timestamp of when the response was generated'
  })
  timestamp: string;
}
