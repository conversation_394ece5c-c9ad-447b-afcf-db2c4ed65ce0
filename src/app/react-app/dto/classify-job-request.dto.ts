import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';

/**
 * DTO for the job classification request
 */
export class ClassifyJobRequestDto {
  @ApiProperty({
    description: 'Keywords extracted from the user resume/profile',
    example: 'javascript, react, nodejs, typescript, frontend development',
  })
  @IsNotEmpty()
  @IsString()
  resume_keywords: string;

  @ApiProperty({
    description: 'Keywords extracted from the job description',
    example: 'javascript, react, redux, css, html, frontend development',
  })
  @IsNotEmpty()
  @IsString()
  job_description_keywords: string;

  @IsOptional()
  extId?: string;
}
