import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO for keywords data request
 */
export class KeywordsDataRequestDto {
  @ApiProperty({
    description: 'The ID of the smart profile to generate keywords for',
    example: 12345,
  })
  @IsNumber()
  @Type(() => Number)
  smartProfileId: number;

  @ApiProperty({
    description: 'Smart profile data as a string or object',
    example: '{"skills":["JavaScript","React"],"experience":[{"role":"Developer"}]}',
    required: true,
  })
  @IsNotEmpty()
  smartProfile: string | Record<string, any>;
}
