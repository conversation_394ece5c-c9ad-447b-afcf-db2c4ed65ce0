import { ApiProperty } from '@nestjs/swagger';
import { Translatable } from '../../translation/decorators/translatable.decorator';

export class ClassifyJobDto {
  @ApiProperty({
    description: 'Matching score between the job and the user profile',
  })
  match: 'NO_MATCH' | 'SLIGHT' | 'GOOD' | 'STRONG';

  @ApiProperty({
    description: 'Skills from user profile that match the job requirements',
    type: [String],
  })
  @Translatable()
  mySkills: string[];

  @ApiProperty({
    description: 'Skills required by the job but missing in user profile',
    type: [String],
  })
  @Translatable()
  missingSkills: string[];

  @ApiProperty({
    description: 'AI note about the job',
  })
  @Translatable()
  header?: string;
  @Translatable()
  note?: string;
}

/**
 * DTO for the job matching response with skill information
 */
export class MatchedJobDto extends ClassifyJobDto {
  @ApiProperty({ description: 'External ID of the job' })
  extId: string;

  @ApiProperty({
    description: 'Matching score between the job and the user profile',
  })
  score: number;
}

/**
 * DTO for the job matching response with metadata
 */
export class MatchedJobsResponseDto {
  @ApiProperty({
    description: 'List of matched jobs with skill information',
    type: [MatchedJobDto],
  })
  jobs: MatchedJobDto[];

  @ApiProperty({
    description: 'Metadata for pagination',
    type: 'object',
    properties: {
      total: { type: 'number', description: 'Total number of matched jobs' },
      page: { type: 'number', description: 'Current page number' },
      limit: { type: 'number', description: 'Number of items per page' },
      has_next: {
        type: 'boolean',
        description: 'Whether there are more pages',
      },
    },
  })
  meta: {
    total: number;
    page: number;
    limit: number;
    has_next: boolean;
  };
}
