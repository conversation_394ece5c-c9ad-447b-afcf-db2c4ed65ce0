import { Body, Controller, Logger, Post, ValidationPipe } from '@nestjs/common';
import {
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { ReactAppService } from '../services/react-app.service';
import { SmartProfileSearchDto } from '../dto/smart-profile-search.dto';
import { MatchedJobsResponseDto } from '../dto/matched-job.dto';
import {
  SkillSuggestionsDataDto,
  SkillSuggestionsRequestDto,
  SkillSuggestionsResponseDto,
} from '../dto/skill-suggestions.dto';
import { KeywordsDataRequestDto } from '../dto/keywords-data.dto';
import { KeywordsResponseDto } from '../dto/keywords-response.dto';
import { TranslateResponse } from '../../translation/decorators/translate-response.decorator';

@ApiTags('React App')
@Controller('v1/react-app')
export class ReactAppController {
  private readonly logger = new Logger(ReactAppController.name);

  constructor(private readonly reactAppService: ReactAppService) {}

  @Post('jobs/match')
  @ApiOperation({ summary: 'Get jobs that match a smart profile' })
  @ApiResponse({
    status: 200,
    description:
      'Returns jobs that match the provided smart profile with skill information',
    type: MatchedJobsResponseDto,
  })
  @TranslateResponse()
  async getMatchedJobs(
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
        transform: true,
      }),
    )
    searchParams: SmartProfileSearchDto & { user_id: string },
  ): Promise<MatchedJobsResponseDto> {
    this.logger.log(
      `Received matched jobs request with params: ${JSON.stringify(searchParams)}`,
    );

    try {
      return await this.reactAppService.getMatchedJobs(searchParams);
    } catch (error) {
      this.logger.error(
        `Error getting matched jobs: ${error.message}`,
        error.stack,
      );
      // Return empty results in case of error
      return {
        jobs: [],
        meta: {
          total: 0,
          page: 1,
          limit: searchParams.limit || 20,
          has_next: false,
        },
      };
    }
  }

  /**
   * Generate skill suggestions based on provided smart profile data using Dify AI
   *
   * This endpoint triggers a call to the Dify AI service to analyze
   * the provided smart profile data and generate relevant skill suggestions.
   *
   * @param requestDto The request containing the smart profile data
   */
  @ApiOperation({
    summary: 'Generate skill suggestions based on smart profile',
    description:
      'Analyzes the provided smart profile data and generates AI-powered skill suggestions based on background and experience. The smart profile should be provided as a JSON string containing skills, experience, and other relevant professional information.',
  })
  @ApiOkResponse({
    description: 'Skill suggestions generated successfully',
    schema: {
      example: {
        success: true,
        data: {
          skills: [
            'JavaScript',
            'React',
            'Node.js',
            'TypeScript',
            'API Development',
            'Cloud Architecture',
            'System Design',
            'Data Structures',
            'Agile Methodologies',
            'CI/CD',
          ],
        },
        message: 'Skill suggestions generated successfully',
        timestamp: '2025-03-18T22:01:38+02:00',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Smart profile not found or invalid',
    schema: {
      example: {
        success: false,
        data: {
          skills: [],
        },
        message:
          'Failed to generate skill suggestions: Smart profile not found or invalid',
        timestamp: '2025-03-18T22:01:38+02:00',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid smart profile format',
    schema: {
      example: {
        statusCode: 400,
        message: ['smartProfile must be a string'],
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error - AI service connection failure',
    schema: {
      example: {
        success: false,
        data: {
          skills: [],
        },
        message: 'Failed to generate skill suggestions: Service unavailable',
        timestamp: '2025-03-18T22:01:38+02:00',
      },
    },
  })
  @Post('skill-suggestions')
  @TranslateResponse()
  async getMySkillSuggestions(
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
        transform: true,
      }),
    )
    requestDto: SkillSuggestionsRequestDto & { user_id: string },
  ): Promise<SkillSuggestionsResponseDto> {
    this.logger.log('Received skill suggestions request');

    // Generate skill suggestions using ReactAppService
    const skillSuggestions =
      await this.reactAppService.generateSkillSuggestions(
        requestDto.smartProfile,
      );

    // Create proper instances of the DTOs for translation to work
    const dataDto = new SkillSuggestionsDataDto();
    dataDto.skills = skillSuggestions;

    const responseDto = new SkillSuggestionsResponseDto();
    responseDto.success = true;
    responseDto.data = dataDto;
    responseDto.message = 'Skill suggestions generated successfully';
    responseDto.timestamp = new Date().toISOString();

    return responseDto;
  }

  /**
   * Generate keyword data based on smart profile
   *
   * This endpoint extracts keywords and skill contexts from the provided smart profile
   * using AI analysis. The extracted data can be used for job matching and recommendations.
   *
   * @param requestDto The request containing smartProfileId and smart profile data
   */
  @ApiOperation({
    summary: 'Generate keyword data from smart profile',
    description:
      'Analyzes the provided smart profile data and extracts keywords and skill contexts using AI analysis. Returns structured keyword data that can be used for job matching and recommendations.',
  })
  @ApiOkResponse({
    description: 'Keywords data generated successfully',
    type: KeywordsResponseDto,
    schema: {
      example: {
        success: true,
        data: {
          keywords: ['javascript', 'react', 'nodejs', 'typescript', 'aws'],
          skill_contexts: ['web development', 'frontend', 'backend', 'cloud'],
        },
        message: 'Keywords data generated successfully',
        timestamp: '2025-03-19T00:48:39+02:00',
      },
    },
  })
  @ApiNotFoundResponse({
    description: 'Smart profile not found or invalid',
    schema: {
      example: {
        success: false,
        data: {
          keywords: [],
          skill_contexts: [],
        },
        message:
          'Failed to generate keywords: Smart profile not found or invalid',
        timestamp: '2025-03-19T00:48:39+02:00',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid smart profile format',
    schema: {
      example: {
        statusCode: 400,
        message: ['smartProfile must be a string or object'],
        error: 'Bad Request',
      },
    },
  })
  @ApiResponse({
    status: 500,
    description: 'Internal Server Error - AI service connection failure',
    schema: {
      example: {
        success: false,
        data: {
          keywords: [],
          skill_contexts: [],
        },
        message: 'Failed to generate keywords: Service unavailable',
        timestamp: '2025-03-19T00:48:39+02:00',
      },
    },
  })
  @Post('keywords-data')
  async generateKeywordsData(
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
        transform: true,
      }),
    )
    requestDto: KeywordsDataRequestDto & { user_id: string },
  ): Promise<KeywordsResponseDto> {
    this.logger.log(
      `Received keywords data request for smart profile ID: ${requestDto.smartProfileId}`,
    );

    try {
      // Generate keywords data using ReactAppService
      const keywordData = await this.reactAppService.generateKeywordData(
        requestDto.smartProfileId,
        requestDto.smartProfile,
      );

      return {
        success: true,
        data: keywordData,
        message: 'Keywords data generated successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(
        `Error generating keywords data: ${error.message}`,
        error.stack,
      );

      return {
        success: false,
        data: {
          keywords: [],
          skill_contexts: [],
        },
        message: `Failed to generate keywords: ${error.message || 'Unknown error'}`,
        timestamp: new Date().toISOString(),
      };
    }
  }
}
