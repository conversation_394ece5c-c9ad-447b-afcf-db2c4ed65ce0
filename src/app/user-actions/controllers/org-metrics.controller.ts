import { <PERSON>, Get, Param, Query, UseGuards, Logger, Post } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AuthGuard } from '../../../auth/guards/auth.guard';
import { OrgMetricsService } from '../services/org-metrics.service';
import { OrgMetricsResponseDto } from '../dtos/org-metrics.dto';
import { TimeframeType } from '../../../entities/user-action-summary.entity';
import { PubSubService } from '../../shared/pubsub/pubsub.service';
import { USER_ACTION_PUBSUB_TOPICS } from '../constants/pubsub.constants';

@ApiTags('OrganizationMetrics')
@Controller('v1/org-metrics')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class OrgMetricsController {
  private readonly logger = new Logger(OrgMetricsController.name);

  constructor(
    private readonly orgMetricsService: OrgMetricsService,
    private readonly pubSubService: PubSubService,
  ) {}

  @Get(':organizationId/:timeframe')
  @ApiOperation({ summary: 'Get organization metrics by timeframe' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'timeframe', enum: TimeframeType, description: 'Timeframe for metrics (daily, weekly, monthly)' })
  @ApiQuery({ name: 'fresh', required: false, type: 'boolean', description: 'Force fresh calculation (optional)' })
  @ApiResponse({ status: 200, description: 'Organization metrics retrieved successfully', type: OrgMetricsResponseDto })
  async getOrgMetrics(
    @Param('organizationId') organizationId: string,
    @Param('timeframe') timeframe: TimeframeType,
    @Query('fresh') fresh: boolean,
  ): Promise<OrgMetricsResponseDto> {
    this.logger.log(`Getting ${timeframe} metrics for organization ${organizationId}`);
    
    return this.orgMetricsService.getOrgMetrics(
      organizationId,
      timeframe,
      fresh === true,
    );
  }

  @Post(':organizationId/:timeframe/calculate')
  @ApiOperation({ summary: 'Trigger calculation of organization metrics' })
  @ApiParam({ name: 'organizationId', description: 'Organization ID' })
  @ApiParam({ name: 'timeframe', enum: TimeframeType, description: 'Timeframe for metrics (daily, weekly, monthly)' })
  @ApiResponse({ status: 201, description: 'Metric calculation triggered successfully' })
  async triggerMetricsCalculation(
    @Param('organizationId') organizationId: string,
    @Param('timeframe') timeframe: TimeframeType,
  ): Promise<{ success: boolean; message: string }> {
    this.logger.log(`Triggering calculation of ${timeframe} metrics for organization ${organizationId}`);
    
    // Create the message payload
    const message = {
      organizationId,
      timeframeType: timeframe,
      periodStart: new Date().toISOString(),
      forceFresh: true,
    };
    
    // Publish the message to trigger calculation
    const result = await this.pubSubService.publishMessage(
      USER_ACTION_PUBSUB_TOPICS.CALCULATE_ORG_METRICS,
      message,
    );
    
    if (!result.success) {
      this.logger.error(`Failed to publish message: ${result.error}`);
      throw new Error('Failed to trigger organization metrics calculation');
    }
    
    return {
      success: true,
      message: `Metrics calculation for organization ${organizationId} has been scheduled`,
    };
  }
}