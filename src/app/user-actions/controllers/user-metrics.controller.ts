import { <PERSON>, Get, Param, Query, UseGuards, Logger, Post } from '@nestjs/common';
import { ApiT<PERSON><PERSON>, ApiBearerAuth, Api<PERSON>peration, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AuthGuard } from '../../../auth/guards/auth.guard';
import { SessionUser } from '../../../auth/decorators/session.decorator';
import { SessionContainer } from 'supertokens-node/recipe/session';
import { UserMetricsService } from '../services/user-metrics.service';
import { UserMetricsResponseDto } from '../dtos/user-metrics.dto';
import { TimeframeType } from '../../../entities/user-action-summary.entity';
import { PubSubService } from '../../shared/pubsub/pubsub.service';
import { USER_ACTION_PUBSUB_TOPICS } from '../constants/pubsub.constants';

@ApiTags('UserMetrics')
@Controller('v1/user-metrics')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class UserMetricsController {
  private readonly logger = new Logger(UserMetricsController.name);

  constructor(
    private readonly userMetricsService: UserMetricsService,
    private readonly pubSubService: PubSubService,
  ) {}

  @Get(':timeframe')
  @ApiOperation({ summary: 'Get user metrics by timeframe' })
  @ApiParam({ name: 'timeframe', enum: TimeframeType, description: 'Timeframe for metrics (daily, weekly, monthly)' })
  @ApiQuery({ name: 'userId', required: false, description: 'User ID (optional, defaults to authenticated user)' })
  @ApiQuery({ name: 'fresh', required: false, type: 'boolean', description: 'Force fresh calculation (optional)' })
  @ApiResponse({ status: 200, description: 'User metrics retrieved successfully', type: UserMetricsResponseDto })
  async getUserMetrics(
    @Param('timeframe') timeframe: TimeframeType,
    @Query('userId') userIdQuery: string,
    @Query('fresh') fresh: boolean,
    @SessionUser() session: SessionContainer,
  ): Promise<UserMetricsResponseDto> {
    // Use user ID from query param or fall back to session user ID
    const userId = userIdQuery || session.getUserId();
    
    if (!userId) {
      throw new Error('No user ID available');
    }
    
    this.logger.log(`Getting ${timeframe} metrics for user ${userId}`);
    
    return this.userMetricsService.getUserMetrics(
      userId,
      timeframe,
      fresh === true,
    );
  }

  @Post(':timeframe/calculate')
  @ApiOperation({ summary: 'Trigger calculation of user metrics' })
  @ApiParam({ name: 'timeframe', enum: TimeframeType, description: 'Timeframe for metrics (daily, weekly, monthly)' })
  @ApiQuery({ name: 'userId', required: false, description: 'User ID (optional, defaults to authenticated user)' })
  @ApiResponse({ status: 201, description: 'Metric calculation triggered successfully' })
  async triggerMetricsCalculation(
    @Param('timeframe') timeframe: TimeframeType,
    @Query('userId') userIdQuery: string,
    @SessionUser() session: SessionContainer,
  ): Promise<{ success: boolean; message: string }> {
    // Use user ID from query param or fall back to session user ID
    const userId = userIdQuery || session.getUserId();
    
    if (!userId) {
      throw new Error('No user ID available');
    }
    
    this.logger.log(`Triggering calculation of ${timeframe} metrics for user ${userId}`);
    
    // Create the message payload
    const message = {
      userId,
      timeframeType: timeframe,
      periodStart: new Date().toISOString(),
      forceFresh: true,
    };
    
    // Publish the message to trigger calculation
    const result = await this.pubSubService.publishMessage(
      USER_ACTION_PUBSUB_TOPICS.CALCULATE_USER_METRICS,
      message,
    );
    
    if (!result.success) {
      this.logger.error(`Failed to publish message: ${result.error}`);
      throw new Error('Failed to trigger metrics calculation');
    }
    
    return {
      success: true,
      message: `Metrics calculation for user ${userId} has been scheduled`,
    };
  }
}