import { Body, Controller, Post, Logger } from '@nestjs/common';
import { UserActionTrackerService } from '../services/user-action-tracker.service';
import { 
  TrackSearchDto,
  TrackJobViewDto,
  TrackJobSaveDto,
  TrackJobApplyDto,
  TrackRecommendationsShownDto,
  TrackRecommendationClickedDto 
} from '../dtos/track-action.dto';

@Controller('v1/user-actions')
export class UserActionController {
  private readonly logger = new Logger(UserActionController.name);

  constructor(private readonly userActionService: UserActionTrackerService) {}

  @Post('search')

  async trackSearch(@Body() dto: TrackSearchDto): Promise<{ success: boolean }> {
    await this.userActionService.trackSearch(
      dto.userId,
      dto.searchQuery,
      dto.filters || {},
    );
    return { success: true };
  }

  @Post('view')

  async trackJobView(@Body() dto: TrackJobViewDto): Promise<{ success: boolean }> {
    await this.userActionService.trackJobView(
      dto.userId,
      dto.jobExtId,
      dto.source,
      dto.correlationId,
      dto.viewDurationMs,
    );
    return { success: true };
  }

  @Post('save')

  async trackJobSave(@Body() dto: TrackJobSaveDto): Promise<{ success: boolean }> {
    await this.userActionService.trackJobSave(
      dto.userId,
      dto.jobExtId,
      dto.source,
      dto.correlationId,
    );
    return { success: true };
  }

  @Post('unsave')

  async trackJobUnsave(@Body() dto: TrackJobSaveDto): Promise<{ success: boolean }> {
    await this.userActionService.trackJobUnsave(
      dto.userId,
      dto.jobExtId,
      dto.source,
      dto.correlationId,
    );
    return { success: true };
  }

  @Post('apply')

  async trackJobApply(@Body() dto: TrackJobApplyDto): Promise<{ success: boolean }> {
    await this.userActionService.trackJobApply(
      dto.userId,
      dto.jobExtId,
      dto.source,
      dto.correlationId,
      dto.applicationData,
    );
    return { success: true };
  }

  @Post('recommendations-shown')

  async trackRecommendationsShown(
    @Body() dto: TrackRecommendationsShownDto,
  ): Promise<{ success: boolean; correlationId: string }> {
    const correlationId = await this.userActionService.trackRecommendationsShown(
      dto.userId,
      dto.jobExtIds,
      dto.source,
    );
    return { success: true, correlationId };
  }

  @Post('recommendation-clicked')

  async trackRecommendationClicked(
    @Body() dto: TrackRecommendationClickedDto,
  ): Promise<{ success: boolean }> {
    await this.userActionService.trackRecommendationClicked(
      dto.userId,
      dto.jobExtId,
      dto.correlationId,
      dto.position,
    );
    return { success: true };
  }
}
