import { Injectable, Logger } from '@nestjs/common';
import { PubSubMessagePattern } from '../../../auth/decorators/pubsub.decorator';
import { USER_ACTION_PUBSUB_TOPICS, CalculateUserMetricsMessage } from '../constants/pubsub.constants';
import { UserMetricsService } from '../services/user-metrics.service';
import { TimeframeType } from '../../../entities/user-action-summary.entity';

@Injectable()
export class UserMetricsHandler {
  private readonly logger = new Logger(UserMetricsHandler.name);

  constructor(private readonly userMetricsService: UserMetricsService) {}

  @PubSubMessagePattern(USER_ACTION_PUBSUB_TOPICS.CALCULATE_USER_METRICS)
  async handleCalculateUserMetrics(data: CalculateUserMetricsMessage): Promise<void> {
    this.logger.log(`Received message to calculate user metrics: ${JSON.stringify(data)}`);
    
    try {
      const { userId, timeframeType, periodStart, forceFresh } = data;
      
      if (!userId || !timeframeType || !periodStart) {
        this.logger.error('Invalid message data: missing required fields');
        return;
      }
      
      // Parse the timeframe type
      const timeframe = timeframeType as TimeframeType;
      
      // Parse the period start date
      const periodStartDate = new Date(periodStart);
      
      // Calculate metrics
      await this.userMetricsService.calculateUserMetrics(
        userId,
        timeframe,
        periodStartDate,
      );
      
      this.logger.log(`Successfully calculated metrics for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error processing calculate user metrics message: ${error.message}`, error.stack);
    }
  }
}