import { Injectable, Logger } from '@nestjs/common';
import { PubSubMessagePattern } from '../../../auth/decorators/pubsub.decorator';
import { USER_ACTION_PUBSUB_TOPICS, CalculateOrgMetricsMessage } from '../constants/pubsub.constants';
import { OrgMetricsService } from '../services/org-metrics.service';
import { TimeframeType } from '../../../entities/user-action-summary.entity';

@Injectable()
export class OrgMetricsHandler {
  private readonly logger = new Logger(OrgMetricsHandler.name);

  constructor(private readonly orgMetricsService: OrgMetricsService) {}

  @PubSubMessagePattern(USER_ACTION_PUBSUB_TOPICS.CALCULATE_ORG_METRICS)
  async handleCalculateOrgMetrics(data: CalculateOrgMetricsMessage): Promise<void> {
    this.logger.log(`Received message to calculate organization metrics: ${JSON.stringify(data)}`);
    
    try {
      const { organizationId, timeframeType, periodStart, forceFresh } = data;
      
      if (!organizationId || !timeframeType || !periodStart) {
        this.logger.error('Invalid message data: missing required fields');
        return;
      }
      
      // Parse the timeframe type
      const timeframe = timeframeType as TimeframeType;
      
      // Parse the period start date
      const periodStartDate = new Date(periodStart);
      
      // Calculate metrics
      await this.orgMetricsService.calculateOrgMetrics(
        organizationId,
        timeframe,
        periodStartDate,
      );
      
      this.logger.log(`Successfully calculated metrics for organization ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error processing calculate org metrics message: ${error.message}`, error.stack);
    }
  }
}