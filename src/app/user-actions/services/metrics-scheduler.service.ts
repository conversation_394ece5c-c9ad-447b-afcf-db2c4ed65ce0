import { Injectable, Logger, OnApplicationBootstrap } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserAction } from '../../../entities/user-action.entity';
import { TimeframeType } from '../../../entities/user-action-summary.entity';
import { PubSubService } from '../../shared/pubsub/pubsub.service';
import { USER_ACTION_PUBSUB_TOPICS } from '../constants/pubsub.constants';
import { startOfDay, startOfWeek, startOfMonth } from 'date-fns';

@Injectable()
export class MetricsSchedulerService implements OnApplicationBootstrap {
  private readonly logger = new Logger(MetricsSchedulerService.name);
  
  constructor(
    @InjectRepository(UserAction)
    private readonly userActionRepository: Repository<UserAction>,
    private readonly pubSubService: PubSubService,
  ) {}
  
  onApplicationBootstrap() {
    this.logger.log('Metrics scheduler service initialized');
  }
  
  /**
   * Run daily at 2:00 AM to calculate daily metrics for active users
   */
  @Cron('0 2 * * *')
  async scheduleDailyMetricsCalculation() {
    this.logger.log('Starting scheduled daily metrics calculation');
    
    try {
      // Get users who were active in the last 24 hours
      const activeUserIds = await this.getRecentlyActiveUsers(1);
      this.logger.log(`Found ${activeUserIds.length} users active in the last day`);
      
      const periodStart = startOfDay(new Date());
      
      // Schedule calculation for each user
      for (const userId of activeUserIds) {
        await this.scheduleUserMetricsCalculation(
          userId,
          TimeframeType.DAILY,
          periodStart,
        );
      }
      
      // Also schedule org-level metrics for any organizations with active users
      const organizationIds = await this.getOrganizationsWithActiveUsers(activeUserIds);
      this.logger.log(`Found ${organizationIds.length} organizations with active users`);
      
      for (const orgId of organizationIds) {
        await this.scheduleOrgMetricsCalculation(
          orgId,
          TimeframeType.DAILY,
          periodStart,
        );
      }
      
      this.logger.log('Completed scheduling daily metrics calculations');
    } catch (error) {
      this.logger.error(`Error scheduling daily metrics calculation: ${error.message}`, error.stack);
    }
  }
  
  /**
   * Run weekly on Sunday at 3:00 AM to calculate weekly metrics
   */
  @Cron('0 3 * * 0')
  async scheduleWeeklyMetricsCalculation() {
    this.logger.log('Starting scheduled weekly metrics calculation');
    
    try {
      // Get users who were active in the last week
      const activeUserIds = await this.getRecentlyActiveUsers(7);
      this.logger.log(`Found ${activeUserIds.length} users active in the last week`);
      
      const periodStart = startOfWeek(new Date());
      
      // Schedule calculation for each user
      for (const userId of activeUserIds) {
        await this.scheduleUserMetricsCalculation(
          userId,
          TimeframeType.WEEKLY,
          periodStart,
        );
      }
      
      // Also schedule org-level metrics for any organizations with active users
      const organizationIds = await this.getOrganizationsWithActiveUsers(activeUserIds);
      this.logger.log(`Found ${organizationIds.length} organizations with active users`);
      
      for (const orgId of organizationIds) {
        await this.scheduleOrgMetricsCalculation(
          orgId,
          TimeframeType.WEEKLY,
          periodStart,
        );
      }
      
      this.logger.log('Completed scheduling weekly metrics calculations');
    } catch (error) {
      this.logger.error(`Error scheduling weekly metrics calculation: ${error.message}`, error.stack);
    }
  }
  
  /**
   * Run monthly on the 1st of each month at 4:00 AM to calculate monthly metrics
   */
  @Cron('0 4 1 * *')
  async scheduleMonthlyMetricsCalculation() {
    this.logger.log('Starting scheduled monthly metrics calculation');
    
    try {
      // Get users who were active in the last month
      const activeUserIds = await this.getRecentlyActiveUsers(30);
      this.logger.log(`Found ${activeUserIds.length} users active in the last month`);
      
      const periodStart = startOfMonth(new Date());
      
      // Schedule calculation for each user
      for (const userId of activeUserIds) {
        await this.scheduleUserMetricsCalculation(
          userId,
          TimeframeType.MONTHLY,
          periodStart,
        );
      }
      
      // Also schedule org-level metrics for any organizations with active users
      const organizationIds = await this.getOrganizationsWithActiveUsers(activeUserIds);
      this.logger.log(`Found ${organizationIds.length} organizations with active users`);
      
      for (const orgId of organizationIds) {
        await this.scheduleOrgMetricsCalculation(
          orgId,
          TimeframeType.MONTHLY,
          periodStart,
        );
      }
      
      this.logger.log('Completed scheduling monthly metrics calculations');
    } catch (error) {
      this.logger.error(`Error scheduling monthly metrics calculation: ${error.message}`, error.stack);
    }
  }
  
  /**
   * Get users who were active in the last N days
   */
  private async getRecentlyActiveUsers(days: number): Promise<string[]> {
    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - days);
    
    const result = await this.userActionRepository
      .createQueryBuilder('action')
      .select('DISTINCT action.userId')
      .where('action.createdAt >= :daysAgo', { daysAgo })
      .getRawMany();
    
    return result.map(r => r.userId);
  }
  
  /**
   * Get organizations that have active users
   * Note: This is a stub - in a real implementation, you would use actual org mapping
   */
  private async getOrganizationsWithActiveUsers(userIds: string[]): Promise<string[]> {
    if (userIds.length === 0) {
      return [];
    }
    
    try {
      const result = await this.userActionRepository
        .createQueryBuilder('action')
        .select('DISTINCT action.metadata->>"organizationId" as organizationId')
        .where('action.userId IN (:...userIds)', { userIds })
        .andWhere('action.metadata->>"organizationId" IS NOT NULL')
        .getRawMany();
      
      return result
        .map(r => r.organizationId)
        .filter(Boolean); // Remove any null/undefined values
    } catch (error) {
      this.logger.error(`Error getting organizations with active users: ${error.message}`);
      return [];
    }
  }
  
  /**
   * Schedule user metrics calculation via PubSub
   */
  private async scheduleUserMetricsCalculation(
    userId: string,
    timeframe: TimeframeType,
    periodStart: Date,
  ): Promise<void> {
    try {
      const message = {
        userId,
        timeframeType: timeframe,
        periodStart: periodStart.toISOString(),
      };
      
      const result = await this.pubSubService.publishMessage(
        USER_ACTION_PUBSUB_TOPICS.CALCULATE_USER_METRICS,
        message,
      );
      
      if (!result.success) {
        this.logger.error(`Failed to publish user metrics calculation message: ${result.error}`);
      }
    } catch (error) {
      this.logger.error(`Error scheduling user metrics calculation: ${error.message}`);
    }
  }
  
  /**
   * Schedule organization metrics calculation via PubSub
   */
  private async scheduleOrgMetricsCalculation(
    organizationId: string,
    timeframe: TimeframeType,
    periodStart: Date,
  ): Promise<void> {
    try {
      const message = {
        organizationId,
        timeframeType: timeframe,
        periodStart: periodStart.toISOString(),
      };
      
      const result = await this.pubSubService.publishMessage(
        USER_ACTION_PUBSUB_TOPICS.CALCULATE_ORG_METRICS,
        message,
      );
      
      if (!result.success) {
        this.logger.error(`Failed to publish org metrics calculation message: ${result.error}`);
      }
    } catch (error) {
      this.logger.error(`Error scheduling org metrics calculation: ${error.message}`);
    }
  }
}