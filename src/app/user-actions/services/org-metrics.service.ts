import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, In, Repository } from 'typeorm';
import { UserAction, UserActionType } from '../../../entities/user-action.entity';
import { TimeframeType } from '../../../entities/user-action-summary.entity';
import { OrganizationActionSummaryRepository } from '../repositories/organization-action-summary.repository';
import { OrgInsight, OrgMetricItem, OrgMetricTrend, OrgMetricsResponseDto } from '../dtos/org-metrics.dto';
import { CacheService } from '../../../cache/cache.service';
import { startOfDay, subDays, format, subWeeks, subMonths, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';
import { UserActionSummaryRepository } from '../repositories/user-action-summary.repository';

@Injectable()
export class OrgMetricsService {
  private readonly logger = new Logger(OrgMetricsService.name);
  private readonly CACHE_TTL = 24 * 60 * 60; // 24 hours in seconds

  constructor(
    @InjectRepository(UserAction)
    private readonly userActionRepository: Repository<UserAction>,
    private readonly organizationActionSummaryRepository: OrganizationActionSummaryRepository,
    private readonly userActionSummaryRepository: UserActionSummaryRepository,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Get a cache key for organization metrics
   */
  private getOrgMetricsCacheKey(orgId: string, timeframe: TimeframeType): string {
    return `org-metrics:${orgId}:${timeframe}`;
  }

  /**
   * Get metrics for an organization within the specified timeframe
   */
  async getOrgMetrics(
    organizationId: string,
    timeframe: TimeframeType,
    forceFresh: boolean = false,
  ): Promise<OrgMetricsResponseDto> {
    const cacheKey = this.getOrgMetricsCacheKey(organizationId, timeframe);
    
    // Return cached data if available and fresh data isn't forced
    if (!forceFresh) {
      const cachedData = await this.cacheService.get<OrgMetricsResponseDto>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    // Define date range based on timeframe
    const { startDate, endDate } = this.getDateRangeForTimeframe(timeframe);
    
    // Find all summaries for this organization in the given timeframe
    const summaries = await this.organizationActionSummaryRepository.getSummariesInRange(
      organizationId,
      timeframe,
      startDate,
      endDate,
    );

    if (summaries.length === 0) {
      // No summary data exists, trigger calculation and return empty response
      this.logger.log(`No metrics found for organization ${organizationId} in timeframe ${timeframe}, triggering calculation`);
      await this.calculateOrgMetrics(organizationId, timeframe, startDate);
      
      return this.getEmptyMetricsResponse(organizationId, timeframe, startDate, endDate);
    }

    // Process summaries into a response object
    const response = this.processOrgSummaries(organizationId, timeframe, startDate, endDate, summaries);
    
    // Cache the response
    await this.cacheService.set(cacheKey, response, this.CACHE_TTL);
    
    return response;
  }

  /**
   * Create an empty metrics response
   */
  private getEmptyMetricsResponse(
    organizationId: string,
    timeframe: TimeframeType,
    startDate: Date,
    endDate: Date,
  ): OrgMetricsResponseDto {
    return {
      organizationId,
      timeframe,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      metrics: {
        totalUsers: 0,
        activeUsers: 0,
        totalSearches: 0,
        totalViews: 0,
        totalSaves: 0,
        totalApplications: 0,
        
        searchesPerUser: 0,
        viewsPerUser: 0,
        savesPerUser: 0,
        applicationsPerUser: 0,
        
        activeUsersTrend: { data: [], trend: 'stable', changePercentage: 0 },
        searchesTrend: { data: [], trend: 'stable', changePercentage: 0 },
        viewsTrend: { data: [], trend: 'stable', changePercentage: 0 },
        savesTrend: { data: [], trend: 'stable', changePercentage: 0 },
        applicationsTrend: { data: [], trend: 'stable', changePercentage: 0 },
        
        topSearchCategories: {},
        topViewedCategories: {},
        topSavedCategories: {},
        topAppliedCategories: {},
      },
      insights: [
        {
          type: 'info',
          message: 'Not enough data to generate organization insights yet',
          priority: 'low',
        },
      ],
      lastUpdated: new Date().toISOString(),
    };
  }

  /**
   * Process organization summaries into a response object
   */
  private processOrgSummaries(
    organizationId: string,
    timeframe: TimeframeType,
    startDate: Date,
    endDate: Date, 
    summaries: any[],
  ): OrgMetricsResponseDto {
    // Get the most recent summary for totals
    const latestSummary = summaries[summaries.length - 1];
    
    // Extract metrics from the latest summary
    const metrics = latestSummary.metrics || {};
    
    // Calculate trends
    const activeUsersTrend = this.calculateTrend(summaries, 'activeUsers');
    const searchesTrend = this.calculateTrend(summaries, 'totalSearches');
    const viewsTrend = this.calculateTrend(summaries, 'totalViews');
    const savesTrend = this.calculateTrend(summaries, 'totalSaves');
    const applicationsTrend = this.calculateTrend(summaries, 'totalApplications');
    
    return {
      organizationId,
      timeframe,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      metrics: {
        totalUsers: metrics.totalUsers || 0,
        activeUsers: metrics.activeUsers || 0,
        totalSearches: metrics.totalSearches || 0,
        totalViews: metrics.totalViews || 0,
        totalSaves: metrics.totalSaves || 0,
        totalApplications: metrics.totalApplications || 0,
        
        searchesPerUser: metrics.searchesPerUser || 0,
        viewsPerUser: metrics.viewsPerUser || 0,
        savesPerUser: metrics.savesPerUser || 0,
        applicationsPerUser: metrics.applicationsPerUser || 0,
        
        activeUsersTrend,
        searchesTrend,
        viewsTrend,
        savesTrend,
        applicationsTrend,
        
        topSearchCategories: metrics.topSearchCategories || {},
        topViewedCategories: metrics.topViewedCategories || {},
        topSavedCategories: metrics.topSavedCategories || {},
        topAppliedCategories: metrics.topAppliedCategories || {},
      },
      insights: (metrics.insights || []).map(i => ({
        type: i.type,
        message: i.message,
        priority: i.priority,
        relatedMetrics: i.relatedMetrics,
      })),
      lastUpdated: latestSummary.lastProcessedAt?.toISOString() || new Date().toISOString(),
    };
  }

  /**
   * Calculate a metric trend from summaries
   */
  private calculateTrend(summaries: any[], metricKey: string): OrgMetricTrend {
    if (!summaries.length) {
      return { data: [], trend: 'stable', changePercentage: 0 };
    }
    
    // Map summaries to metric items
    const data = summaries.map(summary => ({
      count: summary.metrics?.[metricKey] || 0,
      date: format(new Date(summary.periodStart), 'yyyy-MM-dd'),
    }));
    
    // Calculate trend direction and percentage change
    let trend: 'up' | 'down' | 'stable' = 'stable';
    let changePercentage = 0;
    
    if (data.length >= 2) {
      const firstValue = data[0].count;
      const lastValue = data[data.length - 1].count;
      
      if (firstValue === 0) {
        trend = lastValue > 0 ? 'up' : 'stable';
        changePercentage = lastValue > 0 ? 100 : 0;
      } else {
        changePercentage = ((lastValue - firstValue) / firstValue) * 100;
        trend = changePercentage > 5 ? 'up' : (changePercentage < -5 ? 'down' : 'stable');
      }
    }
    
    return {
      data,
      trend,
      changePercentage: Math.round(changePercentage * 100) / 100, // Round to 2 decimal places
    };
  }

  /**
   * Get date range for specified timeframe
   */
  private getDateRangeForTimeframe(timeframe: TimeframeType): { startDate: Date, endDate: Date } {
    const now = new Date();
    let startDate: Date;
    let endDate: Date = endOfDay(now);
    
    switch (timeframe) {
      case TimeframeType.DAILY:
        startDate = startOfDay(subDays(now, 30)); // Last 30 days
        break;
      case TimeframeType.WEEKLY:
        startDate = startOfWeek(subWeeks(now, 12)); // Last 12 weeks
        break;
      case TimeframeType.MONTHLY:
        startDate = startOfMonth(subMonths(now, 12)); // Last 12 months
        break;
      default:
        startDate = startOfDay(subDays(now, 30)); // Default to 30 days
    }
    
    return { startDate, endDate };
  }

  /**
   * Calculate metrics for an organization in the specified timeframe
   */
  async calculateOrgMetrics(
    organizationId: string,
    timeframe: TimeframeType,
    periodStart: Date,
  ): Promise<void> {
    this.logger.log(`Calculating ${timeframe} metrics for organization ${organizationId} starting from ${periodStart}`);
    
    try {
      // Define date range based on timeframe
      const periodEnd = this.getPeriodEndDate(timeframe, periodStart);
      
      // Find or create a summary record
      const summary = await this.organizationActionSummaryRepository.findOrCreate(
        organizationId,
        timeframe,
        periodStart,
      );
      
      // Fetch all users in this organization
      const orgUsers = await this.getOrganizationUsers(organizationId);
      const totalUsers = orgUsers.length;
      
      if (totalUsers === 0) {
        this.logger.warn(`No users found for organization ${organizationId}`);
        return;
      }
      
      // Get active users (those with at least one action in the period)
      const activeUserIds = await this.getActiveUserIds(orgUsers, periodStart, periodEnd);
      const activeUsers = activeUserIds.length;
      
      // Count user actions by type within the period for org users
      const activityCounts = await this.countOrgUserActionsByTypeInPeriod(
        orgUsers,
        periodStart,
        periodEnd,
      );
      
      // Calculate per-user averages
      const searchesPerUser = activeUsers > 0 ? activityCounts.totalSearches / activeUsers : 0;
      const viewsPerUser = activeUsers > 0 ? activityCounts.totalViews / activeUsers : 0;
      const savesPerUser = activeUsers > 0 ? activityCounts.totalSaves / activeUsers : 0;
      const applicationsPerUser = activeUsers > 0 ? activityCounts.totalApplications / activeUsers : 0;
      
      // Get top categories
      const topCategories = await this.getTopCategoriesByActionType(
        orgUsers,
        periodStart,
        periodEnd,
      );
      
      // Generate insights
      const insights = this.generateOrgInsights(
        totalUsers,
        activeUsers,
        activityCounts,
        { searchesPerUser, viewsPerUser, savesPerUser, applicationsPerUser }
      );
      
      // Prepare metrics object
      const metrics = {
        totalUsers,
        activeUsers,
        ...activityCounts,
        searchesPerUser: Math.round(searchesPerUser * 100) / 100,
        viewsPerUser: Math.round(viewsPerUser * 100) / 100,
        savesPerUser: Math.round(savesPerUser * 100) / 100,
        applicationsPerUser: Math.round(applicationsPerUser * 100) / 100,
        ...topCategories,
        insights,
      };
      
      // Save the calculated metrics
      await this.organizationActionSummaryRepository.saveMetrics(
        summary.id, // ID is already a number from the BaseEntity
        metrics,
        totalUsers, // update member count
      );
      
      // Clear the cache for this organization's metrics
      await this.cacheService.delete(this.getOrgMetricsCacheKey(organizationId, timeframe));
      
      this.logger.log(`Successfully calculated ${timeframe} metrics for organization ${organizationId}`);
    } catch (error) {
      this.logger.error(`Error calculating organization metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get the end date for a period based on the start date and timeframe
   */
  private getPeriodEndDate(timeframe: TimeframeType, periodStart: Date): Date {
    switch (timeframe) {
      case TimeframeType.DAILY:
        return endOfDay(periodStart);
      case TimeframeType.WEEKLY:
        return endOfWeek(periodStart);
      case TimeframeType.MONTHLY:
        return endOfMonth(periodStart);
      default:
        return endOfDay(periodStart);
    }
  }

  /**
   * Get all users in an organization
   * Note: This is a stub - in a real implementation, you would fetch from a user service
   */
  private async getOrganizationUsers(organizationId: string): Promise<string[]> {
    // In a real implementation, this would fetch from a user service or database
    // For now, we'll simulate by fetching all users with actions connected to this org
    try {
      const result = await this.userActionRepository
        .createQueryBuilder('action')
        .select('DISTINCT action.userId')
        .where('action.metadata->>"organizationId" = :organizationId', { organizationId })
        .getRawMany();
      
      return result.map(r => r.userId);
    } catch (error) {
      this.logger.error(`Error fetching organization users: ${error.message}`);
      return [];
    }
  }

  /**
   * Get IDs of users who were active in the specified period
   */
  private async getActiveUserIds(
    userIds: string[],
    startDate: Date,
    endDate: Date,
  ): Promise<string[]> {
    if (userIds.length === 0) {
      return [];
    }
    
    const result = await this.userActionRepository
      .createQueryBuilder('action')
      .select('DISTINCT action.userId')
      .where('action.userId IN (:...userIds)', { userIds })
      .andWhere('action.createdAt BETWEEN :startDate AND :endDate', { startDate, endDate })
      .getRawMany();
    
    return result.map(r => r.userId);
  }

  /**
   * Count organization user actions by type within a period
   */
  private async countOrgUserActionsByTypeInPeriod(
    userIds: string[],
    startDate: Date,
    endDate: Date,
  ): Promise<Record<string, number>> {
    if (userIds.length === 0) {
      return {
        totalSearches: 0,
        totalViews: 0,
        totalSaves: 0,
        totalApplications: 0,
      };
    }
    
    // Count searches
    const searchCount = await this.userActionRepository.count({
      where: {
        userId: In(userIds),
        actionType: UserActionType.SEARCH,
        createdAt: Between(startDate, endDate),
      },
    });
    
    // Count views
    const viewCount = await this.userActionRepository.count({
      where: {
        userId: In(userIds),
        actionType: UserActionType.VIEW,
        createdAt: Between(startDate, endDate),
      },
    });
    
    // Count saves
    const saveCount = await this.userActionRepository.count({
      where: {
        userId: In(userIds),
        actionType: UserActionType.SAVE,
        createdAt: Between(startDate, endDate),
      },
    });
    
    // Count applications
    const applyCount = await this.userActionRepository.count({
      where: {
        userId: In(userIds),
        actionType: UserActionType.APPLY,
        createdAt: Between(startDate, endDate),
      },
    });
    
    return {
      totalSearches: searchCount,
      totalViews: viewCount,
      totalSaves: saveCount,
      totalApplications: applyCount,
    };
  }

  /**
   * Get top categories for different action types
   */
  private async getTopCategoriesByActionType(
    userIds: string[],
    startDate: Date,
    endDate: Date,
  ): Promise<Record<string, Record<string, number>>> {
    // This would be implemented with database-specific JSON querying
    // For now, return empty objects as placeholders
    return {
      topSearchCategories: {},
      topViewedCategories: {},
      topSavedCategories: {},
      topAppliedCategories: {},
    };
  }

  /**
   * Generate insights based on metrics
   */
  private generateOrgInsights(
    totalUsers: number,
    activeUsers: number,
    activityCounts: Record<string, number>,
    perUserMetrics: Record<string, number>,
  ): OrgInsight[] {
    const insights: OrgInsight[] = [];
    
    // Insight: User engagement
    const activeUserPercentage = totalUsers > 0 ? (activeUsers / totalUsers) * 100 : 0;
    
    if (activeUserPercentage < 30) {
      insights.push({
        type: 'opportunity',
        message: `Only ${Math.round(activeUserPercentage)}% of your organization's users are actively engaged. Consider sending email reminders.`,
        priority: 'high',
        relatedMetrics: ['activeUsers', 'totalUsers'],
      });
    } else if (activeUserPercentage > 70) {
      insights.push({
        type: 'positive',
        message: `Strong engagement: ${Math.round(activeUserPercentage)}% of your organization's users are actively using the platform.`,
        priority: 'medium',
        relatedMetrics: ['activeUsers', 'totalUsers'],
      });
    }
    
    // Insight: Application funnel
    const { totalViews, totalApplications } = activityCounts;
    if (totalViews > 20) {
      const applicationRate = totalViews > 0 ? (totalApplications / totalViews) * 100 : 0;
      
      if (applicationRate < 5) {
        insights.push({
          type: 'opportunity',
          message: `Low application rate (${Math.round(applicationRate)}%). Users are viewing jobs but not applying. Consider skills training or resume workshops.`,
          priority: 'medium',
          relatedMetrics: ['totalViews', 'totalApplications'],
        });
      } else if (applicationRate > 20) {
        insights.push({
          type: 'positive',
          message: `High application rate (${Math.round(applicationRate)}%). Your users are actively applying to viewed jobs.`,
          priority: 'medium',
          relatedMetrics: ['totalViews', 'totalApplications'],
        });
      }
    }
    
    // If no insights generated, add a generic one
    if (insights.length === 0) {
      insights.push({
        type: 'info',
        message: 'Keep encouraging your organization members to use the platform to generate more insights.',
        priority: 'low',
      });
    }
    
    return insights;
  }
}