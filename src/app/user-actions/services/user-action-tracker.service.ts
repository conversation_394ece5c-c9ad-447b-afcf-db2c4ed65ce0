import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { UserAction, UserActionSource, UserActionType } from '../../../entities/user-action.entity';

@Injectable()
export class UserActionTrackerService {
  private readonly logger = new Logger(UserActionTrackerService.name);

  constructor(
    @InjectRepository(UserAction)
    private readonly userActionRepository: Repository<UserAction>,
  ) {}

  /**
   * Track a user search action
   */
  async trackSearch(
    userId: string, 
    searchQuery: string,
    filters: Record<string, unknown>,
  ): Promise<void> {
    try {
      const action = new UserAction();
      action.userId = userId;
      action.actionType = UserActionType.SEARCH;
      action.source = UserActionSource.SEARCH;
      action.correlationId = uuidv4();
      action.metadata = {
        query: searchQuery,
        filters,
        timestamp: new Date().toISOString(),
      };

      await this.userActionRepository.save(action);
      this.logger.debug(`Tracked search for user ${userId}: ${searchQuery}`);
    } catch (error) {
      this.logger.error(
        `Failed to track search action: ${error.message}`,
        error.stack,
      );
      // We don't throw here to prevent affecting the user experience
    }
  }

  /**
   * Track when a job is viewed
   */
  async trackJobView(
    userId: string,
    jobExtId: string,
    source: UserActionSource,
    correlationId?: string,
    viewDurationMs?: number,
  ): Promise<void> {
    try {
      const action = new UserAction();
      action.userId = userId;
      action.job_ext_id = jobExtId;
      action.actionType = UserActionType.VIEW;
      action.source = source;
      action.correlationId = correlationId || uuidv4();
      action.metadata = {
        viewDurationMs,
        timestamp: new Date().toISOString(),
      };

      await this.userActionRepository.save(action);
      this.logger.debug(`Tracked job view for user ${userId}: ${jobExtId}`);
    } catch (error) {
      this.logger.error(
        `Failed to track job view action: ${error.message}`,
        error.stack,
      );
      // We don't throw here to prevent affecting the user experience
    }
  }

  /**
   * Track when a job is saved
   */
  async trackJobSave(
    userId: string,
    jobExtId: string,
    source: UserActionSource,
    correlationId?: string,
  ): Promise<void> {
    try {
      const action = new UserAction();
      action.userId = userId;
      action.job_ext_id = jobExtId;
      action.actionType = UserActionType.SAVE;
      action.source = source;
      action.correlationId = correlationId || uuidv4();
      action.metadata = {
        timestamp: new Date().toISOString(),
      };

      await this.userActionRepository.save(action);
      this.logger.debug(`Tracked job save for user ${userId}: ${jobExtId}`);
    } catch (error) {
      this.logger.error(
        `Failed to track job save action: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Track when a job is unsaved
   */
  async trackJobUnsave(
    userId: string,
    jobExtId: string,
    source: UserActionSource,
    correlationId?: string,
  ): Promise<void> {
    try {
      const action = new UserAction();
      action.userId = userId;
      action.job_ext_id = jobExtId;
      action.actionType = UserActionType.UNSAVE;
      action.source = source;
      action.correlationId = correlationId || uuidv4();
      action.metadata = {
        timestamp: new Date().toISOString(),
      };

      await this.userActionRepository.save(action);
      this.logger.debug(`Tracked job unsave for user ${userId}: ${jobExtId}`);
    } catch (error) {
      this.logger.error(
        `Failed to track job unsave action: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Track when a job is applied to
   */
  async trackJobApply(
    userId: string,
    jobExtId: string,
    source: UserActionSource,
    correlationId?: string,
    applicationData?: Record<string, unknown>,
  ): Promise<void> {
    try {
      const action = new UserAction();
      action.userId = userId;
      action.job_ext_id = jobExtId;
      action.actionType = UserActionType.APPLY;
      action.source = source;
      action.correlationId = correlationId || uuidv4();
      action.metadata = {
        applicationData,
        timestamp: new Date().toISOString(),
      };

      await this.userActionRepository.save(action);
      this.logger.debug(`Tracked job application for user ${userId}: ${jobExtId}`);
    } catch (error) {
      this.logger.error(
        `Failed to track job application action: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Track when recommendations are shown to a user
   */
  async trackRecommendationsShown(
    userId: string,
    jobExtIds: string[],
    source: UserActionSource = UserActionSource.FOR_YOU,
  ): Promise<string> {
    try {
      const correlationId = uuidv4();
      const action = new UserAction();
      action.userId = userId;
      action.actionType = UserActionType.RECOMMENDATION_SHOWN;
      action.source = source;
      action.correlationId = correlationId;
      action.metadata = {
        jobIds: jobExtIds,
        count: jobExtIds.length,
        timestamp: new Date().toISOString(),
      };

      await this.userActionRepository.save(action);
      this.logger.debug(
        `Tracked ${jobExtIds.length} recommendations shown to user ${userId}`,
      );
      return correlationId;
    } catch (error) {
      this.logger.error(
        `Failed to track recommendations shown: ${error.message}`,
        error.stack,
      );
      return uuidv4(); // Return a new ID anyway to not break the flow
    }
  }

  /**
   * Track when a user clicks on a recommendation
   */
  async trackRecommendationClicked(
    userId: string,
    jobExtId: string,
    correlationId: string,
    position: number,
  ): Promise<void> {
    try {
      const action = new UserAction();
      action.userId = userId;
      action.job_ext_id = jobExtId;
      action.actionType = UserActionType.RECOMMENDATION_CLICKED;
      action.source = UserActionSource.FOR_YOU;
      action.correlationId = correlationId;
      action.metadata = {
        position,
        timestamp: new Date().toISOString(),
      };

      await this.userActionRepository.save(action);
      this.logger.debug(
        `Tracked recommendation click for user ${userId}: ${jobExtId} at position ${position}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to track recommendation click: ${error.message}`,
        error.stack,
      );
    }
  }
}
