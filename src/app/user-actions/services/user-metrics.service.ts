import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { UserAction, UserActionType } from '../../../entities/user-action.entity';
import { TimeframeType } from '../../../entities/user-action-summary.entity';
import { UserActionSummaryRepository } from '../repositories/user-action-summary.repository';
import { UserInsight, UserMetricItem, UserMetricTrend, UserMetricsResponseDto } from '../dtos/user-metrics.dto';
import { CacheService } from '../../../cache/cache.service';
import { startOfDay, subDays, format, subWeeks, subMonths, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

@Injectable()
export class UserMetricsService {
  private readonly logger = new Logger(UserMetricsService.name);
  private readonly CACHE_TTL = 4 * 60 * 60; // 4 hours in seconds

  constructor(
    @InjectRepository(UserAction)
    private readonly userActionRepository: Repository<UserAction>,
    private readonly userActionSummaryRepository: UserActionSummaryRepository,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Get a cache key for user metrics
   */
  private getUserMetricsCacheKey(userId: string, timeframe: TimeframeType): string {
    return `user-metrics:${userId}:${timeframe}`;
  }

  /**
   * Get metrics for a user within the specified timeframe
   */
  async getUserMetrics(
    userId: string,
    timeframe: TimeframeType,
    forceFresh: boolean = false,
  ): Promise<UserMetricsResponseDto> {
    const cacheKey = this.getUserMetricsCacheKey(userId, timeframe);
    
    // Return cached data if available and fresh data isn't forced
    if (!forceFresh) {
      const cachedData = await this.cacheService.get<UserMetricsResponseDto>(cacheKey);
      if (cachedData) {
        return cachedData;
      }
    }

    // Define date range based on timeframe
    const { startDate, endDate } = this.getDateRangeForTimeframe(timeframe);
    
    // Find all summaries for this user in the given timeframe
    const summaries = await this.userActionSummaryRepository.getSummariesInRange(
      userId,
      timeframe,
      startDate,
      endDate,
    );

    if (summaries.length === 0) {
      // No summary data exists, trigger calculation and return empty response
      this.logger.log(`No metrics found for user ${userId} in timeframe ${timeframe}, triggering calculation`);
      await this.calculateUserMetrics(userId, timeframe, startDate);
      
      return this.getEmptyMetricsResponse(userId, timeframe, startDate, endDate);
    }

    // Process summaries into a response object
    const response = this.processUserSummaries(userId, timeframe, startDate, endDate, summaries);
    
    // Cache the response
    await this.cacheService.set(cacheKey, response, this.CACHE_TTL);
    
    return response;
  }

  /**
   * Create an empty metrics response
   */
  private getEmptyMetricsResponse(
    userId: string,
    timeframe: TimeframeType,
    startDate: Date,
    endDate: Date,
  ): UserMetricsResponseDto {
    return {
      userId,
      timeframe,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      metrics: {
        totalSearches: 0,
        totalViews: 0,
        totalSaves: 0,
        totalApplications: 0,
        totalRecommendationViews: 0,
        totalRecommendationClicks: 0,
        clickThroughRate: 0,
        applicationRate: 0,
        
        searchesTrend: { data: [], trend: 'stable', changePercentage: 0 },
        viewsTrend: { data: [], trend: 'stable', changePercentage: 0 },
        savesTrend: { data: [], trend: 'stable', changePercentage: 0 },
        applicationsTrend: { data: [], trend: 'stable', changePercentage: 0 },
      },
      insights: [
        {
          type: 'info',
          message: 'Not enough data to generate insights yet',
          priority: 'low',
        },
      ],
      lastUpdated: new Date().toISOString(),
    };
  }

  /**
   * Process user summaries into a response object
   */
  private processUserSummaries(
    userId: string,
    timeframe: TimeframeType,
    startDate: Date,
    endDate: Date, 
    summaries: any[],
  ): UserMetricsResponseDto {
    // Get the most recent summary for totals
    const latestSummary = summaries[summaries.length - 1];
    
    // Extract metrics from the latest summary
    const metrics = latestSummary.metrics || {};
    
    // Calculate trends
    const searchesTrend = this.calculateTrend(summaries, 'searchCount');
    const viewsTrend = this.calculateTrend(summaries, 'viewCount');
    const savesTrend = this.calculateTrend(summaries, 'saveCount');
    const applicationsTrend = this.calculateTrend(summaries, 'applyCount');
    
    // Extract or generate insights
    const insights = latestSummary.insights || [];
    
    return {
      userId,
      timeframe,
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString(),
      metrics: {
        totalSearches: metrics.totalSearches || 0,
        totalViews: metrics.totalViews || 0,
        totalSaves: metrics.totalSaves || 0,
        totalApplications: metrics.totalApplications || 0,
        totalRecommendationViews: metrics.totalRecommendationViews || 0,
        totalRecommendationClicks: metrics.totalRecommendationClicks || 0,
        clickThroughRate: metrics.clickThroughRate || 0,
        applicationRate: metrics.applicationRate || 0,
        
        searchesTrend,
        viewsTrend,
        savesTrend,
        applicationsTrend,
        
        searchesByCategory: metrics.searchesByCategory || {},
        viewsByCategory: metrics.viewsByCategory || {},
        savesByCategory: metrics.savesByCategory || {},
        applicationsByCategory: metrics.applicationsByCategory || {},
      },
      insights: insights.map(i => ({
        type: i.type,
        message: i.message,
        priority: i.priority,
        relatedMetrics: i.relatedMetrics,
      })),
      lastUpdated: latestSummary.lastProcessedAt?.toISOString() || new Date().toISOString(),
    };
  }

  /**
   * Calculate a metric trend from summaries
   */
  private calculateTrend(summaries: any[], metricKey: string): UserMetricTrend {
    if (!summaries.length) {
      return { data: [], trend: 'stable', changePercentage: 0 };
    }
    
    // Map summaries to metric items
    const data = summaries.map(summary => ({
      count: summary.metrics?.[metricKey] || 0,
      date: format(new Date(summary.periodStart), 'yyyy-MM-dd'),
    }));
    
    // Calculate trend direction and percentage change
    let trend: 'up' | 'down' | 'stable' = 'stable';
    let changePercentage = 0;
    
    if (data.length >= 2) {
      const firstValue = data[0].count;
      const lastValue = data[data.length - 1].count;
      
      if (firstValue === 0) {
        trend = lastValue > 0 ? 'up' : 'stable';
        changePercentage = lastValue > 0 ? 100 : 0;
      } else {
        changePercentage = ((lastValue - firstValue) / firstValue) * 100;
        trend = changePercentage > 5 ? 'up' : (changePercentage < -5 ? 'down' : 'stable');
      }
    }
    
    return {
      data,
      trend,
      changePercentage: Math.round(changePercentage * 100) / 100, // Round to 2 decimal places
    };
  }

  /**
   * Get date range for specified timeframe
   */
  private getDateRangeForTimeframe(timeframe: TimeframeType): { startDate: Date, endDate: Date } {
    const now = new Date();
    let startDate: Date;
    let endDate: Date = endOfDay(now);
    
    switch (timeframe) {
      case TimeframeType.DAILY:
        startDate = startOfDay(subDays(now, 30)); // Last 30 days
        break;
      case TimeframeType.WEEKLY:
        startDate = startOfWeek(subWeeks(now, 12)); // Last 12 weeks
        break;
      case TimeframeType.MONTHLY:
        startDate = startOfMonth(subMonths(now, 12)); // Last 12 months
        break;
      default:
        startDate = startOfDay(subDays(now, 30)); // Default to 30 days
    }
    
    return { startDate, endDate };
  }

  /**
   * Calculate metrics for a user in the specified timeframe
   */
  async calculateUserMetrics(
    userId: string,
    timeframe: TimeframeType,
    periodStart: Date,
  ): Promise<void> {
    this.logger.log(`Calculating ${timeframe} metrics for user ${userId} starting from ${periodStart}`);
    
    try {
      // Define date range based on timeframe
      const periodEnd = this.getPeriodEndDate(timeframe, periodStart);
      
      // Find or create a summary record
      const summary = await this.userActionSummaryRepository.findOrCreate(
        userId,
        timeframe,
        periodStart,
      );
      
      // Count user actions by type within the period
      const metrics = await this.countUserActionsByTypeInPeriod(userId, periodStart, periodEnd);
      
      // Calculate derived metrics
      const derivedMetrics = this.calculateDerivedMetrics(metrics);
      
      // Generate insights based on the metrics
      const insights = await this.generateUserInsights(userId, timeframe, metrics, derivedMetrics);
      
      // Save the calculated metrics and insights
      await this.userActionSummaryRepository.saveMetrics(
        summary.id, // ID is already a number from the BaseEntity
        { ...metrics, ...derivedMetrics },
        insights,
      );
      
      // Clear the cache for this user's metrics
      await this.cacheService.delete(this.getUserMetricsCacheKey(userId, timeframe));
      
      this.logger.log(`Successfully calculated ${timeframe} metrics for user ${userId}`);
    } catch (error) {
      this.logger.error(`Error calculating user metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get the end date for a period based on the start date and timeframe
   */
  private getPeriodEndDate(timeframe: TimeframeType, periodStart: Date): Date {
    switch (timeframe) {
      case TimeframeType.DAILY:
        return endOfDay(periodStart);
      case TimeframeType.WEEKLY:
        return endOfWeek(periodStart);
      case TimeframeType.MONTHLY:
        return endOfMonth(periodStart);
      default:
        return endOfDay(periodStart);
    }
  }

  /**
   * Count user actions by type within a period
   */
  private async countUserActionsByTypeInPeriod(
    userId: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Record<string, any>> {
    const searchCount = await this.countActionsOfType(userId, UserActionType.SEARCH, startDate, endDate);
    const viewCount = await this.countActionsOfType(userId, UserActionType.VIEW, startDate, endDate);
    const saveCount = await this.countActionsOfType(userId, UserActionType.SAVE, startDate, endDate);
    const unsaveCount = await this.countActionsOfType(userId, UserActionType.UNSAVE, startDate, endDate);
    const applyCount = await this.countActionsOfType(userId, UserActionType.APPLY, startDate, endDate);
    const recommendationsShownCount = await this.countActionsOfType(userId, UserActionType.RECOMMENDATION_SHOWN, startDate, endDate);
    const recommendationsClickedCount = await this.countActionsOfType(userId, UserActionType.RECOMMENDATION_CLICKED, startDate, endDate);
    
    // Categorize actions (where possible)
    const searchesByCategory = await this.categorizationByField(userId, UserActionType.SEARCH, 'metadata.filters.category', startDate, endDate);
    const viewsByCategory = await this.categorizationByField(userId, UserActionType.VIEW, 'metadata.category', startDate, endDate);
    
    return {
      totalSearches: searchCount,
      totalViews: viewCount,
      totalSaves: saveCount,
      totalUnsaves: unsaveCount,
      totalApplications: applyCount,
      totalRecommendationViews: recommendationsShownCount,
      totalRecommendationClicks: recommendationsClickedCount,
      
      // Save category breakdowns
      searchesByCategory,
      viewsByCategory,
    };
  }

  /**
   * Count actions of a specific type
   */
  private async countActionsOfType(
    userId: string,
    actionType: UserActionType,
    startDate: Date,
    endDate: Date,
  ): Promise<number> {
    return this.userActionRepository.count({
      where: {
        userId,
        actionType,
        createdAt: Between(startDate, endDate),
      },
    });
  }

  /**
   * Get categorization of actions by a metadata field
   */
  private async categorizationByField(
    userId: string,
    actionType: UserActionType,
    metadataField: string,
    startDate: Date,
    endDate: Date,
  ): Promise<Record<string, number>> {
    // For simplicity, this is a placeholder. In a real implementation,
    // you would use database-specific JSON extraction to group by metadata field.
    return {};
  }

  /**
   * Calculate derived metrics from base metrics
   */
  private calculateDerivedMetrics(metrics: Record<string, any>): Record<string, any> {
    const {
      totalViews,
      totalSaves,
      totalApplications,
      totalRecommendationViews,
      totalRecommendationClicks,
    } = metrics;
    
    // Calculate click-through rate for recommendations
    const clickThroughRate = totalRecommendationViews > 0
      ? (totalRecommendationClicks / totalRecommendationViews) * 100
      : 0;
    
    // Calculate application rate (applications per view)
    const applicationRate = totalViews > 0
      ? (totalApplications / totalViews) * 100
      : 0;
    
    // Calculate save rate (saves per view)
    const saveRate = totalViews > 0
      ? (totalSaves / totalViews) * 100
      : 0;
    
    return {
      clickThroughRate: Math.round(clickThroughRate * 100) / 100, // Round to 2 decimal places
      applicationRate: Math.round(applicationRate * 100) / 100,
      saveRate: Math.round(saveRate * 100) / 100,
    };
  }

  /**
   * Generate insights based on metrics
   */
  private async generateUserInsights(
    userId: string,
    timeframe: TimeframeType,
    metrics: Record<string, any>,
    derivedMetrics: Record<string, any>,
  ): Promise<UserInsight[]> {
    const insights: UserInsight[] = [];
    
    // Get previous period's metrics for comparison
    const previousPeriodMetrics = await this.getPreviousPeriodMetrics(userId, timeframe);
    
    // Example insight: Application rate trending up or down
    if (previousPeriodMetrics?.applicationRate) {
      const currentRate = derivedMetrics.applicationRate;
      const previousRate = previousPeriodMetrics.applicationRate;
      const difference = currentRate - previousRate;
      
      if (difference >= 5) {
        insights.push({
          type: 'positive',
          message: `Your application rate has increased by ${Math.round(difference)}% compared to the previous period.`,
          priority: 'medium',
          relatedMetrics: ['applicationRate'],
        });
      } else if (difference <= -5) {
        insights.push({
          type: 'opportunity',
          message: `Your application rate has decreased by ${Math.round(Math.abs(difference))}% compared to the previous period.`,
          priority: 'medium',
          relatedMetrics: ['applicationRate'],
        });
      }
    }
    
    // Example insight: Search to application funnel
    const { totalSearches, totalViews, totalApplications } = metrics;
    
    if (totalSearches > 0 && totalViews > 0) {
      const searchToViewRate = (totalViews / totalSearches) * 100;
      if (searchToViewRate < 20) {
        insights.push({
          type: 'opportunity',
          message: 'Consider refining your search terms. Only a small percentage of your searches lead to job views.',
          priority: 'medium',
          relatedMetrics: ['totalSearches', 'totalViews'],
        });
      }
    }
    
    // If no specific insights, add a generic one
    if (insights.length === 0) {
      insights.push({
        type: 'info',
        message: 'Keep using the platform to generate more personalized insights.',
        priority: 'low',
      });
    }
    
    return insights;
  }

  /**
   * Get metrics from the previous period for comparison
   */
  private async getPreviousPeriodMetrics(
    userId: string,
    timeframe: TimeframeType,
  ): Promise<Record<string, any> | null> {
    // Calculate the start date for the previous period
    const now = new Date();
    let previousPeriodStart: Date;
    
    switch (timeframe) {
      case TimeframeType.DAILY:
        previousPeriodStart = subDays(now, 1);
        break;
      case TimeframeType.WEEKLY:
        previousPeriodStart = subWeeks(now, 1);
        break;
      case TimeframeType.MONTHLY:
        previousPeriodStart = subMonths(now, 1);
        break;
      default:
        previousPeriodStart = subDays(now, 1);
    }
    
    // Find the summary for the previous period
    const previousSummary = await this.userActionSummaryRepository.getSummariesInRange(
      userId,
      timeframe,
      previousPeriodStart,
      previousPeriodStart,
    );
    
    if (previousSummary.length === 0) {
      return null;
    }
    
    return previousSummary[0].metrics;
  }
}