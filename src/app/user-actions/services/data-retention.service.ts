import { Injectable, Logger } from '@nestjs/common';
import { <PERSON>ron } from '@nestjs/schedule';
import { InjectRepository } from '@nestjs/typeorm';
import { LessThan, Repository } from 'typeorm';
import { UserAction } from '../../../entities/user-action.entity';
import { UserActionSummaryRepository } from '../repositories/user-action-summary.repository';
import { OrganizationActionSummaryRepository } from '../repositories/organization-action-summary.repository';
import { TimeframeType } from '../../../entities/user-action-summary.entity';
import { subDays } from 'date-fns';

@Injectable()
export class DataRetentionService {
  private readonly logger = new Logger(DataRetentionService.name);
  
  constructor(
    @InjectRepository(UserAction)
    private readonly userActionRepository: Repository<UserAction>,
    private readonly userActionSummaryRepository: UserActionSummaryRepository,
    private readonly orgActionSummaryRepository: OrganizationActionSummaryRepository,
  ) {}
  
  /**
   * Run daily at 1:00 AM to clean up old data
   */
  @Cron('0 1 * * *')
  async cleanupOldData() {
    this.logger.log('Starting scheduled data retention cleanup');
    
    try {
      // Delete raw user actions older than 90 days
      await this.cleanupRawUserActions();
      
      // Delete old summaries based on retention policy
      await this.cleanupOldSummaries();
      
      this.logger.log('Completed data retention cleanup');
    } catch (error) {
      this.logger.error(`Error in data retention cleanup: ${error.message}`, error.stack);
    }
  }
  
  /**
   * Delete raw user actions older than 90 days
   */
  private async cleanupRawUserActions(): Promise<void> {
    const cutoffDate = subDays(new Date(), 90);
    
    try {
      const result = await this.userActionRepository.delete({
        createdAt: LessThan(cutoffDate),
      });
      
      this.logger.log(`Deleted ${result.affected || 0} raw user actions older than 90 days`);
    } catch (error) {
      this.logger.error(`Error cleaning up raw user actions: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Delete old summaries based on retention policy
   * - Daily summaries: Keep 90 days
   * - Weekly summaries: Keep 52 weeks (1 year)
   * - Monthly summaries: Keep 24 months (2 years)
   */
  private async cleanupOldSummaries(): Promise<void> {
    try {
      // Delete old daily summaries (older than 90 days)
      const dailyCutoff = subDays(new Date(), 90);
      const dailyDeleted = await this.userActionSummaryRepository.deleteOldSummaries(
        dailyCutoff,
        TimeframeType.DAILY,
      );
      this.logger.log(`Deleted ${dailyDeleted} daily user action summaries older than 90 days`);
      
      // Delete old org daily summaries
      const orgDailyDeleted = await this.orgActionSummaryRepository.deleteOldSummaries(
        dailyCutoff,
        TimeframeType.DAILY,
      );
      this.logger.log(`Deleted ${orgDailyDeleted} daily organization action summaries older than 90 days`);
      
      // Delete old weekly summaries (older than 52 weeks)
      const weeklyCutoff = subDays(new Date(), 52 * 7);
      const weeklyDeleted = await this.userActionSummaryRepository.deleteOldSummaries(
        weeklyCutoff,
        TimeframeType.WEEKLY,
      );
      this.logger.log(`Deleted ${weeklyDeleted} weekly user action summaries older than 1 year`);
      
      // Delete old org weekly summaries
      const orgWeeklyDeleted = await this.orgActionSummaryRepository.deleteOldSummaries(
        weeklyCutoff,
        TimeframeType.WEEKLY,
      );
      this.logger.log(`Deleted ${orgWeeklyDeleted} weekly organization action summaries older than 1 year`);
      
      // Delete old monthly summaries (older than 24 months)
      const monthlyCutoff = subDays(new Date(), 24 * 30);
      const monthlyDeleted = await this.userActionSummaryRepository.deleteOldSummaries(
        monthlyCutoff,
        TimeframeType.MONTHLY,
      );
      this.logger.log(`Deleted ${monthlyDeleted} monthly user action summaries older than 2 years`);
      
      // Delete old org monthly summaries
      const orgMonthlyDeleted = await this.orgActionSummaryRepository.deleteOldSummaries(
        monthlyCutoff,
        TimeframeType.MONTHLY,
      );
      this.logger.log(`Deleted ${orgMonthlyDeleted} monthly organization action summaries older than 2 years`);
    } catch (error) {
      this.logger.error(`Error cleaning up old summaries: ${error.message}`);
      throw error;
    }
  }
}