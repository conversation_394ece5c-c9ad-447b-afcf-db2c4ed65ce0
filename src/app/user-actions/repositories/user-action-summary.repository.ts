import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, LessThanOrEqual, Repository } from 'typeorm';
import { UserActionSummary, TimeframeType } from '../../../entities/user-action-summary.entity';

@Injectable()
export class UserActionSummaryRepository {
  constructor(
    @InjectRepository(UserActionSummary)
    private readonly repository: Repository<UserActionSummary>,
  ) {}

  /**
   * Find a user action summary by id
   */
  async findById(id: number): Promise<UserActionSummary | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Find or create a summary for a user and timeframe
   */
  async findOrCreate(
    userId: string,
    timeframe: TimeframeType,
    periodStart: Date,
  ): Promise<UserActionSummary> {
    const existing = await this.repository.findOne({
      where: {
        userId,
        timeframe,
        periodStart,
      },
    });

    if (existing) {
      return existing;
    }

    const newSummary = new UserActionSummary();
    newSummary.userId = userId;
    newSummary.timeframe = timeframe;
    newSummary.periodStart = periodStart;
    newSummary.metrics = {};
    newSummary.insights = {};
    newSummary.isProcessed = false;

    return this.repository.save(newSummary);
  }

  /**
   * Save metrics to a user action summary
   */
  async saveMetrics(
    id: number,
    metrics: Record<string, any>,
    insights: Record<string, any> = {},
  ): Promise<UserActionSummary> {
    const summary = await this.findById(id);
    if (!summary) {
      throw new Error(`UserActionSummary with id ${id} not found`);
    }

    summary.metrics = metrics;
    summary.insights = insights;
    summary.isProcessed = true;
    summary.lastProcessedAt = new Date();

    return this.repository.save(summary);
  }

  /**
   * Get recent summaries for a user
   */
  async getRecentSummaries(
    userId: string,
    timeframe: TimeframeType,
    limit: number = 30, // Default to last 30 days/weeks/months
  ): Promise<UserActionSummary[]> {
    return this.repository.find({
      where: {
        userId,
        timeframe,
        isProcessed: true,
      },
      order: {
        periodStart: 'DESC',
      },
      take: limit,
    });
  }

  /**
   * Get summaries within a date range
   */
  async getSummariesInRange(
    userId: string,
    timeframe: TimeframeType,
    startDate: Date,
    endDate: Date,
  ): Promise<UserActionSummary[]> {
    return this.repository.find({
      where: {
        userId,
        timeframe,
        periodStart: Between(startDate, endDate),
        isProcessed: true,
      },
      order: {
        periodStart: 'ASC',
      },
    });
  }

  /**
   * Delete old summaries (data retention policy)
   */
  async deleteOldSummaries(
    olderThan: Date,
    timeframe?: TimeframeType,
  ): Promise<number> {
    const query = this.repository.createQueryBuilder()
      .delete()
      .where('periodStart <= :olderThan', { olderThan });

    if (timeframe) {
      query.andWhere('timeframe = :timeframe', { timeframe });
    }

    const result = await query.execute();
    return result.affected || 0;
  }
}