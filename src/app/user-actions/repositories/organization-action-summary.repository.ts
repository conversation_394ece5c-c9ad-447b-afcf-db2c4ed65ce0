import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { OrganizationActionSummary } from '../../../entities/organization-action-summary.entity';
import { TimeframeType } from '../../../entities/user-action-summary.entity';

@Injectable()
export class OrganizationActionSummaryRepository {
  constructor(
    @InjectRepository(OrganizationActionSummary)
    private readonly repository: Repository<OrganizationActionSummary>,
  ) {}

  /**
   * Find an organization action summary by id
   */
  async findById(id: number): Promise<OrganizationActionSummary | null> {
    return this.repository.findOne({ where: { id } });
  }

  /**
   * Find or create a summary for an organization and timeframe
   */
  async findOrCreate(
    organizationId: string,
    timeframe: TimeframeType,
    periodStart: Date,
    memberCount: number = 0,
  ): Promise<OrganizationActionSummary> {
    const existing = await this.repository.findOne({
      where: {
        organizationId,
        timeframe,
        periodStart,
      },
    });

    if (existing) {
      return existing;
    }

    const newSummary = new OrganizationActionSummary();
    newSummary.organizationId = organizationId;
    newSummary.timeframe = timeframe;
    newSummary.periodStart = periodStart;
    newSummary.metrics = {};
    newSummary.memberCount = memberCount;
    newSummary.isProcessed = false;

    return this.repository.save(newSummary);
  }

  /**
   * Save metrics to an organization action summary
   */
  async saveMetrics(
    id: number,
    metrics: Record<string, any>,
    memberCount?: number,
  ): Promise<OrganizationActionSummary> {
    const summary = await this.findById(id);
    if (!summary) {
      throw new Error(`OrganizationActionSummary with id ${id} not found`);
    }

    summary.metrics = metrics;
    if (memberCount !== undefined) {
      summary.memberCount = memberCount;
    }
    summary.isProcessed = true;
    summary.lastProcessedAt = new Date();

    return this.repository.save(summary);
  }

  /**
   * Get recent summaries for an organization
   */
  async getRecentSummaries(
    organizationId: string,
    timeframe: TimeframeType,
    limit: number = 12, // Default to last 12 periods (e.g., months)
  ): Promise<OrganizationActionSummary[]> {
    return this.repository.find({
      where: {
        organizationId,
        timeframe,
        isProcessed: true,
      },
      order: {
        periodStart: 'DESC',
      },
      take: limit,
    });
  }

  /**
   * Get summaries within a date range
   */
  async getSummariesInRange(
    organizationId: string,
    timeframe: TimeframeType,
    startDate: Date,
    endDate: Date,
  ): Promise<OrganizationActionSummary[]> {
    return this.repository.find({
      where: {
        organizationId,
        timeframe,
        periodStart: Between(startDate, endDate),
        isProcessed: true,
      },
      order: {
        periodStart: 'ASC',
      },
    });
  }

  /**
   * Delete old summaries (data retention policy)
   */
  async deleteOldSummaries(
    olderThan: Date,
    timeframe?: TimeframeType,
  ): Promise<number> {
    const query = this.repository.createQueryBuilder()
      .delete()
      .where('periodStart <= :olderThan', { olderThan });

    if (timeframe) {
      query.andWhere('timeframe = :timeframe', { timeframe });
    }

    const result = await query.execute();
    return result.affected || 0;
  }
}