export enum USER_ACTION_PUBSUB_TOPICS {
  CALCULATE_USER_METRICS = 'calculate-user-metrics',
  CALCULATE_ORG_METRICS = 'calculate-org-metrics',
}

export interface CalculateUserMetricsMessage {
  userId: string;
  timeframeType: string;
  periodStart: string;
  forceFresh?: boolean;
}

export interface CalculateOrgMetricsMessage {
  organizationId: string;
  timeframeType: string;
  periodStart: string;
  forceFresh?: boolean;
}