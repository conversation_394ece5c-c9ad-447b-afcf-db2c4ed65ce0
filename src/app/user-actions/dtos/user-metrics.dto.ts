import { TimeframeType } from '../../../entities/user-action-summary.entity';

export class UserMetricItem {
  count: number;
  date: string;
}

export class UserMetricTrend {
  data: UserMetricItem[];
  trend: 'up' | 'down' | 'stable';
  changePercentage?: number;
}

export class UserInsight {
  type: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  relatedMetrics?: string[];
}

export class UserMetricsResponseDto {
  userId: string;
  timeframe: TimeframeType;
  startDate: string;
  endDate: string;
  metrics: {
    totalSearches: number;
    totalViews: number;
    totalSaves: number;
    totalApplications: number;
    totalRecommendationViews: number;
    totalRecommendationClicks: number;
    clickThroughRate: number;
    applicationRate: number;
    
    // Trends over the specified timeframe
    searchesTrend: UserMetricTrend;
    viewsTrend: UserMetricTrend;
    savesTrend: UserMetricTrend;
    applicationsTrend: UserMetricTrend;
    
    // Category breakdowns
    searchesByCategory?: Record<string, number>;
    viewsByCategory?: Record<string, number>;
    savesByCategory?: Record<string, number>;
    applicationsByCategory?: Record<string, number>;
  };
  
  insights: UserInsight[];
  
  lastUpdated: string;
}