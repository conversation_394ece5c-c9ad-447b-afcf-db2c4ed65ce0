import { TimeframeType } from '../../../entities/user-action-summary.entity';

export class OrgMetricItem {
  count: number;
  date: string;
}

export class OrgMetricTrend {
  data: OrgMetricItem[];
  trend: 'up' | 'down' | 'stable';
  changePercentage?: number;
}

export class OrgInsight {
  type: string;
  message: string;
  priority: 'high' | 'medium' | 'low';
  relatedMetrics?: string[];
}

export class OrgMetricsResponseDto {
  organizationId: string;
  timeframe: TimeframeType;
  startDate: string;
  endDate: string;
  
  metrics: {
    // Overall metrics
    totalUsers: number;
    activeUsers: number;
    totalSearches: number;
    totalViews: number;
    totalSaves: number;
    totalApplications: number;
    
    // Per user averages
    searchesPerUser: number;
    viewsPerUser: number;
    savesPerUser: number;
    applicationsPerUser: number;
    
    // Trends
    activeUsersTrend: OrgMetricTrend;
    searchesTrend: OrgMetricTrend;
    viewsTrend: OrgMetricTrend;
    savesTrend: OrgMetricTrend;
    applicationsTrend: OrgMetricTrend;
    
    // Top categories
    topSearchCategories: Record<string, number>;
    topViewedCategories: Record<string, number>;
    topSavedCategories: Record<string, number>;
    topAppliedCategories: Record<string, number>;
  };
  
  insights: OrgInsight[];
  
  lastUpdated: string;
}