import { z } from 'zod';
import { UserActionSource, UserActionType } from '../../../entities/user-action.entity';

// Schema for tracking search actions
export const TrackSearchSchema = z.object({
  userId: z.string().min(1),
  searchQuery: z.string(),
  filters: z.record(z.unknown()).optional(),
});

// DTO class for validating search tracking requests
export class TrackSearchDto {
  userId!: string;
  searchQuery!: string;
  filters?: Record<string, unknown>;
}

// Schema for tracking job views
export const TrackJobViewSchema = z.object({
  userId: z.string().min(1),
  jobExtId: z.string().min(1),
  source: z.nativeEnum(UserActionSource),
  correlationId: z.string().optional(),
  viewDurationMs: z.number().positive().optional(),
});

// DTO class for validating job view tracking
export class TrackJobViewDto {
  userId!: string;
  jobExtId!: string;
  source!: UserActionSource;
  correlationId?: string;
  viewDurationMs?: number;
}

// Schema for tracking job saves/unsaves
export const TrackJobSaveSchema = z.object({
  userId: z.string().min(1),
  jobExtId: z.string().min(1),
  source: z.nativeEnum(UserActionSource),
  correlationId: z.string().optional(),
});

// DTO class for validating job save tracking
export class TrackJobSaveDto {
  userId!: string;
  jobExtId!: string;
  source!: UserActionSource;
  correlationId?: string;
}

// Schema for tracking job applications
export const TrackJobApplySchema = z.object({
  userId: z.string().min(1),
  jobExtId: z.string().min(1),
  source: z.nativeEnum(UserActionSource),
  correlationId: z.string().optional(),
  applicationData: z.record(z.unknown()).optional(),
});

// DTO class for validating job application tracking
export class TrackJobApplyDto {
  userId!: string;
  jobExtId!: string;
  source!: UserActionSource;
  correlationId?: string;
  applicationData?: Record<string, unknown>;
}

// Schema for tracking recommendations shown
export const TrackRecommendationsShownSchema = z.object({
  userId: z.string().min(1),
  jobExtIds: z.array(z.string().min(1)),
  source: z.nativeEnum(UserActionSource).optional(),
});

// DTO class for validating recommendations shown tracking
export class TrackRecommendationsShownDto {
  userId!: string;
  jobExtIds!: string[];
  source?: UserActionSource;
}

// Schema for tracking recommendation clicks
export const TrackRecommendationClickedSchema = z.object({
  userId: z.string().min(1),
  jobExtId: z.string().min(1),
  correlationId: z.string(),
  position: z.number().int().nonnegative(),
});

// DTO class for validating recommendation click tracking
export class TrackRecommendationClickedDto {
  userId!: string;
  jobExtId!: string;
  correlationId!: string;
  position!: number;
}
