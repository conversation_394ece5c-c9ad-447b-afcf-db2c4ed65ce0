import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { CacheModule } from '../../cache/cache.module';
import { PubSubModule } from '../shared/pubsub/pubsub.module';

// Entities
import { UserAction } from '../../entities/user-action.entity';
import { UserActionSummary } from '../../entities/user-action-summary.entity';
import { OrganizationActionSummary } from '../../entities/organization-action-summary.entity';

// Services
import { UserActionTrackerService } from './services/user-action-tracker.service';
import { UserMetricsService } from './services/user-metrics.service';
import { OrgMetricsService } from './services/org-metrics.service';
import { MetricsSchedulerService } from './services/metrics-scheduler.service';
import { DataRetentionService } from './services/data-retention.service';

// Repositories
import { UserActionSummaryRepository } from './repositories/user-action-summary.repository';
import { OrganizationActionSummaryRepository } from './repositories/organization-action-summary.repository';

// Controllers
import { UserActionController } from './controllers/user-action.controller';
import { UserMetricsController } from './controllers/user-metrics.controller';
import { OrgMetricsController } from './controllers/org-metrics.controller';

// PubSub Handlers
import { UserMetricsHandler } from './pubsub/user-metrics.handler';
import { OrgMetricsHandler } from './pubsub/org-metrics.handler';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserAction,
      UserActionSummary,
      OrganizationActionSummary,
    ]),
    ScheduleModule.forRoot(),
    CacheModule,
    PubSubModule,
  ],
  controllers: [
    UserActionController,
    UserMetricsController,
    OrgMetricsController,
  ],
  providers: [
    // Services
    UserActionTrackerService,
    UserMetricsService,
    OrgMetricsService,
    MetricsSchedulerService,
    DataRetentionService,
    
    // Repositories
    UserActionSummaryRepository,
    OrganizationActionSummaryRepository,
    
    // PubSub Handlers
    UserMetricsHandler,
    OrgMetricsHandler,
  ],
  exports: [
    UserActionTrackerService,
    UserMetricsService,
    OrgMetricsService,
  ],
})
export class UserActionsModule {}