import { JobSearchResult } from '../services/interfaces/job-search.interface';
import { SimplifiedJobDataDto } from './simplified-job-data.dto';

/**
 * DTO class for JobSearchResult with translatable fields
 * Jobs array contains SimplifiedJobDataDto objects with translatable fields
 * This is a lighter version that omits market_salary, skill_contexts, evolution, and summary
 * to avoid unnecessary translations when fetching lists of jobs.
 */
export class SimplifiedJobSearchResultDto {
  jobs: SimplifiedJobDataDto[];
  
  meta: {
    total: number;
    page: number;
    limit: number;
    has_next: boolean;
  };
  
  /**
   * Transforms a plain JobSearchResult object into a SimplifiedJobSearchResultDto
   */
  static from(result: JobSearchResult): SimplifiedJobSearchResultDto {
    const dto = new SimplifiedJobSearchResultDto();
    
    // Map the meta information
    dto.meta = { ...result.meta };
    
    // Transform each job to a SimplifiedJobDataDto (with safety check)
    dto.jobs = Array.isArray(result.jobs) 
      ? result.jobs.map(job => SimplifiedJobDataDto.from(job)) 
      : [];
      
    return dto;
  }
}
