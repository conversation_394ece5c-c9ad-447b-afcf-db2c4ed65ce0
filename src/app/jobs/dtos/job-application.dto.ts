import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  <PERSON>Array,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUUID,
  ValidateNested,
} from 'class-validator';
import {
  CvSectionDto,
  KeyFitPointDto,
} from '../../cv-workspace/dto/cv-generation.dto';

export class JobApplicationDto {
  @ApiProperty({
    description: 'User ID from Supertoken',
    example: '9f8d7c6b-5432-1a0b-9c8d-7654321fedcb',
  })
  @IsUUID()
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'HTML content of the generated CV',
    example: '<div>CV content</div>',
  })
  @IsString()
  @IsNotEmpty()
  cvHtml: string;

  @ApiProperty({
    description: 'URL to the generated CV PDF',
    example: 'https://storage.googleapis.com/cvs/generated-cv-123.pdf',
    required: false,
  })
  @IsString()
  @IsOptional()
  cvUrl?: string;

  @ApiProperty({
    description: 'External ID of the job being applied to',
    example: '013c9f4a-54ea-4b65-9e97-1fbf1c4a4606',
  })
  @IsString()
  @IsNotEmpty()
  jobExtId: string;

  @ApiProperty({
    description: 'Selected language for the CV',
    example: 'en',
  })
  @IsString()
  @IsNotEmpty()
  selectedLanguage: string;

  @ApiProperty({
    description: 'Normalized language name (Finnish, Swedish, English)',
    example: 'English',
    required: false,
  })
  @IsString()
  @IsOptional()
  language?: string;

  @ApiProperty({
    description: 'Type of document generated',
    example: 'CV_ONLY',
  })
  @IsString()
  @IsNotEmpty()
  documentType: string;

  @ApiProperty({
    description: 'Selected CV template',
    example: 'Modern Minimal',
  })
  @IsString()
  @IsNotEmpty()
  selectedTemplate: string;

  @ApiProperty({
    description: 'Sections included in the CV',
    type: [CvSectionDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CvSectionDto)
  sections: CvSectionDto[];

  @ApiProperty({
    description: 'AI-generated About Me section content',
    example:
      'I am a passionate software developer with over 5 years of experience...',
    required: false,
  })
  @IsString()
  @IsOptional()
  generatedAboutMe?: string;

  @ApiProperty({
    description: 'Key points highlighting fit with job position',
    type: [KeyFitPointDto],
    required: false,
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => KeyFitPointDto)
  @IsOptional()
  keyFitPoints?: KeyFitPointDto[];
}

export class JobApplicationResponseDto {
  @ApiProperty({
    description: 'Job application ID',
    example: '9f8d7c6b-5432-1a0b-9c8d-7654321fedcb',
  })
  id: string;

  @ApiProperty({
    description: 'User ID from Supertoken',
    example: '9f8d7c6b-5432-1a0b-9c8d-7654321fedcb',
  })
  userId: string;

  @ApiProperty({
    description: 'URL to the generated CV PDF',
    example: 'https://storage.googleapis.com/cvs/generated-cv-123.pdf',
  })
  cvUrl: string;

  @ApiProperty({
    description: 'External ID of the job being applied to',
    example: '013c9f4a-54ea-4b65-9e97-1fbf1c4a4606',
  })
  jobExtId: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2025-03-25T10:13:29.000Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2025-03-25T10:13:29.000Z',
  })
  updatedAt: Date;
}
