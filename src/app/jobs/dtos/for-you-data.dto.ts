import { Translatable } from '../../translation/decorators/translatable.decorator';
import { ForYou } from '../../../entities/for-you.entity';

/**
 * DTO class for ForYou with translatable fields
 */
export class ForYouDataDto implements Partial<ForYou> {
  userId: string;
  recommended_at: Date;
  is_viewed: boolean;
  is_liked?: boolean;
  score?: number;
  
  // Add Translatable decorator to classification_data and why fields
  @Translatable()
  classification_data?: Record<string, any>;
  
  @Translatable()
  why?: string[];
  
  /**
   * Transforms a plain ForYou entity into a ForYouDataDto
   */
  static from(forYou: ForYou): ForYouDataDto {
    const dto = new ForYouDataDto();
    
    // Copy all properties
    Object.assign(dto, {
      userId: forYou.userId,
      recommended_at: forYou.recommended_at,
      is_viewed: forYou.is_viewed,
      is_liked: forYou.is_liked,
      classification_data: forYou.classification_data,
      why: forYou.why,
      score: forYou.score,
    });
    
    return dto;
  }
}
