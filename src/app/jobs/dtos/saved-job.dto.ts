import { ApiProperty } from '@nestjs/swagger';

export class SavedJobResponseDto {
  @ApiProperty({ description: 'Saved job DB ID', example: 1 })
  id: number;

  @ApiProperty({ description: 'User ID', example: '9f8d7c6b-5432-1a0b-9c8d-7654321fedcb' })
  userId: string;

  @ApiProperty({ description: 'External job ID', example: '013c9f4a-54ea-4b65-9e97-1fbf1c4a4606' })
  jobExtId: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}
