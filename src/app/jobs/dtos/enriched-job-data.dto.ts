import { Translatable } from '../../translation/decorators/translatable.decorator';
import { EnrichedJobData } from '../services/interfaces/job-search.interface';

/**
 * DTO for the market salary section of job data
 */
export class MarketSalaryDto {
  @Translatable()
  job_role: string;

  median_salary: number;
  salary_lower_bound: number;
  salary_upper_bound: number;
  currency: string;
  salary_period: string;
  data_source: string;
  @Translatable()
  data_reliability: string;
}

/**
 * DTO for the summary section of job data
 */
export class JobSummaryDto {
  @Translatable()
  elevator_pitch: string;

  short_description: string;

  @Translatable()
  primary_tasks: string | string[];
}

/**
 * DTO for the evolution section of job data
 */
export class JobEvolutionDto {
  @Translatable()
  evolution_summary: string;
  
  @Translatable()
  automation_impact: string;
  
  @Translatable()
  relevance_prediction: string;
}

/**
 * DTO class for EnrichedJobData with translatable fields
 * Fields marked with @Translatable() will be automatically translated
 * based on the Accept-Language header in the request
 */
export class EnrichedJobDataDto implements EnrichedJobData {
  id: string;

  @Translatable()
  title: string;
  
  rankingScore?: number;
  
  employer: {
    id: number;
    name: string;
  };
  
  location: {
    city: string;
    region: string;
    country: string;
    isRemote: boolean;
  };
  
  employment: {
    working_time: string;
    working_hours: string;
    continuity: string;
  };
  
  timing: {
    published_date: string;
    expires_at: string;
    is_new: boolean;
    closing_soon: boolean;
  };
  
  salary: {
    lower_bound: number;
    upper_bound: number;
    has_salary: boolean;
  };
  
  application: string | null;
  
  summary: JobSummaryDto;
  
  skills: string[];
  @Translatable()
  skill_contexts: string[];
  languages: string[];
  
  @Translatable()
  industry: string;

  @Translatable()
  occupation: string;

  seniority: string;
  source: string;
  
  market_salary?: MarketSalaryDto;
  
  evolution?: JobEvolutionDto;
  
  ranking_score?: number;
  is_viewed?: boolean;
  is_liked?: boolean;
  hasApplied?: boolean;
  classification_data?: Record<string, any>;
  
  /**
   * Transforms a plain EnrichedJobData object into an EnrichedJobDataDto
   * This is needed to ensure the class prototype is available for reflection
   */
  static from(jobData: EnrichedJobData): EnrichedJobDataDto {
    const dto = new EnrichedJobDataDto();
    
    // Copy all basic properties
    Object.assign(dto, {
      ...jobData,
      summary: undefined, // We'll set these separately
      evolution: undefined,
      market_salary: undefined,
    });
    
    // Handle nested objects that need their own class instances
    if (jobData.summary) {
      dto.summary = new JobSummaryDto();
      Object.assign(dto.summary, jobData.summary);
    }
    
    if (jobData.evolution) {
      dto.evolution = new JobEvolutionDto();
      Object.assign(dto.evolution, jobData.evolution);
    }

    if (jobData.market_salary) {
      dto.market_salary = new MarketSalaryDto();
      Object.assign(dto.market_salary, jobData.market_salary);
    }

    return dto;
  }
}
