import { JobSearchResult } from '../services/interfaces/job-search.interface';
import { EnrichedJobDataDto } from './enriched-job-data.dto';

/**
 * DTO class for JobSearchResult with translatable fields
 * Jobs array contains EnrichedJobDataDto objects with translatable fields
 * This is used by the TranslationInterceptor to automatically translate marked fields
 * based on the Accept-Language header in the request
 */
export class JobSearchResultDto implements JobSearchResult {
  jobs: EnrichedJobDataDto[];
  
  meta: {
    total: number;
    page: number;
    limit: number;
    has_next: boolean;
  };
  
  /**
   * Transforms a plain JobSearchResult object into a JobSearchResultDto
   * This ensures the class prototype is available for reflection metadata
   * required by the @Translatable() decorator
   */
  static from(result: JobSearchResult): JobSearchResultDto {
    const dto = new JobSearchResultDto();
    
    // Map the meta information
    dto.meta = { ...result.meta };

    // Transform each job to an EnrichedJobDataDto
    dto.jobs = result.jobs.map(job => EnrichedJobDataDto.from(job));
    
    return dto;
  }
}
