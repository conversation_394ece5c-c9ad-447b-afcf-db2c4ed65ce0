import { Translatable } from '../../translation/decorators/translatable.decorator';
import { EnrichedJobData } from '../services/interfaces/job-search.interface';

/**
 * DTO class for EnrichedJobData with translatable fields,
 * but excluding market_salary, skill_contexts, evolution, and summary
 * to avoid unnecessary translations when fetching lists of jobs.
 */
export class SimplifiedJobDataDto implements Partial<EnrichedJobData> {
  id: string;
  title: string;
  rankingScore?: number;
  
  employer: {
    id: number;
    name: string;
  };
  
  location: {
    city: string;
    region: string;
    country: string;
    isRemote: boolean;
  };
  
  employment: {
    working_time: string;
    working_hours: string;
    continuity: string;
  };
  
  timing: {
    published_date: string;
    expires_at: string;
    is_new: boolean;
    closing_soon: boolean;
  };
  
  salary: {
    lower_bound: number;
    upper_bound: number;
    has_salary: boolean;
  };
  
  application: string | null;
  
  skills: string[];
  languages: string[];
  
  @Translatable()
  industry: string;
  
  occupation: string;

  seniority: string;
  source: string;
  
  ranking_score?: number;
  is_viewed?: boolean;
  is_liked?: boolean;
  classification_data?: Record<string, any>;
  
  /**
   * Transforms a plain EnrichedJobData object into a SimplifiedJobDataDto
   * excluding certain fields to avoid unnecessary translations
   */
  static from(jobData: EnrichedJobData): SimplifiedJobDataDto {
    const dto = new SimplifiedJobDataDto();
    
    // Copy all basic properties except for the excluded ones
    Object.assign(dto, {
      ...jobData,
      // Explicitly exclude these fields
      market_salary: undefined,
      evolution: undefined,
      summary: undefined,
      skill_contexts: undefined
    });
    
    return dto;
  }
}
