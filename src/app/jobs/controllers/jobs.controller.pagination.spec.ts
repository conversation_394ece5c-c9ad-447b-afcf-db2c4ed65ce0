import { Test, TestingModule } from '@nestjs/testing';
import { JobsController } from './jobs.controller';
import { PubSubService } from '../../shared/pubsub/pubsub.service';
import { JobsImportService } from '../services/jobs-import.service';
import { JobsProcessingService } from '../services/jobs-processing.service';
import { JobsExportService } from '../services/jobs-export.service';
import { JobsPineconeService } from '../services/jobs-pinecone.service';
import { JobsImportRepository } from '../repository/jobs-import.repository';
import { ReactAppService } from '../../react-app/services/react-app.service';
import { JobsSearchService } from '../services/jobs-search.service';
import { UserApiService } from '../../../app/user/services/user-api.service';
import { AuthService } from '../../../auth/services/auth.service';
import { OpenAIService } from '../../../openai/openai.service';
import { ForYouRepository } from '../repository/for-you.repository';
import { JobApplicationService } from '../services/job-application.service';
import { UserActionTrackerService } from '../../user-actions/services/user-action-tracker.service';
import { PaginatedSearchDto } from './dto/paginated-search.dto';
import { SimplifiedJobSearchResultDto } from '../dtos/simplified-job-search-result.dto';
import { Request } from 'express';

// Mock implementations
const mockPubSubService = {
  publishMessage: jest.fn(),
  getJobImportTopic: jest.fn(),
};
const mockImportService = { import: jest.fn() };
const mockProcessingService = { processJobs: jest.fn() };
const mockExportService = { exportUnprocessedJobsToCSV: jest.fn() };
const mockPineconeService = {
  uploadAllProcessedJobs: jest.fn(),
  updateAllJobsWithKeywords: jest.fn(),
  updateExpiresAtToUnixTimestamp: jest.fn(),
};
const mockJobsRepository = {
  findJobsWithDetails: jest.fn(),
  findByExtId: jest.fn(),
  markJobAsProcessed: jest.fn(),
  upsertProcessedJobs: jest.fn(),
  updateExpiredJobs: jest.fn(),
  countExpiredJobs: jest.fn(),
};
const mockReactAppService = {
  classifySingleJob: jest.fn(),
};
const mockSearchService = {
  searchJobs: jest.fn(),
  getTopJobs: jest.fn(),
  getUserApplications: jest.fn(),
  getForYouJobs: jest.fn(),
  getJobsByIds: jest.fn(),
  prepareForClassifier: jest.fn(),
  forYou: jest.fn(),
};
const mockUserApiService = {
  getUserKeywordsData: jest.fn(),
};
const mockAuthService = {};
const mockOpenAIService = {
  createQueryEmbedding: jest.fn(),
};
const mockForYouRepository = {
  getForYouJobs: jest.fn(),
  markJobAsViewed: jest.fn(),
  updateJobRating: jest.fn(),
  storeClassificationData: jest.fn(),
  saveForYouJobs: jest.fn(),
};
const mockJobApplicationService = {
  hasUserAppliedToJob: jest.fn(),
};
const mockUserActionTrackerService = {
  trackSearch: jest.fn(),
  trackJobView: jest.fn(),
};

describe('JobsController - Pagination', () => {
  let controller: JobsController;
  let searchService: JobsSearchService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [JobsController],
      providers: [
        { provide: PubSubService, useValue: mockPubSubService },
        { provide: JobsImportService, useValue: mockImportService },
        { provide: JobsProcessingService, useValue: mockProcessingService },
        { provide: JobsExportService, useValue: mockExportService },
        { provide: JobsPineconeService, useValue: mockPineconeService },
        { provide: JobsImportRepository, useValue: mockJobsRepository },
        { provide: ReactAppService, useValue: mockReactAppService },
        { provide: JobsSearchService, useValue: mockSearchService },
        { provide: UserApiService, useValue: mockUserApiService },
        { provide: AuthService, useValue: mockAuthService },
        { provide: OpenAIService, useValue: mockOpenAIService },
        { provide: ForYouRepository, useValue: mockForYouRepository },
        { provide: JobApplicationService, useValue: mockJobApplicationService },
        {
          provide: UserActionTrackerService,
          useValue: mockUserActionTrackerService,
        },
      ],
    }).compile();

    controller = module.get<JobsController>(JobsController);
    searchService = module.get<JobsSearchService>(JobsSearchService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('searchJobs', () => {
    it('should pass the useCache parameter to search service', async () => {
      // Arrange
      const searchParams: PaginatedSearchDto = {
        query: 'developer',
        page: 2,
        limit: 10,
        useCache: true,
      };

      const mockRequest = {
        headers: {},
        session: {
          getUserId: jest.fn().mockReturnValue('test-user-id'),
        },
      } as unknown as Request;

      const mockSearchResult = {
        jobs: [{ id: '1', title: 'Developer Job' }],
        meta: {
          total: 50,
          page: 2,
          limit: 10,
          has_next: true,
        },
      };

      mockSearchService.searchJobs.mockResolvedValue(mockSearchResult);

      // Act
      const result = await controller.searchJobs(mockRequest, searchParams);

      // Assert
      expect(searchService.searchJobs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'developer',
          page: 2,
          limit: 10,
          useCache: true,
          user_id: 'test-user-id',
        }),
        undefined,
      );

      expect(result).toBeInstanceOf(SimplifiedJobSearchResultDto);
      expect(result.meta.page).toBe(2);
      expect(result.meta.has_next).toBe(true);
    });

    it('should handle anonymous users correctly', async () => {
      // Arrange
      const searchParams: PaginatedSearchDto = {
        query: 'developer',
        page: 1,
        limit: 10,
      };

      const mockRequest = {
        headers: {},
      } as unknown as Request;

      const mockSearchResult = {
        jobs: [{ id: '1', title: 'Developer Job' }],
        meta: {
          total: 50,
          page: 1,
          limit: 10,
          has_next: true,
        },
      };

      mockSearchService.searchJobs.mockResolvedValue(mockSearchResult);

      // Act
      const result = await controller.searchJobs(mockRequest, searchParams);

      // Assert
      expect(searchService.searchJobs).toHaveBeenCalledWith(
        expect.objectContaining({
          query: 'developer',
          page: 1,
          limit: 10,
          useCache: true,
          user_id: 'anonymous',
        }),
        undefined,
      );

      expect(result.meta.total).toBe(50);
    });

    it('should handle search errors gracefully', async () => {
      // Arrange
      const searchParams: PaginatedSearchDto = {
        query: 'developer',
        page: 1,
        limit: 10,
      };

      const mockRequest = {
        headers: {},
      } as unknown as Request;

      mockSearchService.searchJobs.mockRejectedValue(
        new Error('Search failed'),
      );

      // Act
      const result = await controller.searchJobs(mockRequest, searchParams);

      // Assert
      expect(result).toEqual({
        meta: { has_next: false, limit: 0, page: 0, total: 0 },
        jobs: [],
      });
    });
  });
});