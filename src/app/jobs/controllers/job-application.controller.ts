import {
  Body,
  Controller,
  Get,
  Logger,
  Param,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { JobApplicationService } from '../services/job-application.service';
import {
  JobApplicationDto,
  JobApplicationResponseDto,
} from '../dtos/job-application.dto';
import { AuthGuard } from '../../../auth/guards/auth.guard';

@ApiTags('job-applications')
@Controller('v1/job-applications')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class JobApplicationController {
  private readonly logger = new Logger(JobApplicationController.name);

  constructor(private readonly jobApplicationService: JobApplicationService) {}

  @Post()
  @ApiOperation({
    summary: 'Create a new job application',
    description: 'Creates a new job application record with CV data',
  })
  @ApiResponse({
    status: 201,
    description: 'The job application has been successfully created',
    type: JobApplicationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Job not found' })
  async createJobApplication(
    @Body() applicationData: JobApplicationDto,
  ): Promise<JobApplicationResponseDto> {
    this.logger.log('Creating new job application');
    return this.jobApplicationService.createJobApplication(applicationData);
  }

  @Get()
  @ApiOperation({
    summary: 'Get all job applications for a user',
    description: 'Returns all job applications for the specified user ID',
  })
  @ApiResponse({
    status: 200,
    description: 'List of job applications',
    type: [JobApplicationResponseDto],
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getJobApplicationsByUserId(
    @Query('userId') userId: string,
  ): Promise<JobApplicationResponseDto[]> {
    this.logger.log(`Fetching job applications for user ${userId}`);
    return this.jobApplicationService.getJobApplicationsByUserId(userId);
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get a job application by ID',
    description: 'Returns a specific job application by its ID',
  })
  @ApiResponse({
    status: 200,
    description: 'The job application',
    type: JobApplicationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad Request' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 404, description: 'Job application not found' })
  async getJobApplicationById(
    @Param('id') id: string,
  ): Promise<JobApplicationResponseDto> {
    this.logger.log(`Fetching job application with ID ${id}`);
    return this.jobApplicationService.getJobApplicationById(id);
  }
}
