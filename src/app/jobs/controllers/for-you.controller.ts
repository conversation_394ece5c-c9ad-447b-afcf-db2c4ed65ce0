import {
  BadRequestException,
  Body,
  Controller,
  Logger,
  NotFoundException,
  Post,
  Request,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { ApiBody, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JobsSearchService } from '../services/jobs-search.service';
import { ForYouRepository } from '../repository/for-you.repository';
import { UserIdDto } from '../controllers/dto/user-id.dto';
import { ForYouStatusDto } from './dto/for-you-status.dto';
import { JobClassificationDto, JobRatingDto, JobViewDto } from '../../react-app/dto/job-interaction.dto';
import { RecommendationEngineService } from '../services/recommendation-engine.service';
import { UserProcessTrackingRepository } from '../../core/repository/user-process-tracking.repository';
import { ProcessEntityType, ProcessStatus } from '../../../entities/user-process-tracking.entity';
import { UserApiService } from 'src/app/user/services/user-api.service';
import { AuthGuard } from 'src/auth/guards/auth.guard';
import { SessionUser } from 'src/auth/decorators/session.decorator';
import { SessionContainer } from 'supertokens-node/recipe/session';
import { SimplifiedJobDataDto } from '../dtos/simplified-job-data.dto';
import { ForYouDataDto } from '../dtos/for-you-data.dto';
import { TranslateResponse } from '../../translation/decorators/translate-response.decorator';
import { UserActionTrackerService } from '../../user-actions/services/user-action-tracker.service';
import { UserActionSource } from '../../../entities/user-action.entity';

/**
 * ForYou API Controller
 *
 * Handles operations related to personalized job recommendations (ForYou feature)
 */
@ApiTags('ForYou')
@Controller('/for-you')
export class ForYouController {
  private readonly logger = new Logger(ForYouController.name);

  constructor(
    private readonly forYouRepository: ForYouRepository,
    private readonly searchService: JobsSearchService,
    private readonly recommendationEngine: RecommendationEngineService,
    private readonly processTrackingRepository: UserProcessTrackingRepository,
    private readonly userServiceClient: UserApiService,
    private readonly userActionTracker: UserActionTrackerService
  ) {}

  /**
   * Get access token from session
   * @param session The session container
   * @returns Promise resolving to access token
   */
  private async getSessionAccessToken(session: SessionContainer): Promise<string> {
    return session.getAccessToken();
  }

  /**
   * Generate "For You" job recommendations for a user
   */
  @Post('/generate')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Generate "For You" job recommendations',
    description: 'Initiates the generation of personalized job recommendations for a specific user and returns immediately',
  })
  @ApiBody({
    description: 'User ID for which to generate recommendations',
    type: UserIdDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Recommendation generation process started successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Missing user ID or invalid request',
  })
  async generateForYouRecommendations(
    @Request() req,
    @SessionUser() session: SessionContainer,
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
      }),
    )
    requestDto: UserIdDto,
  ) {
    // Use authenticated user's ID from session if available, fallback to request DTO
    const userId = session.getUserId() || requestDto.userId;
    
    if (!userId) {
      throw new BadRequestException('userId is required');
    }

    this.logger.log(`Initiating "For You" recommendations generation for user: ${userId}`);
    
    try {
      let smartProfile = requestDto.smartProfile;
      if (!smartProfile && userId) {
        try {
          // Get the access token from the session
          const accessToken = await this.getSessionAccessToken(session);
          
          if (!accessToken) {
            throw new BadRequestException('Access token is required for fetching smart profile');
          }
          
          const userProfile = await this.userServiceClient.getUserSmartProfile(accessToken);
          smartProfile = userProfile.data;
        } catch (error) {
          this.logger.error(`Failed to fetch user profile for userId ${userId}:`, error);
        }
      }
      if (!smartProfile) {
        throw new BadRequestException('Smart profile is required but was not provided and could not be fetched');
      }

      // Check if there's already an active process for this user
      const hasActiveProcess = await this.processTrackingRepository.hasActiveProcess(
        userId,
        ProcessEntityType.FOR_YOU
      );
      
      if (hasActiveProcess) {
        return {
          success: true,
          data: {
            inProgress: true,
          },
          message: 'ForYou recommendations generation is already in progress',
          timestamp: new Date().toISOString(),
        };
      }
      
      // Create a process tracking record
      const process = await this.processTrackingRepository.createProcess(
        userId,
        ProcessEntityType.FOR_YOU,
        { 
          requestTime: new Date().toISOString(),
          hasSmartProfile: !!requestDto.smartProfile 
        }
      );
      
      // Start background processing
      setTimeout(async () => {
        try {
          // Update process status to in progress
          await this.processTrackingRepository.updateStatus(
            process.id,
            ProcessStatus.IN_PROGRESS
          );
          
          // Parse the smartProfile if it's a string
          const parsedSmartProfile = 
            typeof smartProfile === 'string' 
              ? JSON.parse(smartProfile) 
              : smartProfile;
          
          // Use our new recommendation engine to generate ForYou entries
          const forYouEntries = await this.recommendationEngine.generateForYou(
            userId,
            parsedSmartProfile
          );
          
          // Mark the process as complete
          await this.processTrackingRepository.completeProcess(process.id);
          
        } catch (error) {
          this.logger.error(
            `Error in background generation of "For You" recommendations: ${error.message}`, 
            error.stack
          );
          
          // Mark the process as failed
          await this.processTrackingRepository.failProcess(
            process.id,
            error.message
          );
        }
      }, 0);
      
      // Return immediately to the client
      return {
        success: true,
        data: {
          inProgress: true,
          processId: process.id,
        },
        message: 'ForYou recommendations generation started successfully',
        timestamp: new Date().toISOString(),
      };
      
    } catch (error) {
      this.logger.error(`Error initiating "For You" recommendations: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to initiate recommendations: ${error.message}`);
    }
  }

  /**
   * Get "For You" job recommendations for a user
   */
  @Post('/recommendations')
  @UseGuards(AuthGuard)
  @TranslateResponse()
  @ApiOperation({
    summary: 'Get "For You" job recommendations',
    description: 'Retrieves persisted personalized job recommendations for a specific user',
  })
  @ApiBody({
    description: 'User ID for which to retrieve recommendations',
    type: UserIdDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Recommendations retrieved successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Missing user ID or invalid request',
  })
  async getForYouRecommendations(
    @Request() req,
    @SessionUser() session: SessionContainer,
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
      }),
    )
    requestDto: UserIdDto,
  ) {
    // Use authenticated user's ID from session if available, fallback to request DTO
    const userId = session.getUserId() || requestDto.userId;
    
    if (!userId) {
      throw new BadRequestException('userId is required');
    }

    this.logger.log(`Getting "For You" recommendations for user: ${userId}`);
    
    try {
      // Get recommendations for the user
      let recommendations = await this.forYouRepository.getForYouJobs(userId);
      
      // If no recommendations, check if there's an active generation process
      if (recommendations.length === 0) {
        const activeProcess = await this.processTrackingRepository.findByUserAndType(
          userId,
          ProcessEntityType.FOR_YOU
        );
        
        if (activeProcess && 
           (activeProcess.status === ProcessStatus.PENDING || 
            activeProcess.status === ProcessStatus.IN_PROGRESS)) {
          return {
            success: true,
            data: {
              jobs: [],
              meta: {
                total: 0,
                count: 0,
                inProgress: true,
              }
            },
            message: 'ForYou recommendations generation is in progress',
            timestamp: new Date().toISOString(),
          };
        } else {
          // No recommendations for today and no active process, try to get yesterday's recommendations
          this.logger.log(`No recommendations for today for user ${userId}, checking yesterday's recommendations`);
          recommendations = await this.forYouRepository.getYesterdaysRecommendations(userId);
        }
      }
      
      // Apply pagination if limit is provided
      const limit = requestDto.limit || recommendations.length;
      const paginatedRecommendations = recommendations.slice(0, limit);
      
      // Track recommendations shown to the user for analytics
      try {
        // Get job IDs for tracking
        const jobIds = paginatedRecommendations.map(rec => rec.job.id);
        
        // Only track if we have recommendations and a userId
        if (jobIds.length > 0) {
          await this.userActionTracker.trackRecommendationsShown(
            userId,
            jobIds,
            UserActionSource.FOR_YOU
          );
        }
      } catch (trackingError) {
        // Just log tracking errors, don't disrupt the user experience
        this.logger.warn(`Error tracking recommendations shown: ${trackingError.message}`);
      }
      
      let message = `Successfully retrieved ${paginatedRecommendations.length} "For You" recommendations`;
      if (recommendations.length > 0 && await this.forYouRepository.hasGeneratedForYouToday(userId) === false) {
        message = `Successfully retrieved ${paginatedRecommendations.length} "For You" recommendations from yesterday`;
      }
      
      return {
        success: true,
        data: {
          jobs: paginatedRecommendations.map(r => {
            // Create explicitly typed DTO objects
            const forYouDto = ForYouDataDto.from(r.forYou);
            // Force @Translatable metadata into the object directly
            Object.defineProperty(forYouDto, '__translatable_fields', {
              value: ['classification_data', 'why'],
              enumerable: false
            });
            
            return {
              job: SimplifiedJobDataDto.from(r.job),
              forYou: forYouDto
            };
          }),
          meta: {
            total: recommendations.length,
            count: paginatedRecommendations.length,
            fromYesterday: recommendations.length > 0 && await this.forYouRepository.hasGeneratedForYouToday(userId) === false
          }
        },
        message: message,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Error getting "For You" recommendations: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get recommendations: ${error.message}`);
    }
  }

  /**
   * Get the current status of "For You" recommendations for a user
   */
  @Post('/status')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Get status of "For You" recommendations',
    description: 'Checks if a user has recommendations and whether generation is in progress',
  })
  @ApiBody({
    description: 'User ID to check status for',
    type: UserIdDto,
  })
  @ApiResponse({
    status: 200,
    description: 'Status retrieved successfully',
    type: ForYouStatusDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Missing user ID or invalid request',
  })
  async getForYouStatus(
    @Request() req,
    @SessionUser() session: SessionContainer,
    @Body(
      new ValidationPipe({
        forbidNonWhitelisted: false,
        whitelist: false,
      }),
    )
    requestDto: UserIdDto,
  ): Promise<ForYouStatusDto> {
    // Use authenticated user's ID from session if available, fallback to request DTO
    const userId = session.getUserId() || requestDto.userId;
    
    if (!userId) {
      throw new BadRequestException('userId is required');
    }

    this.logger.log(`Getting "For You" status for user: ${userId}`);
    
    try {
      // Check if user has recommendations
      const hasRecommendations = await this.forYouRepository.hasForYouJobs(userId);
      
      // Check if there is an active process
      const activeProcess = await this.processTrackingRepository.findByUserAndType(
        userId,
        ProcessEntityType.FOR_YOU
      );
      
      const inProgress = activeProcess && 
        (activeProcess.status === ProcessStatus.PENDING || 
         activeProcess.status === ProcessStatus.IN_PROGRESS);
      
      // Check if recommendations need to be refreshed (based on last Pinecone upload)
      const latestUploadTime = await this.forYouRepository.getLatestPineconeUploadTimestamp();
      
      // Check for recommendations generated today
      const generatedToday = await this.forYouRepository.hasGeneratedForYouToday(userId);
      
      // Determine if refresh is needed:
      // 1. If user has recommendations but they weren't generated today
      // 2. If there's a newer upload to Pinecone compared to when recommendations were generated
      let needsRefresh = false;
      
      if (hasRecommendations && !generatedToday) {
        needsRefresh = true;
      } else if (latestUploadTime && activeProcess && activeProcess.completedAt) {
        needsRefresh = latestUploadTime > activeProcess.completedAt;
      }
      
      return {
        // Original fields required by frontend
        latestJobUploadTimestamp: latestUploadTime,
        // If recommendations are being generated, treat it as if they've been generated today
        hasGeneratedForYouToday: generatedToday || !!inProgress,
        
        // Standard response fields
        success: true,
        data: {
          hasRecommendations,
          inProgress: !!inProgress,
          needsRefresh,
          processStatus: activeProcess ? activeProcess.status : null,
          lastUpdated: activeProcess ? activeProcess.completedAt : null,
        },
        message: 'ForYou status retrieved successfully',
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      this.logger.error(`Error getting "For You" status: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to get status: ${error.message}`);
    }
  }

  /**
   * Mark a job as viewed by a user
   */
  @Post('/view')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Mark job as viewed',
    description: 'Records that a user has viewed a specific job',
  })
  @ApiBody({
    description: 'Job view data',
    type: JobViewDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Job successfully marked as viewed',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Job successfully marked as viewed' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Missing required fields in request body' })
  @ApiResponse({ status: 404, description: 'Job recommendation not found' })
  async markJobAsViewed(
    @Request() req,
    @SessionUser() session: SessionContainer,
    @Body(new ValidationPipe()) jobViewDto: JobViewDto,
  ) {
    try {
      // Use authenticated user's ID from session if available, fallback to request DTO
      const userId = session.getUserId() || jobViewDto.userId;
      
      if (!userId) {
        throw new BadRequestException('userId is required');
      }
      
      const { extId } = jobViewDto;
      
      await this.forYouRepository.markJobAsViewed(userId, extId);
      
      // Track the view in the user action system
      try {
        await this.userActionTracker.trackJobView(
          userId, 
          extId,
          UserActionSource.FOR_YOU
        );
      } catch (trackingError) {
        // Just log tracking errors, don't disrupt the user experience
        this.logger.warn(`Error tracking job view: ${trackingError.message}`);
      }
      
      return {
        success: true,
        message: 'Job successfully marked as viewed',
      };
    } catch (error) {
      this.logger.error(`Error marking job as viewed: ${error.message}`, error.stack);
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      throw new BadRequestException(`Failed to mark job as viewed: ${error.message}`);
    }
  }

  /**
   * Updates a job rating (like/dislike) for a user
   */
  @Post('/rate')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Rate a job',
    description: 'Records a user\'s rating (like/dislike) for a specific job',
  })
  @ApiBody({
    description: 'Job rating data',
    type: JobRatingDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Job rating successfully updated',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Job rating successfully updated' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Missing required fields in request body' })
  @ApiResponse({ status: 404, description: 'Job recommendation not found' })
  async rateJob(
    @Request() req,
    @SessionUser() session: SessionContainer,
    @Body(new ValidationPipe()) jobRatingDto: JobRatingDto,
  ) {
    try {
      // Use authenticated user's ID from session if available, fallback to request DTO
      const userId = session.getUserId() || jobRatingDto.userId;
      
      if (!userId) {
        throw new BadRequestException('userId is required');
      }
      
      const { extId, isLiked } = jobRatingDto;
      
      await this.forYouRepository.updateJobRating(userId, extId, isLiked);
      
      return {
        success: true,
        message: 'Job rating successfully updated',
      };
    } catch (error) {
      this.logger.error(`Error updating job rating: ${error.message}`, error.stack);
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      throw new BadRequestException(`Failed to update job rating: ${error.message}`);
    }
  }

  /**
   * Stores classification data for a job
   */
  @Post('/classification')
  @UseGuards(AuthGuard)
  @ApiOperation({
    summary: 'Store job classification data',
    description: 'Stores AI classification data for a specific job and user',
  })
  @ApiBody({
    description: 'Job classification data',
    type: JobClassificationDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Classification data successfully stored',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Classification data successfully stored' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Missing required fields in request body' })
  @ApiResponse({ status: 404, description: 'Job recommendation not found' })
  async storeJobClassification(
    @Request() req,
    @SessionUser() session: SessionContainer,
    @Body(new ValidationPipe()) jobClassificationDto: JobClassificationDto,
  ) {
    try {
      // Use authenticated user's ID from session if available, fallback to request DTO
      const userId = session.getUserId() || jobClassificationDto.userId;
      
      if (!userId) {
        throw new BadRequestException('userId is required');
      }
      
      const { extId, classificationData } = jobClassificationDto;
      
      await this.forYouRepository.storeClassificationData(userId, extId, classificationData);
      
      return {
        success: true,
        message: 'Classification data successfully stored',
      };
    } catch (error) {
      this.logger.error(`Error storing classification data: ${error.message}`, error.stack);
      
      if (error instanceof NotFoundException) {
        throw error;
      }
      
      throw new BadRequestException(`Failed to store classification data: ${error.message}`);
    }
  }
}
