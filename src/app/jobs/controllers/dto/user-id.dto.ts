import { ApiProperty } from '@nestjs/swagger';
import { SmartProfileResponseDto } from '../../../user/services/smart-profile-response.dto';

/**
 * Schema for user ID request
 */
export class UserIdDto {
  @ApiProperty({
    description: 'User ID for which to generate or retrieve recommendations',
    example: 'user-123456',
  })
  userId: string;

  @ApiProperty({
    description: 'Maximum number of recommendations to generate',
    example: 10,
    required: false,
  })
  limit?: number;
  
  @ApiProperty({
    description: 'Smart profile data for the user, used to generate personalized recommendations',
    required: false,
    type: Object,
  })
  smartProfile?: SmartProfileResponseDto | string | Record<string, any>;
}
