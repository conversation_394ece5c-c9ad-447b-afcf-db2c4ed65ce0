import { ApiProperty } from '@nestjs/swagger';
import { ProcessStatus } from '../../../../entities/user-process-tracking.entity';

export class ForYouStatusDataDto {
  @ApiProperty({
    description: 'Whether the user has any For You recommendations',
    type: <PERSON>olean,
    example: true,
  })
  hasRecommendations: boolean;

  @ApiProperty({
    description: 'Whether For You recommendations generation is currently in progress',
    type: Boolean,
    example: false,
  })
  inProgress: boolean;

  @ApiProperty({
    description: 'Whether recommendations should be refreshed',
    type: Boolean,
    example: false,
  })
  needsRefresh: boolean;

  @ApiProperty({
    description: 'Current status of the For You process',
    nullable: true,
    enum: ProcessStatus,
    example: 'completed',
  })
  processStatus: ProcessStatus | null;

  @ApiProperty({
    description: 'When the recommendations were last updated',
    type: Date,
    nullable: true,
    example: '2025-04-14T12:00:00.000Z',
  })
  lastUpdated: Date | null;
}

export class ForYouStatusDto {
  @ApiProperty({
    description: 'Timestamp of the last job upload to Pinecone',
    type: Date,
    nullable: true,
    example: '2025-04-14T12:00:00.000Z',
  })
  latestJobUploadTimestamp: Date | null;

  @ApiProperty({
    description: 'Whether \'For You\' recommendations were generated for the user today or are being generated',
    type: Boolean,
    example: true,
  })
  hasGeneratedForYouToday: boolean;

  @ApiProperty({
    description: 'Whether the operation was successful',
    type: Boolean,
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Response data',
    type: ForYouStatusDataDto,
  })
  data: ForYouStatusDataDto;

  @ApiProperty({
    description: 'Response message',
    type: String,
    example: 'ForYou status retrieved successfully',
  })
  message: string;

  @ApiProperty({
    description: 'Timestamp of the response',
    type: String,
    example: '2025-04-14T12:00:00.000Z',
  })
  timestamp: string;
}
