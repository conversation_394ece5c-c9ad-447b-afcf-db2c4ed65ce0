import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsN<PERSON>ber, IsOptional, IsString, Max, Min } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { SmartProfileSearchDto } from '../../../react-app/dto/smart-profile-search.dto';

/**
 * DTO for paginated job searches with built-in pagination support
 */
export class PaginatedSearchDto extends SmartProfileSearchDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination (1-based)',
    type: Number,
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    type: Number,
    default: 10,
    minimum: 1,
    maximum: 50,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 10;

  @ApiPropertyOptional({
    description: 'User ID for tracking and personalization',
    type: String,
  })
  @IsOptional()
  @IsString()
  user_id?: string;

  @ApiPropertyOptional({
    description: 'Whether to use cached results if available',
    type: Boolean,
    default: true,
  })
  @IsOptional()
  @Transform(({ value }) => value === 'true' || value === true)
  @IsBoolean()
  useCache?: boolean = true;
}