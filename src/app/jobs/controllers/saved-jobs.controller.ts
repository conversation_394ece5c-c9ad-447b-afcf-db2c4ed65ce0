import { <PERSON>, Post, Get, Delete, Param, Query, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthGuard } from '../../../auth/guards/auth.guard';
import { SavedJobsService } from '../services/saved-jobs.service';
import { SavedJobResponseDto } from '../dtos/saved-job.dto';
import { SessionUser } from '../../../auth/decorators/session.decorator';
import { SessionContainer } from 'supertokens-node/recipe/session';
import { UserActionTrackerService } from '../../user-actions/services/user-action-tracker.service';
import { UserActionSource } from '../../../entities/user-action.entity';

@ApiTags('SavedJobs')
@Controller('v1/saved-jobs')
@UseGuards(AuthGuard)
@ApiBearerAuth()
export class SavedJobsController {
  private readonly logger = new Logger(SavedJobsController.name);

  constructor(
    private readonly savedJobsService: SavedJobsService,
    private readonly userActionTracker: UserActionTrackerService,
  ) {}

  @Post(':extId')
  @ApiOperation({ summary: 'Save a job for a user' })
  @ApiResponse({ status: 201, description: 'Job saved', type: SavedJobResponseDto })
  @ApiResponse({ status: 404, description: 'Job not found' })
  async saveJob(
    @Param('extId') extId: string,
    @SessionUser() session: SessionContainer,
    @Query('userId') userIdFromQuery?: string,
  ): Promise<SavedJobResponseDto> {
    const userId = session.getUserId() || userIdFromQuery;
    if (!userId) {
      throw new Error('User ID must be provided either in session or as a query parameter');
    }
    const saved = await this.savedJobsService.saveJob(userId, extId);
    
    // Track this job save action
    await this.userActionTracker.trackJobSave(
      userId,
      extId,
      UserActionSource.BROWSE,
    );
    
    return {
      id: saved.id,
      userId: saved.userId,
      jobExtId: saved.job_ext_id,
      createdAt: saved.createdAt,
      updatedAt: saved.updatedAt,
    };
  }

  @Get()
  @ApiOperation({ summary: 'Get saved jobs for a user' })
  @ApiResponse({ status: 200, description: 'List of saved jobs', type: [SavedJobResponseDto] })
  async getSavedJobs(
    @SessionUser() session: SessionContainer,
    @Query('userId') userIdFromQuery?: string,
  ): Promise<SavedJobResponseDto[]> {
    const userId = session.getUserId() || userIdFromQuery;
    if (!userId) {
      throw new Error('User ID must be provided either in session or as a query parameter');
    }
    const savedList = await this.savedJobsService.getSavedJobs(userId);
    return savedList.map((saved) => ({
      id: saved.id,
      userId: saved.userId,
      jobExtId: saved.job_ext_id,
      createdAt: saved.createdAt,
      updatedAt: saved.updatedAt,
    }));
  }

  @Delete(':extId')
  @ApiOperation({ summary: 'Remove a saved job for a user' })
  @ApiResponse({ status: 204, description: 'Job unsaved' })
  async removeSavedJob(
    @Param('extId') extId: string,
    @SessionUser() session: SessionContainer,
    @Query('userId') userIdFromQuery?: string,
  ): Promise<void> {
    const userId = session.getUserId() || userIdFromQuery;
    if (!userId) {
      throw new Error('User ID must be provided either in session or as a query parameter');
    }
    await this.savedJobsService.removeSavedJob(userId, extId);
    
    // Track this job unsave action
    await this.userActionTracker.trackJobUnsave(
      userId,
      extId,
      UserActionSource.BROWSE,
    );
  }
}
