import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, In, Repository } from 'typeorm';

import { Job } from '../../../entities/job.entity';
import { JobTranslation } from '../../../entities/job-translation.entity';
import { UpsertData } from './interfaces/upsert-data.interface';
import { ProcessedJob } from '../../../entities/processed-job.entity';
import { IJob } from './interfaces/job.interface';
import { IClassification } from './interfaces/classification.interface';
import { JobLocationService } from '../services/job-location.service';
import { JobSeniority } from 'src/entities/job-seniority.enum';
import { CacheService } from '../../../cache/cache.service';
import { Industry } from '../../../entities/industry.entity';
import { Occupation } from '../../../entities/occupation.entity';

@Injectable()
export class JobsImportRepository {
  private readonly CACHE_PREFIX = 'job:';
  private readonly CACHE_LIST_PREFIX = 'jobs:list:';
  private readonly CACHE_TTL = 3600; // 1 hour in seconds
  private readonly logger = new Logger(JobsImportRepository.name);

  constructor(
    @InjectRepository(Job)
    private readonly repository: Repository<Job>,
    private readonly cacheService: CacheService,
    private jobLocationService: JobLocationService,
    private readonly dataSource: DataSource,
  ) {}

  async find(limit: number, offset = 0) {
    return this.repository.find({
      relations: ['translation'],
      skip: offset,
      take: limit,
    });
  }

  /**
   * Find all jobs that have been uploaded to Pinecone with selected fields
   */
  async findPineconeJobs() {
    return this.repository
      .createQueryBuilder('job')
      .select(['job.id', 'job.ext_id', 'job.expires_at'])
      .where('job.pinecone_uploaded = :uploaded', { uploaded: true })
      .getMany();
  }

  async upsert(
    data: UpsertData,
    classifications: Record<string, IClassification> = {},
  ) {
    const { jobData, jobLocations, jobTranslations } = data;

    this.logger.debug(
      `Starting upsert with ${jobData.length} jobs, ${jobLocations.length} locations, ${jobTranslations.length} translations`,
    );
    const startTime = Date.now();

    try {
      await this.repository.manager.transaction(async (trx: EntityManager) => {
        // Insert jobs first
        this.logger.debug(`Upserting ${jobData.length} jobs...`);
        const jobStartTime = Date.now();
        await trx.getRepository(Job).upsert(jobData, ['ext_id']);
        this.logger.debug(`Jobs upserted in ${Date.now() - jobStartTime}ms`);

        // Handle job locations with relationships
        this.logger.debug(`Processing ${jobLocations.length} job locations...`);
        const locationStartTime = Date.now();
        for (const jobLocation of jobLocations) {
          this.logger.debug(
            `Processing location for job_id: ${jobLocation.job_id}`,
          );
          await this.jobLocationService.createOrUpdate(jobLocation, trx);
        }
        this.logger.debug(
          `Job locations processed in ${Date.now() - locationStartTime}ms`,
        );

        // Handle translations
        this.logger.debug(
          `Upserting ${jobTranslations.length} translations...`,
        );
        const translationStartTime = Date.now();
        await trx
          .getRepository(JobTranslation)
          .upsert(jobTranslations, ['job_id', 'language']);
        this.logger.debug(
          `Translations upserted in ${Date.now() - translationStartTime}ms`,
        );

        // NOTE: Automatic job processing during import has been removed
        // Job processing is now handled separately via:
        // 1. Manual processing via /jobs/processing endpoint
        // 2. Export to CSV for external processing via /jobs/export-batch
        // 3. Processing callback via /jobs/callback endpoint
      });

      this.logger.debug(`Upsert completed in ${Date.now() - startTime}ms`);
    } catch (error) {
      this.logger.error(`Error in upsert: ${error.message}`, error.stack);
      throw error;
    }
  }

  async findByExtIds(ids: string[]) {
    const existingJobs = await this.repository.find({
      where: { ext_id: In(ids) },
    });

    return existingJobs.map((job) => job.ext_id);
  }

  /**
   * Find jobs by their external IDs with full details
   * @param ids Array of external job IDs
   * @returns Array of job objects with all details
   */
  async findJobsByExtIds(ids: string[]): Promise<Job[]> {
    return this.repository.find({
      where: { ext_id: In(ids) },
      relations: [
        'translation',
        'processedJob',
        'location',
        'location.regions',
        'location.municipalities',
      ],
    });
  }

  /**
   * Find a job by its external ID
   * @param ext_id External job ID
   * @returns The job if found, null otherwise
   */
  async findByExtId(ext_id: string): Promise<Job | null> {
    // Try to get from cache first
    const cacheKey = `${this.CACHE_PREFIX}${ext_id}`;
    const cachedJob = await this.cacheService.get<Job>(cacheKey);

    if (cachedJob) {
      return cachedJob;
    }

    // If not in cache, get from database
    const job = await this.repository.findOne({
      where: { ext_id },
      relations: [
        'translation',
        'processedJob',
        'processedJob.industry',
        'processedJob.occupation',
        'location',
        'location.regions',
        'location.municipalities',
        'employer',
      ],
    });

    // Cache the result
    if (job) {
      await this.cacheService.set(cacheKey, job, this.CACHE_TTL);
    }

    return job;
  }

  /**
   * Find jobs that need to be processed
   * Filters out already processed and expired jobs
   * @param limit Maximum number of jobs to return
   */
  async findJobsForProcessing(limit = 100): Promise<Job[]> {
    // Update expired jobs first to ensure we have accurate data
    await this.updateExpiredJobs();

    return this.repository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.translation', 'translation')
      .where('job.processed = :processed', { processed: false })
      .andWhere('job.queued_for_processing = :queuedForProcessing', {
        queuedForProcessing: false,
      })
      .andWhere('job.expired = :expired', { expired: false })
      .limit(limit)
      .getMany();
  }

  /**
   * Finds jobs that haven't been processed or queued for processing
   * @param limit Maximum number of jobs to return
   * @returns Array of unprocessed jobs
   */
  async findUnprocessedJobs(limit: number): Promise<Job[]> {
    return this.repository.find({
      where: {
        processed: false,
        queued_for_processing: false,
        expired: false,
      },
      relations: ['translation'],
      take: limit,
    });
  }

  /**
   * Marks jobs as queued for processing
   * @param jobIds Array of job IDs to mark as queued
   */
  async markJobsAsQueued(jobIds: string[]): Promise<void> {
    await this.repository.update(
      { ext_id: In(jobIds) },
      { queued_for_processing: true },
    );
  }

  /**
   * Marks a job as processed
   * @param jobId Job ID to mark as processed
   */
  async markJobAsProcessed(jobId: string): Promise<void> {
    await this.repository.update(
      { ext_id: jobId },
      {
        processed: true,
        queued_for_processing: false,
      },
    );
  }

  /**
   * Find all processed jobs
   * @returns All jobs marked as processed
   */
  async findProcessedJobs(): Promise<Job[]> {
    // Update expired jobs first to ensure we have accurate data
    await this.updateExpiredJobs();

    const query = this.repository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.translation', 'translation')
      .leftJoinAndSelect('job.processedJob', 'processed_job')
      .leftJoinAndSelect('job.location', 'location')
      .leftJoinAndSelect('location.regions', 'regions')
      .leftJoinAndSelect('location.municipalities', 'municipalities')
      .where('job.processed = :processed', { processed: true })
      .andWhere('job.expired = :expired', { expired: false });

    return query.getMany();
  }

  /**
   * Find all processed jobs that haven't been uploaded to Pinecone
   * @returns Processed jobs that need to be uploaded to Pinecone
   */
  async findProcessedJobsForPinecone(): Promise<Job[]> {
    // Check cache first
    const cacheKey = `${this.CACHE_LIST_PREFIX}pinecone_pending`;
    const cachedJobs = false; // await this.cacheService.get<Job[]>(cacheKey);

    if (cachedJobs) {
      return cachedJobs;
    }

    // Update expired jobs first to ensure we have accurate data
    await this.updateExpiredJobs();

    const query = this.repository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.translation', 'translation')
      .leftJoinAndSelect('job.processedJob', 'processed_job')
      .leftJoinAndSelect('job.location', 'location')
      .leftJoinAndSelect('location.regions', 'regions')
      .leftJoinAndSelect('location.municipalities', 'municipalities')
      .where('job.processed = :processed', { processed: true })
      .andWhere('job.pinecone_uploaded = :pinecone_uploaded', {
        pinecone_uploaded: false,
      })
      .andWhere('job.expired = :expired', { expired: false });

    const jobs = await query.getMany();

    // Cache the result (short TTL since this list changes often)
    // await this.cacheService.set(cacheKey, jobs, 300); // 5 minutes

    return jobs;
  }

  // Keep this method for backward compatibility and gradual migration
  async upsertProcessedJobs(job: IJob, classification: any) {
    const translationEn = job.translation.find(
      (item) => item.language?.toLowerCase() === 'en',
    );

    // Skip if no English translation is available
    if (!translationEn) {
      this.logger.warn(
        `[upsertProcessedJobs] No English translation found for job ${job.ext_id}`,
      );
      return;
    }

    this.logger.log(
      `[upsertProcessedJobs] Starting transaction for job ${job.ext_id}`,
    );

    return await this.dataSource.transaction(async (trx: EntityManager) => {
      const jobRepo = trx.getRepository(Job);
      const processedJobRepo = trx.getRepository(ProcessedJob);
      const industryRepo = trx.getRepository(Industry);
      const occupationRepo = trx.getRepository(Occupation);

      // First, ensure the Job exists in the database
      this.logger.log(
        `[upsertProcessedJobs] Checking if job ${job.ext_id} exists in database`,
      );
      const existingJob = await jobRepo.findOne({
        where: { ext_id: job.ext_id },
      });
      if (!existingJob) {
        this.logger.error(
          `[upsertProcessedJobs] Job with ext_id ${job.ext_id} not found in database, cannot create ProcessedJob`,
        );
        throw new Error(`Job with ext_id ${job.ext_id} not found in database`);
      }

      this.logger.log(
        `[upsertProcessedJobs] Found existing job with ext_id ${job.ext_id}, id: ${existingJob.id}`,
      );

      // Validate foreign key references before upserting
      // Check industry code if provided
      if (classification.industry) {
        this.logger.log(
          `[upsertProcessedJobs] Validating industry code: ${classification.industry}`,
        );
        const industry = await industryRepo.findOne({
          where: { code: classification.industry },
        });
        if (!industry) {
          this.logger.warn(
            `[upsertProcessedJobs] Industry with code ${classification.industry} not found, setting to FREELANCE`,
          );
          classification.industry = 'FREELANCE';
        }
      } else {
        // Use FREELANCE as the default value if no industry code is provided
        this.logger.warn(
          `[upsertProcessedJobs] No industry code provided, setting to FREELANCE`,
        );
        classification.industry = 'FREELANCE';
      }

      // Check occupation codes - primary, secondary, and tertiary
      // Primary occupation
      if (classification.occupation) {
        this.logger.log(
          `[upsertProcessedJobs] Validating primary occupation code: ${classification.occupation}`,
        );
        const occupation = await occupationRepo.findOne({
          where: { code: classification.occupation },
        });
        if (!occupation) {
          this.logger.warn(
            `[upsertProcessedJobs] Primary occupation with code ${classification.occupation} not found, setting to null`,
          );
          classification.occupation = null;
        }
      } else {
        // Ensure it's explicitly null if not provided
        classification.occupation = null;
      }

      // Secondary occupation
      if (classification.occupation_secondary) {
        this.logger.log(
          `[upsertProcessedJobs] Validating secondary occupation code: ${classification.occupation_secondary}`,
        );
        const occupation = await occupationRepo.findOne({
          where: { code: classification.occupation_secondary },
        });
        if (!occupation) {
          this.logger.warn(
            `[upsertProcessedJobs] Secondary occupation with code ${classification.occupation_secondary} not found, setting to null`,
          );
          classification.occupation_secondary = null;
        }
      } else {
        // Ensure it's explicitly null if not provided
        classification.occupation_secondary = null;
      }

      // Tertiary occupation
      if (classification.occupation_tertiary) {
        this.logger.log(
          `[upsertProcessedJobs] Validating tertiary occupation code: ${classification.occupation_tertiary}`,
        );
        const occupation = await occupationRepo.findOne({
          where: { code: classification.occupation_tertiary },
        });
        if (!occupation) {
          this.logger.warn(
            `[upsertProcessedJobs] Tertiary occupation with code ${classification.occupation_tertiary} not found, setting to null`,
          );
          classification.occupation_tertiary = null;
        }
      } else {
        // Ensure it's explicitly null if not provided
        classification.occupation_tertiary = null;
      }

      // Build a summary string for semantic search
      const summary =
        `${classification.occupation || ''} ${classification.occupation_secondary || ''} ${classification.occupation_tertiary || ''} ${classification.skills || ''}`.trim();

      // Upsert processed job with all the new fields
      this.logger.log(
        `[upsertProcessedJobs] Attempting to upsert ProcessedJob for job_id: ${job.ext_id}`,
      );

      // Log final industry code to verify we're not sending null
      this.logger.log(
        `[upsertProcessedJobs] Using final industry_code value: ${classification.industry || 'FREELANCE'}`,
      );

      try {
        await processedJobRepo.upsert(
          {
            job_id: job.ext_id,
            industry_code: classification.industry || 'FREELANCE', // Double check to ensure never null
            occupation_code: classification.occupation ?? null,
            occupation_code_secondary:
              classification.occupation_secondary ?? null,
            occupation_code_tertiary:
              classification.occupation_tertiary ?? null,
            summary,
            seniority: classification.seniority as JobSeniority,
            skills: classification.skills,
            salary: classification.salary,
            elevator_pitch: this.cleanupText(classification.elevator_pitch),
            primary_tasks: classification.primary_tasks || [],
            keywords: classification.keywords || [],
            skill_contexts: classification.skill_contexts || [],
            expected_outcomes: classification.expected_outcomes || [],
            possible_career_outcomes:
              classification.possible_career_outcomes || [],
            typical_day_tasks: classification.typical_day_tasks || [],
            culture_keywords: classification.culture_keywords || [],
            ideal_candidate_traits: classification.ideal_candidate_traits || [],
            work_life_balance_signals:
              classification.work_life_balance_signals || [],
            role_impact_summary: classification.role_impact_summary,
            market_salary: classification.market_salary,
            evolution: classification.evolution
              ? typeof classification.evolution === 'string'
                ? JSON.parse(this.cleanupText(classification.evolution))
                : {
                    evolution_summary:
                      classification.evolution.evolution_summary,
                    automation_impact:
                      classification.evolution.automation_impact,
                    relevance_prediction:
                      classification.evolution.relevance_prediction,
                  }
              : undefined,
            contacts: classification.contacts || null,
            requirements: classification.requirements || null,
            degree_required: classification.degree_required || false,
            internship: classification.internship || false,
            application_url: classification.application_url || null,
          },
          ['job_id'],
        );
        this.logger.log(
          `[upsertProcessedJobs] Successfully upserted ProcessedJob for job_id: ${job.ext_id}`,
        );
      } catch (error) {
        this.logger.error(
          `[upsertProcessedJobs] Error upserting ProcessedJob: ${error.message}`,
          error.stack,
        );
        throw error;
      }

      // Process location data if available
      if (
        classification.municipalities?.length > 0 ||
        classification.regions?.length > 0
      ) {
        this.logger.log(
          `[upsertProcessedJobs] Processing location data for job ${job.ext_id}`,
        );
        try {
          // Try to find an existing job location
          const existingLocation = await this.jobLocationService.findByJobId(
            job.ext_id,
          );

          // Prepare the location data
          const jobLocationData: any = {
            job_id: job.ext_id,
            _municipalityCodes: classification.municipalities || [],
            _regionCodes: classification.regions || [],
          };

          // If we have an existing location, include the ID
          if (existingLocation) {
            jobLocationData.id = existingLocation.id;
          }

          // Create or update the job location
          await this.jobLocationService.createOrUpdate(jobLocationData, trx);

          this.logger.log(
            `[upsertProcessedJobs] Successfully saved location data for job ${job.ext_id}`,
          );
        } catch (locationError) {
          this.logger.error(
            `[upsertProcessedJobs] Error saving location data for job ${job.ext_id}: ${locationError.message}`,
            locationError.stack,
          );
          // We don't want to fail the entire transaction if just the location update fails
          // This is an MVP, so we'll log the error but continue
        }
      } else {
        this.logger.log(
          `[upsertProcessedJobs] No location data provided for job ${job.ext_id}`,
        );
      }

      // Invalidate cache for this job
      const cacheKey = `${this.CACHE_PREFIX}${job.ext_id}`;
      await this.cacheService.delete(cacheKey);
      await this.invalidateListCaches();
    });
  }

  /**
   * Cleans up text by removing code block formatting, especially for JSON blocks
   * @param text Text to clean
   * @returns Cleaned text
   */
  private cleanupText(text: string): string {
    if (!text) return '';

    // First, try to extract JSON from code blocks if they exist
    const jsonBlockRegex = /```\s*json\s*([\s\S]*?)```/;
    const jsonBlockMatch = text.match(jsonBlockRegex);

    // If we found a JSON code block with content
    if (jsonBlockMatch && jsonBlockMatch[1] && jsonBlockMatch[1].trim()) {
      try {
        const jsonContent = JSON.parse(jsonBlockMatch[1].trim());

        // Case 1: If it has an "answer" field, use that
        if (jsonContent.answer) {
          return jsonContent.answer.trim();
        }

        // Case 2: If it's an array, join the elements
        if (Array.isArray(jsonContent)) {
          return jsonContent.join(', ');
        }

        // Case 3: If it's an object with a single property that's an array
        // (like work_life_balance_signals), join that array
        const keys = Object.keys(jsonContent);
        if (keys.length === 1 && Array.isArray(jsonContent[keys[0]])) {
          return jsonContent[keys[0]].join(', ');
        }

        // Case 4: Otherwise, stringify the JSON in a readable format
        return JSON.stringify(jsonContent, null, 2);
      } catch (e) {
        // If parsing fails, just use the content directly
        return jsonBlockMatch[1].trim();
      }
    }

    // Otherwise, proceed with regular cleanup
    // Remove JSON code block formatting if present
    const withoutJsonFormatting = text.replace(
      /```\s*json\s*([\s\S]*?)```/g,
      '$1',
    );

    // Trim any extra whitespace
    return withoutJsonFormatting.trim();
  }

  /**
   * Save a job entity
   * @param job The job to save
   * @returns The saved job
   */
  async save(job: Job): Promise<Job> {
    const savedJob = await this.repository.save(job);

    // Update the cache
    const cacheKey = `${this.CACHE_PREFIX}${job.ext_id}`;
    await this.cacheService.set(cacheKey, savedJob, this.CACHE_TTL);

    // Invalidate list caches when a job is updated
    await this.invalidateListCaches();

    return savedJob;
  }

  /**
   * Update expiry status for jobs
   * Sets expired=true for jobs where current date > expires_at
   * @returns Number of jobs marked as expired
   */
  async updateExpiredJobs(): Promise<number> {
    const result = await this.repository
      .createQueryBuilder()
      .update(Job)
      .set({ expired: true })
      .where('expires_at < NOW()')
      .andWhere('expired = :expired', { expired: false })
      .execute();

    return result.affected || 0;
  }

  /**
   * Mark jobs from a specific source as expired if they are not in the activeExtIds list
   * Used to expire jobs that are no longer present in feed imports
   * @param activeExtIds List of external IDs that are currently active/available in the feed
   * @param source Source identifier (e.g., 'Jobly', 'Tyomarkkinatori')
   * @returns Number of jobs marked as expired
   */
  async markSourceJobsAsExpired(
    activeExtIds: string[],
    source: string,
  ): Promise<number> {
    this.logger.debug(`Marking expired jobs from source ${source}`);

    const query = this.repository
      .createQueryBuilder()
      .update(Job)
      .set({ expired: true, expires_at: new Date() })
      .where('source = :source', { source })
      .andWhere('expired = false');

    if (activeExtIds.length > 0) {
      // If we have active IDs, mark as expired all jobs NOT in this list
      query.andWhere('ext_id NOT IN (:...activeExtIds)', { activeExtIds });
    }
    // If activeExtIds is empty, all jobs from this source will be marked as expired

    const result = await query.execute();
    const affectedCount = result.affected || 0;

    this.logger.debug(
      `Marked ${affectedCount} jobs from source ${source} as expired`,
    );

    return affectedCount;
  }

  /**
   * Count how many jobs in the database are currently marked as expired
   *
   * @returns Number of expired jobs
   */
  async countExpiredJobs(): Promise<number> {
    const count = await this.repository
      .createQueryBuilder('job')
      .where('job.expired = :expired', { expired: true })
      .getCount();

    return count;
  }

  /**
   * Count unexpired jobs
   * @returns Number of unexpired jobs
   */
  async countUnexpiredJobs(): Promise<number> {
    const count = await this.repository
      .createQueryBuilder('job')
      .where('job.expired = :expired', { expired: false })
      .getCount();

    return count;
  }

  /**
   * Count unexpired jobs that have been uploaded to Pinecone
   * @returns Number of unexpired jobs uploaded to Pinecone
   */
  async countUnexpiredJobsInPinecone(): Promise<number> {
    const count = await this.repository
      .createQueryBuilder('job')
      .where('job.expired = :expired', { expired: false })
      .andWhere('job.pinecone_uploaded = :uploaded', { uploaded: true })
      .getCount();

    return count;
  }

  /**
   * Find jobs by their internal IDs with full details
   * @param ids Array of internal job IDs
   * @returns Array of job objects with all details
   */
  async findByIds(ids: number[]): Promise<Job[]> {
    return this.repository.find({
      where: { id: In(ids) },
      relations: [
        'translation',
        'processedJob',
        'location',
        'location.regions',
        'location.municipalities',
      ],
    });
  }

  /**
   * Find jobs by their external IDs with all details needed for the job listing page
   * Includes employer, location, processed job data, and all other related information
   *
   * @param ids Array of external job IDs
   * @returns Array of job objects with all relationships loaded
   */
  async findJobsWithDetails(ids: string[]): Promise<Job[]> {
    // Try to get from cache first
    const cacheKey = `${this.CACHE_LIST_PREFIX}${ids.sort().join('-')}`;
    const cachedJobs = await this.cacheService.get<Job[]>(cacheKey);

    if (cachedJobs) {
      return cachedJobs;
    }

    // If not in cache, get from database with all necessary relations
    const jobs = await this.repository.find({
      where: { ext_id: In(ids) },
      relations: [
        'employer',
        'location',
        'location.regions',
        'location.municipalities',
        'processedJob',
        'processedJob.industry',
        'processedJob.occupation',
      ],
    });

    // Cache the result
    if (jobs.length > 0) {
      await this.cacheService.set(cacheKey, jobs, this.CACHE_TTL);
    }

    return jobs;
  }

  /**
   * Find unexpired jobs with pagination and all necessary details
   * Used for direct database queries when no search filters are applied
   * 
   * @param page Current page (1-based)
   * @param limit Number of items per page
   * @param sortBy Field to sort by (default: 'published_date')
   * @param sortDirection Sort direction ('asc' or 'desc', default: 'desc')
   * @returns Promise containing jobs array and total count
   */
  async findUnexpiredJobsWithPagination(
    page: number = 1,
    limit: number = 20,
    sortBy: string = 'published_date',
    sortDirection: 'ASC' | 'DESC' = 'DESC'
  ): Promise<{ jobs: Job[]; total: number }> {
    // Build the query
    const query = this.repository
      .createQueryBuilder('job')
      .leftJoinAndSelect('job.employer', 'employer')
      .leftJoinAndSelect('job.location', 'location')
      .leftJoinAndSelect('location.regions', 'regions')
      .leftJoinAndSelect('location.municipalities', 'municipalities')
      .leftJoinAndSelect('job.processedJob', 'processedJob')
      .leftJoinAndSelect('processedJob.industry', 'industry')
      .leftJoinAndSelect('processedJob.occupation', 'occupation')
      .where('job.expired = :expired', { expired: false })
      .andWhere('job.processed = :processed', { processed: true }); // Only show processed jobs

    // Add sorting
    const sortField = sortBy === 'published_date' ? 'job.published_date' : `job.${sortBy}`;
    query.orderBy(sortField, sortDirection);

    // Add pagination
    const skip = (page - 1) * limit;
    query.skip(skip).take(limit);

    // Execute query and get total count
    const [jobs, total] = await query.getManyAndCount();

    this.logger.debug(
      `Found ${jobs.length} unexpired jobs (page ${page}, total ${total})`
    );

    return { jobs, total };
  }

  /**
   * Invalidate all list caches when data changes
   * This is a simple approach for the MVP
   */
  private async invalidateListCaches(): Promise<void> {
    // For MVP, we'll just clear a few key cache entries
    await this.cacheService.delete(`${this.CACHE_LIST_PREFIX}pinecone_pending`);
    await this.cacheService.delete(`${this.CACHE_LIST_PREFIX}processed`);
    await this.cacheService.delete(`${this.CACHE_LIST_PREFIX}unprocessed`);
  }
}

export { Job }; // Add this line to export the Job entity
