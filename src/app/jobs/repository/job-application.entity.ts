import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Job } from '../../../entities/job.entity';

@Entity({ name: 'job_applications' })
export class JobApplication {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid', nullable: false })
  userId: string;

  @Column({ type: 'text', nullable: false })
  cvHtml: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  cvUrl: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  jobExtId: string;

  @ManyToOne(() => Job)
  @JoinColumn({ name: 'jobExtId', referencedColumnName: 'ext_id' })
  job: Job;

  @Column({ type: 'varchar', length: 50, nullable: false })
  selectedLanguage: string;

  @Column({ type: 'varchar', length: 50, nullable: true })
  language: string;

  @Column({ type: 'varchar', length: 50, nullable: false })
  documentType: string;

  @Column({ type: 'varchar', length: 100, nullable: false })
  selectedTemplate: string;

  @Column({ type: 'jsonb', nullable: true })
  sections: Record<string, any>[];

  @Column({ type: 'text', nullable: true })
  generatedAboutMe: string;

  @Column({ type: 'jsonb', nullable: true })
  keyFitPoints: Record<string, any>[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
