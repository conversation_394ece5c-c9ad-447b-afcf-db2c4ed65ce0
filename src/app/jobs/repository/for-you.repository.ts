import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, Repository } from 'typeorm';
import { ForYou } from '../../../entities/for-you.entity';
import { Job } from '../../../entities/job.entity';
import { CacheService } from '../../../cache/cache.service';
import { ClassifyJobDto } from '../../react-app/dto/matched-job.dto';
import { formatJobsForResponse } from '../services/helpers/job-data-to-entity.converter';

@Injectable()
export class ForYouRepository {
  private readonly logger = new Logger(ForYouRepository.name);
  private readonly CACHE_TTL = 24 * 60 * 60; // 24 hours in seconds
  private readonly CACHE_KEY_PREFIX = 'for_you_recommendations:';

  constructor(
    @InjectRepository(ForYou)
    private readonly forYouRepository: Repository<ForYou>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly cacheService: CacheService,
  ) {}

  /**
   * Get cache key for a user's recommendations
   * @param userId User ID
   * @returns Cache key string
   */
  private getCacheKey(userId: string): string {
    return `${this.CACHE_KEY_PREFIX}${userId}`;
  }

  /**
   * Save recommended jobs for a user
   * @param userId User ID
   * @param jobExtIds Array of job external IDs to save as recommendations
   * @returns Array of saved ForYou entities
   */
  async saveForYouJobs(userId: string, jobExtIds: string[]): Promise<ForYou[]> {
    this.logger.log(
      `Saving ${jobExtIds.length} recommended jobs for user ${userId}`,
    );

    // Clear previous recommendations for this user
    await this.forYouRepository.delete({ userId });

    // Create new recommendations
    const forYouEntities = jobExtIds.map((jobExtId) => {
      const forYou = new ForYou();
      forYou.userId = userId;
      forYou.job_ext_id = jobExtId;
      return forYou;
    });

    // Save to database
    const savedEntities = await this.forYouRepository.save(forYouEntities);

    // Invalidate cache
    await this.cacheService.delete(this.getCacheKey(userId));

    return savedEntities;
  }

  /**
   * Get recommended jobs for a user with interaction data
   * @param userId User ID
   * @returns Array of recommended items with job and interaction data
   */
  async getForYouJobs(userId: string): Promise<{ job: any; forYou: ForYou }[]> {
    this.logger.log(`Getting recommended jobs for user ${userId}`);

    // Try to get from cache first
    const cacheKey = this.getCacheKey(userId);
    const cachedRecommendations =
      await this.cacheService.get<{ job: any; forYou: ForYou }[]>(cacheKey);

    if (cachedRecommendations) {
      this.logger.log(
        `Retrieved ${cachedRecommendations.length} recommended jobs from cache for user ${userId}`,
      );
      return cachedRecommendations;
    }

    // If not in cache, get from database
    const forYouEntries = await this.forYouRepository.find({
      where: { userId },
      relations: ['job'],
    });

    if (!forYouEntries || forYouEntries.length === 0) {
      this.logger.warn(`No recommended jobs found for user ${userId}`);
      return [];
    }

    // Filter out entries with null jobs
    const validEntries = forYouEntries.filter(
      (entry) => entry.job !== null && entry.job !== undefined,
    );

    // Load job relations for complete job data
    const recommendationsWithRelations = await Promise.all(
      validEntries.map(async (forYouEntry) => {
        const jobWithRelations = await this.jobRepository.findOne({
          where: { ext_id: forYouEntry.job_ext_id },
          relations: ['employer', 'location', 'processedJob'],
        });

        // Skip entries where job couldn't be found with relations
        if (!jobWithRelations) {
          return null;
        }

        return {
          job: jobWithRelations,
          forYou: forYouEntry,
        };
      }),
    );

    // Filter out any null results
    const filteredRecommendations = recommendationsWithRelations.filter(
      (item): item is { job: Job; forYou: ForYou } =>
        item !== null && item.job !== null && item.job !== undefined,
    );

    // Format job data for response
    const formattedRecommendations = filteredRecommendations.map(
      (recommendation) => ({
        job: formatJobsForResponse([recommendation.job])[0],
        forYou: recommendation.forYou,
      }),
    );

    // Cache the results
    await this.cacheService.set(
      cacheKey,
      formattedRecommendations,
      this.CACHE_TTL,
    );

    return formattedRecommendations;
  }

  /**
   * Get yesterday's recommended jobs for a user when today's aren't available
   * @param userId User ID
   * @returns Array of recommended items with job and interaction data from yesterday
   */
  async getYesterdaysRecommendations(
    userId: string,
  ): Promise<{ job: any; forYou: ForYou }[]> {
    this.logger.log(`Getting yesterday's recommended jobs for user ${userId}`);

    // Calculate yesterday's date range
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    yesterday.setHours(0, 0, 0, 0);

    const endOfYesterday = new Date(yesterday);
    endOfYesterday.setHours(23, 59, 59, 999);

    // Get recommendations from yesterday directly from database (bypass cache)
    const forYouEntries = await this.forYouRepository.find({
      where: {
        userId,
        recommended_at: Between(yesterday, endOfYesterday),
      },
      relations: ['job'],
    });

    if (!forYouEntries || forYouEntries.length === 0) {
      this.logger.warn(
        `No yesterday's recommended jobs found for user ${userId}`,
      );
      return [];
    }

    // Filter out entries with null jobs
    const validEntries = forYouEntries.filter(
      (entry) => entry.job !== null && entry.job !== undefined,
    );

    // Load job relations for complete job data
    const recommendationsWithRelations = await Promise.all(
      validEntries.map(async (forYouEntry) => {
        const jobWithRelations = await this.jobRepository.findOne({
          where: { ext_id: forYouEntry.job_ext_id },
          relations: ['employer', 'location', 'processedJob'],
        });

        // Skip entries where job couldn't be found with relations
        if (!jobWithRelations) {
          return null;
        }

        return {
          job: jobWithRelations,
          forYou: forYouEntry,
        };
      }),
    );

    // Filter out any null results
    const filteredRecommendations = recommendationsWithRelations.filter(
      (item): item is { job: Job; forYou: ForYou } =>
        item !== null && item.job !== null && item.job !== undefined,
    );

    // Format job data for response
    const formattedRecommendations = filteredRecommendations.map(
      (recommendation) => ({
        job: formatJobsForResponse([recommendation.job])[0],
        forYou: recommendation.forYou,
      }),
    );

    return formattedRecommendations;
  }

  /**
   * Check if a user has any recommended jobs
   * @param userId User ID
   * @returns Boolean indicating if the user has recommendations
   */
  async hasForYouJobs(userId: string): Promise<boolean> {
    // Try cache first
    const cacheKey = this.getCacheKey(userId);
    const cachedRecommendations =
      await this.cacheService.get<{ job: any; forYou: ForYou }[]>(cacheKey);

    if (cachedRecommendations) {
      return cachedRecommendations.length > 0;
    }

    // If not in cache, check database
    const count = await this.forYouRepository.count({
      where: { userId },
    });

    return count > 0;
  }

  /**
   * Mark a job as viewed by a user
   * @param userId User ID
   * @param jobId Job ID (external ID)
   * @returns Updated ForYou entity
   */
  async markJobAsViewed(userId: string, jobId: string): Promise<ForYou> {
    const forYouEntry = await this.forYouRepository.findOne({
      where: { userId, job_ext_id: jobId },
    });

    if (!forYouEntry) {
      this.logger.warn(
        `No recommendation found for user ${userId} and job ${jobId}`,
      );
      throw new NotFoundException(
        `Recommendation not found for user ${userId} and job ${jobId}`,
      );
    }

    // Update the viewed status
    forYouEntry.is_viewed = true;

    // Save the updated entity
    const updated = await this.forYouRepository.save(forYouEntry);

    // Invalidate cache
    await this.cacheService.delete(this.getCacheKey(userId));

    return updated;
  }

  /**
   * Update user rating for a job (like or dislike)
   * @param userId User ID
   * @param jobId Job ID (external ID)
   * @param isLiked Boolean indicating if the job is liked (true) or disliked (false)
   * @returns Updated ForYou entity
   */
  async updateJobRating(
    userId: string,
    jobId: string,
    isLiked: boolean,
  ): Promise<ForYou> {
    const forYouEntry = await this.forYouRepository.findOne({
      where: { userId, job_ext_id: jobId },
    });

    if (!forYouEntry) {
      this.logger.warn(
        `No recommendation found for user ${userId} and job ${jobId}`,
      );
      throw new NotFoundException(
        `Recommendation not found for user ${userId} and job ${jobId}`,
      );
    }

    // Update the rating
    forYouEntry.is_liked = isLiked;

    // Save the updated entity
    const updated = await this.forYouRepository.save(forYouEntry);

    // Invalidate cache
    await this.cacheService.delete(this.getCacheKey(userId));

    return updated;
  }

  /**
   * Store classification data for a job
   * @param userId User ID
   * @param jobId Job ID (external ID)
   * @param classificationData ClassifyJobDto object with classification data
   * @returns Updated ForYou entity
   */
  async storeClassificationData(
    userId: string,
    jobId: string,
    classificationData: ClassifyJobDto,
  ): Promise<ForYou> {
    const forYouEntry = await this.forYouRepository.findOne({
      where: { userId, job_ext_id: jobId },
    });

    if (!forYouEntry) {
      this.logger.warn(
        `No recommendation found for user ${userId} and job ${jobId}`,
      );
      throw new NotFoundException(
        `Recommendation not found for user ${userId} and job ${jobId}`,
      );
    }

    // Store the classification data
    forYouEntry.classification_data = classificationData;

    // Save the updated entity
    const updated = await this.forYouRepository.save(forYouEntry);

    // Invalidate cache
    await this.cacheService.delete(this.getCacheKey(userId));

    return updated;
  }

  /**
   * Checks if 'For You' recommendations were generated for the user today.
   * @param userId User ID
   * @returns Boolean indicating if recommendations were generated today.
   */
  async hasGeneratedForYouToday(userId: string): Promise<boolean> {
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    const count = await this.forYouRepository.count({
      where: {
        userId,
        recommended_at: Between(todayStart, todayEnd),
      },
    });

    return count > 0;
  }

  /**
   * Gets the timestamp of the latest job upload to Pinecone.
   * @returns The latest timestamp or null if no jobs have been uploaded.
   */
  async getLatestPineconeUploadTimestamp(): Promise<Date | null> {
    const result = await this.jobRepository
      .createQueryBuilder('job')
      .select('MAX(job.pinecone_uploaded_at)', 'max_timestamp')
      .getRawOne();

    return result?.max_timestamp || null;
  }
}
