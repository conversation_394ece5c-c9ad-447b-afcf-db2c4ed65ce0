import {
  Contact,
  Requirements,
  RoleImpactSummary,
} from '../../../../entities/processed-job.entity';

export interface IClassification {
  industry: string;
  occupation: string; // ESCO Occupation code
  occupation_secondary?: string; // Secondary ESCO Occupation code
  occupation_tertiary?: string; // Tertiary ESCO Occupation code
  seniority: string; // Seniority level of the job [NONE | ENTRY | JUNIOR | MID | SENIOR | LEAD | MANAGERIAL | EXECUTIVE]
  skills: string[]; // Short Phrases that describe Non-requirements but still relevant skills. (eg: React for frontend development)
  salary?: {
    salary_lower_bound: number;
    salary_upper_bound: number;
    period: string;
  };
  elevator_pitch?: string; // Elevator pitch for the job
  primary_tasks: string[]; // Primary tasks of the job role
  keywords: string[]; // Key word phrases that describe this job
  skill_contexts: string[]; // Short phrases to describe what core skills are required
  expected_outcomes: string[]; // What are the key deliverables or results expected from this role?
  possible_career_outcomes: string[]; // Clear indication on what are the career progression opportunities for this role.
  typical_day_tasks: string[]; // Typical day-to-day tasks of the job role - Pattern recognition for task-oriented language, extraction of verbs and objects describing actions, summarization of longer descriptions into task lists.
  culture_keywords: string[]; // Key phrases that describe the company culture
  ideal_candidate_traits: string[]; // Key phrases that describe the ideal candidate (e.g., "proactive," "problem-solver," "communicator," "detail-oriented")
  work_life_balance_signals: string[]; // List of signals - e.g., "flexible hours," "remote work options," "focus on employee well-being"
  role_impact_summary: RoleImpactSummary; // Structured information about the role's impact and working environment
  market_salary?: {
    job_role: string;
    median_salary: number;
    salary_lower_bound: number;
    salary_upper_bound: number;
    currency: string;
    salary_period: string;
    data_source: string;
    data_reliability: string;
  };
  evolution?: {
    evolution_summary: string;
    automation_impact: string;
    relevance_prediction: string;
  };
  internship: boolean;
  contacts: Contact;
  requirements: Requirements;
}