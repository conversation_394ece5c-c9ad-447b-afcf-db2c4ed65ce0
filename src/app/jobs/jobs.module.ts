import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';

import { CacheModule } from '../../cache/cache.module';
import { TranslationModule } from '../translation/translation.module';
import { <PERSON>s<PERSON>ontroller } from './controllers/jobs.controller';
import { JobApplicationController } from './controllers/job-application.controller';
import { JobMarketApiClient } from './services/job-market-api.service';
import { JoblyApiClient } from './services/jobly-api.service';
import { JobsImportService } from './services/jobs-import.service';
import { JobsProcessingService } from './services/jobs-processing.service';
import { JobsExportService } from './services/jobs-export.service';
import { JobsPineconeService } from './services/jobs-pinecone.service';
import { JoblyDataToEntityConverter } from './services/helpers/jobly-data-to-entity.converter';
import { JobMetadataService } from './services/job-metadata.service';
import { JobsImportRepository } from './repository/jobs-import.repository';
import { JobLocationService } from './services/job-location.service';
import { ForYouRepository } from './repository/for-you.repository';
import { RecommendationEngineService } from './services/recommendation-engine.service';
import { DifyService } from './services/helpers/dify.service';
import { JobApplicationService } from './services/job-application.service';
import { SavedJobsService } from './services/saved-jobs.service';

import { Job } from '../../entities/job.entity';
import { JobLocation } from '../../entities/job-location.entity';
import { JobTranslation } from '../../entities/job-translation.entity';
import { ForYou } from '../../entities/for-you.entity';
import { JobApplication } from './repository/job-application.entity';
import { SavedJob } from '../../entities/saved-job.entity';
import { JobMetadata } from '../../entities/job-metadata.entity';
import { PubSubModule } from '../shared/pubsub/pubsub.module';
import { EmployerModule } from '../employer/employer.module';
import { MunicipalityModule } from '../municipality/municipality.module';
import { RegionModule } from '../region/region.module';
import { PineconeModule } from '../../pinecone/pinecone.module';
import { JobsSearchService } from './services/jobs-search.service';
import {
  JobSearchCacheService,
  JobSearchQueryBuilder,
  JobSearchResultCache,
  JobSearchResultProcessor,
  JobSearchVectorService,
  PaginationFlowService,
} from './services/search';
import { DatabaseModule } from '../../database/database.module';
import { UserModule } from '../user/user.module';
import { AuthModule } from '../../auth/auth.module';
import { ReactAppModule } from '../react-app/react-app.module';
import { OpenAIModule } from '../../openai/openai.module';
import { GeminiModule } from '../../gemini/gemini.module';
import { ForYouController } from './controllers/for-you.controller';
import { SavedJobsController } from './controllers/saved-jobs.controller';
import { CoreModule } from '../core/core.module';
import { UserActionsModule } from '../user-actions/user-actions.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Job,
      JobLocation,
      JobTranslation,
      ForYou,
      JobApplication,
      SavedJob,
      JobMetadata,
    ]),
    CacheModule,
    TranslationModule,
    EmployerModule,
    PubSubModule,
    MunicipalityModule,
    RegionModule,
    PineconeModule,
    DatabaseModule,
    UserModule, // Import UserModule to make UserApiService available
    AuthModule, // Import AuthModule to make AuthService available
    OpenAIModule, // Import OpenAIModule to make OpenAIService available
    GeminiModule, // Import GeminiModule to make GeminiService available
    forwardRef(() => ReactAppModule), // Import ReactAppModule to make ReactAppService available
    CoreModule,
    UserActionsModule, // Import UserActionsModule to make UserActionTrackerService available
  ],
  controllers: [
    JobsController,
    JobApplicationController,
    ForYouController,
    SavedJobsController,
  ],
  providers: [
    JobMarketApiClient,
    JoblyApiClient,
    JoblyDataToEntityConverter,
    JobsImportService,
    JobsProcessingService,
    JobsExportService,
    JobsPineconeService,
    JobMetadataService,
    JobsImportRepository,
    JobLocationService,
    JobsSearchService,
    JobSearchQueryBuilder,
    JobSearchVectorService,
    JobSearchResultProcessor,
    JobSearchCacheService,
    JobSearchResultCache,
    PaginationFlowService,
    ForYouRepository,
    RecommendationEngineService,
    DifyService,
    JobApplicationService,
    SavedJobsService,
  ],
  exports: [
    JobsImportService,
    JobsImportRepository,
    JobsSearchService,
    DifyService,
    JobApplicationService,
    SavedJobsService,
    JoblyApiClient,
    JoblyDataToEntityConverter,
    JobMetadataService,
  ],
})
export class JobsModule {}
