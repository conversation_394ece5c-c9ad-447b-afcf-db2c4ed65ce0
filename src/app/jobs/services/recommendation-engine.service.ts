import {
  Injectable,
  Logger,
  UnprocessableEntityException,
} from '@nestjs/common';
import { JobsPineconeService } from './jobs-pinecone.service';
import { PineconeService } from '../../../pinecone/pinecone.service';
import { JobSearchParams } from './interfaces/job-search.interface';
import {
  Job,
  JobsImportRepository,
} from '../repository/jobs-import.repository';
import { formatJobsForResponse } from './helpers/job-data-to-entity.converter';
import { ClassifyJobDto } from '../../react-app/dto/matched-job.dto';
import { ConfigService } from '@nestjs/config';
import { DifyService } from './helpers/dify.service';
import { ClassifyJobRequestDto } from '../../react-app/dto/classify-job-request.dto';
import { GeminiService } from '../../../gemini/gemini.service';
import { ForYouRepository } from '../repository/for-you.repository';
import { Between, IsNull, Repository } from 'typeorm';
import { ForYou } from '../../../entities/for-you.entity';
import { InjectRepository } from '@nestjs/typeorm';
import { endOfDay, startOfDay, subDays } from 'date-fns';
import { Type } from '@google/genai';

@Injectable()
export class RecommendationEngineService {
  private readonly logger = new Logger(RecommendationEngineService.name);

  private readonly difyClassifyApiKey: string;

  constructor(
    private readonly pineconeService: JobsPineconeService,
    private readonly service: PineconeService,
    private readonly jobsRepository: JobsImportRepository,
    private readonly configService: ConfigService,
    private readonly difyService: DifyService,
    private readonly geminiService: GeminiService,
    private readonly forYouRepository: ForYouRepository,
    @InjectRepository(ForYou)
    private readonly forYouEntity: Repository<ForYou>,
  ) {
    this.difyClassifyApiKey = this.configService.get<string>(
      'DIFY_FILTER_MATCH_CLIENT_KEY',
    )!;
  }

  async getRecommendations(
    searchParams: JobSearchParams,
    userKeywords: string,
    userSkillContexts: string | string[],
  ) {
    this.logger.log(
      `Starting job recommendations with params: ${JSON.stringify({
        ...searchParams,
        userKeywords,
        userSkillContexts: Array.isArray(userSkillContexts)
          ? userSkillContexts.join(', ')
          : userSkillContexts,
      })}`,
    );

    const {
      page,
      limit = searchParams.limit || 20,
      offset,
      match_percentage_threshold,
      sort_by,
      sort_direction = 'desc',
      ...filters
    } = searchParams;

    // 1. INITIAL SEMANTIC SEARCH (using keywords)
    this.logger.log(
      `Performing initial search with keywords: "${userKeywords}"`,
    );
    const initialMatches = await this.initialSearch(userKeywords, filters); // Retrieve top 100 initial matches

    // If no initial matches, return empty results early
    if (initialMatches.length === 0) {
      this.logger.warn(
        'No initial matches found in Pinecone. Check your filters and try again.',
      );
      return {
        jobs: [],
        meta: {
          total: 0,
          page:
            offset !== undefined
              ? Math.floor(offset / (limit || 20)) + 1
              : page || 1,
          limit,
          has_next: false,
        },
      };
    }

    this.logger.log(`Found ${initialMatches.length} initial matches`);

    // 2. Prepare documents for reranking
    const documents = initialMatches.map((match, index) => {
      const doc = {
        id: match.id,
        // Use the CORRECT fields for reranking: skills + keywords (combined)
        combinedInfo: `${match!.skills || ''}`, // Ensure we don't have undefined
        initialScore: String(match.score || 0), // Store the initial search score with fallback
        title: match.title || 'Untitled Job', // Fallback title
        skills: match.skills || '', // Fallback empty string
      };

      if (index < 3) {
        // Log first 3 documents for debugging
        this.logger.debug(
          `Document ${index + 1}: ${JSON.stringify(doc, null, 2)}`,
        );
      }

      return doc;
    });

    // 3. RE-RANKING (using skill_contexts)

    // A. Concatenate skill contexts into single string
    const combinedUserSkillContexts = Array.isArray(userSkillContexts)
      ? userSkillContexts.join(' ')
      : userSkillContexts;

    //B. Rerank based on the combined user skill contexts.
    const rerankedResults = await this.service
      .getPineconeClient()
      .inference.rerank(
        'cohere-rerank-3.5',
        combinedUserSkillContexts, // Single string of all contexts.
        documents,
        {
          topN: limit, // Get top N for each skill
          returnDocuments: true,
          rankFields: ['title', 'skills'], // Rank using combined skills and keywords
        },
      );

    //C. Combine scores using alpha and beta
    const alpha = 0.3; // Weight for initial keyword similarity
    const beta = 0.7; // Weight for skill context similarity
    const jobResults = rerankedResults.data.map((match) => {
      const initialMatch = initialMatches.find(
        (m) => m.id === match.document!.id,
      );
      const initialScore = initialMatch ? initialMatch.score : 0; // Fallback to 0 if not found.

      const combinedScore = alpha * initialScore + beta * match.score;
      return {
        id: match.document!.id,
        score: combinedScore,
      };
    });

    // 4. SORT AND RETURN
    jobResults.sort((a, b) => b.score - a.score);

    // Extract just the IDs for fetching from the database
    const extIds = jobResults.map((result) => result.id);

    // Fetch full job details with relations
    const jobs = await this.jobsRepository.findJobsWithDetails(extIds);

    // Create a map of job IDs to scores for easier lookup
    const scoreMap = new Map(
      jobResults.map((result) => [result.id, result.score]),
    );

    // Convert offset/limit to page/limit if offset is provided
    const effectivePage =
      offset !== undefined ? Math.floor(offset / limit) + 1 : page || 1;

    // Get the Pinecone index
    const index = await this.pineconeService.getDefaultIndex();

    // Calculate pagination
    const startIndex = (effectivePage - 1) * limit;
    const endIndex = effectivePage * limit;
    const paginatedJobs = jobs.slice(startIndex, endIndex);

    // Format the enriched job data
    const formattedJobs = formatJobsForResponse(paginatedJobs);

    // Add ranking scores to the formatted jobs
    formattedJobs.forEach((job) => {
      job.rankingScore = scoreMap.get(job.id) || 0;
    });

    // Always use formattedJobs directly without filtering by superMatch
    const finalJobs = formattedJobs;

    this.logger.log(`Returning ${finalJobs.length} jobs for user`);

    return {
      jobs: finalJobs,
      meta: {
        total: jobResults.length,
        page: effectivePage,
        limit,
        has_next: endIndex < jobResults.length,
      },
    };
  }

  private async initialSearch(keywords: string, searchParams: JobSearchParams) {
    this.logger.log(
      `Recommending jobs with params: ${JSON.stringify(searchParams)}`,
    );

    // Extract filters and pagination from request
    const {
      smart_profile,
      smartProfile,
      user_id,
      languages,
      top20, // Here and above are added for compatibility with React app
      query,
      page,
      limit = searchParams.limit || 20,
      offset,
      sort_by,
      sort_direction = 'desc',
      super_match, // Extract and ignore super_match as it doesn't exist in Pinecone
      ...filters
    } = searchParams;

    // Convert offset/limit to page/limit if offset is provided
    const effectivePage =
      offset !== undefined ? Math.floor(offset / limit) + 1 : page || 1;

    // Get the Pinecone index
    const index = await this.pineconeService.getDefaultIndex();

    // Create filter object for Pinecone
    const pineconeFilters: Record<string, any> = {};

    // Add default filter to exclude expired jobs
    const currentTimestamp = Math.floor(Date.now() / 1000);
    pineconeFilters.expires_at = { $gt: currentTimestamp };

    // Add filters to the query
    Object.entries(filters).forEach(([key, value]) => {
      // Skip any fields that don't exist in Pinecone
      if (key === 'super_match') {
        this.logger.debug(
          `Skipping filter 'super_match' as it doesn't exist in Pinecone`,
        );
        return;
      }

      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          // Handle array values (e.g., skills)
          pineconeFilters[key] = { $in: value };
        } else {
          // Handle scalar values
          pineconeFilters[key] = { $eq: value };
        }
      }
    });

    this.logger.debug(
      `Final Pinecone filters: ${JSON.stringify(pineconeFilters)}`,
    );

    // Query Pinecone
    this.logger.log(
      `Querying Pinecone with filters: ${JSON.stringify(pineconeFilters)}`,
    );

    let queryVector;

    this.logger.warn(`Keyword: ${keywords}`);

    // If there's a text query, generate embeddings for it
    if (keywords && keywords.trim()) {
      try {
        this.logger.log(`Generating embeddings for text query: "${keywords}"`);
        const queryEmbeddings =
          await this.pineconeService.generateEmbeddingsV2(keywords);

        if (queryEmbeddings && queryEmbeddings.length > 0) {
          queryVector = queryEmbeddings;
          this.logger.debug(
            `Successfully generated embeddings for query "${keywords}"`,
          );
        } else {
          this.logger.warn(
            `Failed to generate valid embeddings for query "${keywords}", using fallback vector`,
          );
          // Use a small non-zero value to avoid bias in vector search
          queryVector = new Array(this.pineconeService.dimension).fill(0.1);
        }
      } catch (error) {
        this.logger.error(
          `Error generating embeddings for query "${keywords}": ${error.message}`,
          error.stack,
        );
        // Use a small non-zero value to avoid bias in vector search
        queryVector = new Array(this.pineconeService.dimension).fill(0.1);
      }
    } else {
      this.logger.log('No keywords provided, using fallback vector for search');
      // Use a small non-zero value to avoid bias in vector search
      queryVector = new Array(this.pineconeService.dimension).fill(0.1);
    }

    const queryResponse = await index.query({
      vector: queryVector,
      filter: pineconeFilters,
      topK: limit * 5, // Request more than we need in case some jobs are missing
      includeMetadata: true,
    });

    if (!queryResponse.matches || queryResponse.matches.length === 0) {
      return [];
    }

    const sortedMatches = queryResponse.matches;

    // Prepare documents for reranking
    const documents = sortedMatches.map((match) => ({
      id: match.id,
      skills: String(match.metadata!.skills), // Convert to string to ensure compatibility
      title: String(match.metadata!.title),
      score: Number(match.score!),
    }));

    /*      const aiFilteredMatches = await this.aiFilterMatches(
         documents.map((job) => {
           return {
             extId: job.id,
             resume_keywords: keywords,
             job_description_keywords: job.skills,
           } as ClassifyJobRequestDto;
         }),
       );
   
    return documents.filter((job) =>
         aiFilteredMatches.some((match) => match.extId === job.id),
       );*/

    return documents;
  }

  /**
   * Classifies a job match by comparing resume keywords and job description keywords
   * @param resume_keywords Keywords extracted from the user's resume/profile
   * @param job_description_keywords Keywords extracted from the job description
   * @returns Classification results with match level and skill comparisons
   */
  async classifyJobMatch(
    resume_keywords: string,
    job_description_keywords: string,
  ): Promise<ClassifyJobDto> {
    this.logger.log('Classifying job match with Dify API');

    try {
      // Call Dify API using the common method
      const rawClassification = await this.difyService.callDifyApi(
        '',
        {
          apiKey: this.difyClassifyApiKey,
          responseProperty: 'classification',
          errorMessage: 'Failed to classify job match',
          fallbackMessage: 'Could not classify the job match',
        },
        {
          resume_keywords,
          job_description_keywords,
        },
      );

      // Process the result
      let match: ClassifyJobDto['match'] = 'NO_MATCH';
      let mySkills: string[] = [];
      let missingSkills: string[] = [];

      // Try to extract data from the response
      try {
        // First item might contain JSON string with classification data
        const classification = rawClassification as ClassifyJobDto;

        // Try to parse as JSON if it's a string that looks like JSON
        match = classification.match;
        mySkills = classification.mySkills;
        missingSkills = classification.missingSkills;
      } catch (error) {
        this.logger.error('Error processing classification response:', error);
      }

      return {
        match,
        mySkills,
        missingSkills,
      };
    } catch (error) {
      this.logger.error(
        `Error classifying job match: ${error.message}`,
        error.stack,
      );
      // Return default response in case of error
      return {
        match: 'NO_MATCH',
        mySkills: [],
        missingSkills: [],
      };
    }
  }

  async aiFilterMatches(reqDto: ClassifyJobRequestDto[]): Promise<
    {
      extId: string;
      match: 'NO_MATCH' | 'SLIGHT' | 'GOOD' | 'STRONG';
    }[]
  > {
    this.logger.log('Filtering job matches with Dify API');

    try {
      const job_details = reqDto.map((job) => {
        return {
          extId: job.extId!,
          job_description_keywords: job.job_description_keywords!,
        };
      });

      // Call Dify API using the common method
      const rawClassification = await this.difyService.callDifyApi(
        '',
        {
          apiKey: this.difyClassifyApiKey,
          responseProperty: 'classification',
          errorMessage: 'Failed to classify job match',
          fallbackMessage: 'Could not classify the job match',
        },
        {
          job_details: JSON.stringify(job_details, null, 2),
          resume_keywords: reqDto[0].resume_keywords,
        },
      );

      // {result: {extId: string, match: 'NO_MATCH' | 'SLIGHT' | 'GOOD' | 'STRONG'}[]}

      // Try to extract data from the response
      try {
        // First item might contain JSON string with classification data
        const classification = rawClassification as Record<
          string,
          {
            extId: string;
            match: 'NO_MATCH' | 'SLIGHT' | 'GOOD' | 'STRONG';
          }[]
        >;

        const filter = classification.result.filter(
          (r) => r.match != 'NO_MATCH',
        );

        return filter;
      } catch (error) {
        this.logger.error('Error processing classification response:', error);

        const map = reqDto.map((r) => {
          return { extId: r.extId!, match: 'NO_MATCH' } as {
            extId: string;
            match: 'NO_MATCH' | 'SLIGHT' | 'GOOD' | 'STRONG';
          };
        });
        return map;
      }
    } catch (error) {
      this.logger.error(
        `Error classifying job match: ${error.message}`,
        error.stack,
      );
      // Return default response in case of error
      const map = reqDto.map((r) => {
        return { extId: r.extId!, match: 'NO_MATCH' } as {
          extId: string;
          match: 'NO_MATCH' | 'SLIGHT' | 'GOOD' | 'STRONG';
        };
      });
      return map;
    }
  }

  /**
   * Generates "For You" job recommendations for a user
   * @param userId The user ID
   * @param smartProfile The user's smart profile data
   * @returns List of generated ForYou recommendations
   */
  async generateForYou(
    userId: string,
    smartProfile: Record<string, any>,
  ): Promise<ForYou[]> {
    this.logger.log(`Generating ForYou recommendations for user: ${userId}`);

    // 1. Check if user has already generated recommendations today
    const hasGeneratedToday = await this.hasGeneratedForYouToday(userId);
    if (hasGeneratedToday) {
      this.logger.log(
        `User ${userId} has already generated ForYou recommendations today`,
      );
      throw new UnprocessableEntityException(
        'ForYou recommendations have already been generated today for this user',
      );
    }

    // 2. Get carried-over jobs from previous day (unviewed and not rated)
    const carriedOverJobs = await this.getCarriedOverJobs(userId);
    this.logger.log(
      `Found ${carriedOverJobs.length} carried-over jobs for user ${userId}`,
    );

    // 3. Calculate number of new recommendations needed
    const maxRecommendations = 10;
    const neededNewJobs = Math.max(
      0,
      maxRecommendations - carriedOverJobs.length,
    );

    // 4. If we need new jobs, extract valuable phrases from smart profile and find matching jobs
    let newRecommendations: ForYou[] = [];

    if (neededNewJobs > 0) {
      // Extract valuable phrases from user's profile
      const extractedPhrases = await this.extractValuablePhrases(smartProfile);

      // Find matching jobs based on extracted phrases
      const matchingJobs = await this.findMatchingJobs(
        userId,
        extractedPhrases,
        neededNewJobs,
      );

      // Create ForYou entities for the matching jobs
      newRecommendations = await this.createForYouEntities(
        userId,
        matchingJobs,
        extractedPhrases,
      );
    }

    // 5. Combine carried-over jobs with new recommendations
    const allRecommendations = [...carriedOverJobs, ...newRecommendations];

    this.logger.log(
      `Generated ${allRecommendations.length} total ForYou recommendations for user ${userId}`,
    );
    return allRecommendations;
  }

  /**
   * Checks if the user has already generated ForYou recommendations today
   * @param userId The user ID
   * @returns Boolean indicating whether ForYou recommendations have been generated today
   */
  private async hasGeneratedForYouToday(userId: string): Promise<boolean> {
    const today = new Date();
    const startOfToday = startOfDay(today);
    const endOfToday = endOfDay(today);

    const existingRecommendations = await this.forYouEntity.find({
      where: {
        userId,
        recommended_at: Between(startOfToday, endOfToday),
      },
    });

    return existingRecommendations.length > 0;
  }

  /**
   * Gets carried-over jobs from the previous day that were not viewed or rated
   * @param userId The user ID
   * @returns List of carried-over ForYou entities
   */
  private async getCarriedOverJobs(userId: string): Promise<ForYou[]> {
    const yesterday = subDays(new Date(), 1);
    const startOfYesterday = startOfDay(yesterday);
    const endOfYesterday = endOfDay(yesterday);

    // Find yesterday's recommendations that were not viewed and not rated
    const carriedOverJobs = await this.forYouEntity.find({
      where: {
        userId,
        recommended_at: Between(startOfYesterday, endOfYesterday),
        is_viewed: false,
        is_liked: IsNull(),
      },
    });

    // Update the recommended_at timestamp to today for carried-over jobs
    if (carriedOverJobs.length > 0) {
      const now = new Date();
      for (const job of carriedOverJobs) {
        job.recommended_at = now;
        await this.forYouEntity.save(job);
      }
    }

    return carriedOverJobs;
  }

  /**
   * Extracts valuable phrases from the user's smart profile using Gemini
   * @param smartProfile The user's smart profile
   * @returns Array of objects containing extracted phrases with reasons
   */
  private async extractValuablePhrases(
    smartProfile: Record<string, any>,
  ): Promise<Array<{ phrase: string; reason: string }>> {
    const systemInstruction = `
      **Role:** You are an expert AI assistant specialized in analyzing professional profiles (like Resumes or "Smart Profiles") to extract information optimized for a two-stage job matching process: semantic search followed by reranking.

**Objective:**
1.  Analyze the provided User Smart Profile.
2.  Extract the top 3-5 most significant phrases or short sentences that capture the user's core competencies, experiences, qualifications, key skills, and career aspirations in **natural, coherent language suitable for effective semantic job searching.** Avoid single, isolated keywords where a more descriptive phrase provides better semantic context.
3.  For each extracted phrase, generate a concise **reason** that explicitly justifies its importance by **linking it to specific job skills or relevant job titles.** This reason will be used to rerank initial job matches. Focus on how the phrase demonstrates fitness for roles requiring certain skills (e.g., "Python programming", "Client Relationship Management", "Volume Eyelash Extensions") or aligning with specific job titles (e.g., "Project Manager", "Lead Stylist", "Software Engineer").
4.  Order the extracted phrases by their perceived importance and relevance to the user's strongest qualifications and likely job targets.

**Input:** The User Smart Profile text will be provided below.

**Output Requirements:**

1.  **Strict JSON Format:** You MUST return ONLY a valid JSON object. Do not include any introductory text, explanations, apologies, or markdown formatting  outside of the JSON structure itself. Any extraneous text will break the downstream application.
2.  **JSON Schema:** The JSON object MUST be an array containing objects, adhering strictly to the following schema:

    [
      {
        "phrase": "string", // A natural language, coherent phrase or short sentence (typically 3-10 words) extracted from the profile, suitable for semantic search. E.g., "Managed cross-functional project teams", "Proficient in classic and volume eyelash extensions", "Developed scalable backend APIs using Node.js".
        "reason": "string"  // A concise justification (max 20 words) linking the phrase to specific job skills or titles for reranking. E.g., "Demonstrates Project Management skill.", "Highlights key skills for Eyelash Technician roles.", "Shows Node.js skill relevant for Backend Developer titles."
      }
      // ... additional objects
    ]

3.  **Extraction Criteria:**
    *   **Phrase Nature & Relevance:** Extract coherent phrases or short descriptive sentences capturing core competencies, experiences, or goals in natural language optimized for *semantic search*. Focus on terms representing professional skills, specific technologies/techniques, key responsibilities, quantifiable achievements, relevant qualifications, and stated goals.
    *   **Reason Focus (for Reranking):** The reason must explicitly connect the phrase to potential *job skills* (like those in job descriptions, e.g., "Hair Styling", "Data Analysis", "Agile Methodologies") or relevant *job titles*. Explain *how* the phrase signals qualification for these skills/titles.
    *   **Specificity:** Prefer specific, descriptive phrases over generic ones.
    *   **Impact & Strength:** Select terms highlighting significant experience, core strengths, or unique qualifications most likely sought by employers.
    *   **Quantity:** Extract between 5 and 10 phrase-reason pairs total.

4.  **Order of Importance:** The final JSON array MUST be sorted in descending order of perceived importance. The first object should represent the most crucial phrase/reason pair based on the user's strongest qualifications and relevance to target job skills/titles.

    `;

    try {
      const geminiResponse = await this.geminiService.predict(
        JSON.stringify(smartProfile),
        systemInstruction,
        {
          responseMimeType: 'application/json',
          responseSchema: {
            type: Type.ARRAY, // The top-level response is an array
            description:
              'An array of extracted keyword phrases objects, ordered by perceived importance for job matching.',
            items: {
              // Defines the schema for each object within the array
              type: Type.OBJECT,
              properties: {
                phrase: {
                  type: Type.STRING,
                  description:
                    'The specific keyword or short phrase extracted from the profile (typically 1-5 words). Represents a core skill, experience, qualification, etc.',
                  nullable: false, // Phrase should always be present
                },
                reason: {
                  type: Type.STRING,
                  description:
                    "A concise explanation (max 15 words) justifying why this phrase is important and relevant for semantic job searching based on the user's profile.",
                  nullable: false, // Reason should always be present
                },
              },
              required: ['phrase', 'reason'], // Both 'phrase' and 'reason' are mandatory fields in each object
            },
          },
        },
      );

      // Parse the JSON response
      const extractedPhrasesResponse = JSON.parse(geminiResponse);

      // Transform into the new array format if it's in the old format
      let extractedPhrases: Array<{ phrase: string; reason: string }>;

      if (Array.isArray(extractedPhrasesResponse)) {
        extractedPhrases = extractedPhrasesResponse;
      } else {
        // Convert old format (Record<string, string>) to new format (Array<{phrase, reason}>)
        extractedPhrases = Object.entries(extractedPhrasesResponse).map(
          ([reason, phrase]) => ({ phrase: phrase as string, reason }),
        );
      }

      this.logger.log(
        `Extracted ${extractedPhrases.length} valuable phrases from user profile`,
      );
      return extractedPhrases;
    } catch (error) {
      this.logger.error(
        `Error extracting valuable phrases: ${error.message}`,
        error.stack,
      );
      // Provide a fallback approach if Gemini fails
      return this.extractValuablePhrasesWithoutLLM(smartProfile);
    }
  }

  /**
   * Fallback method to extract valuable phrases without using LLM
   * @param profileData The user's profile data
   * @returns Array of objects containing extracted phrases with reasons
   */
  private extractValuablePhrasesWithoutLLM(
    profileData: any,
  ): Array<{ phrase: string; reason: string }> {
    const extractedPhrases: Array<{ phrase: string; reason: string }> = [];

    // Extract skills
    if (profileData.skills && profileData.skills.length > 0) {
      profileData.skills.slice(0, 5).forEach((skill: any) => {
        extractedPhrases.push({
          reason: `Core ${skill.type} skill`,
          phrase: skill.name,
        });
      });
    }

    // Extract occupation and interested occupations
    if (profileData.occupation) {
      extractedPhrases.push({
        reason: 'Current occupation',
        phrase: profileData.occupation.name,
      });
    }

    if (
      profileData.interestedOccupations &&
      profileData.interestedOccupations.length > 0
    ) {
      profileData.interestedOccupations
        .slice(0, 3)
        .forEach((occ: any, index: number) => {
          extractedPhrases.push({
            reason: `Interested occupation ${index + 1}`,
            phrase: occ.name,
          });
        });
    }

    // Extract work experience
    if (profileData.workExperiences && profileData.workExperiences.length > 0) {
      const latestExperience = profileData.workExperiences[0];
      extractedPhrases.push({
        reason: 'Latest job title',
        phrase: latestExperience.title,
      });

      if (
        latestExperience.responsibilities &&
        latestExperience.responsibilities.length > 0
      ) {
        latestExperience.responsibilities
          .slice(0, 2)
          .forEach((resp: string, index: number) => {
            extractedPhrases.push({
              reason: `Key responsibility ${index + 1}`,
              phrase: resp,
            });
          });
      }
    }

    return extractedPhrases;
  }

  /**
   * Finds matching jobs based on extracted phrases
   * @param userId The user ID
   * @param extractedPhrases The extracted phrases with reasons
   * @param limit The maximum number of jobs to find
   * @returns List of matching jobs
   */
  private async findMatchingJobs(
    userId: string,
    extractedPhrases: Array<{ phrase: string; reason: string }>,
    limit: number,
  ): Promise<Job[]> {
    // Combine all phrases into a single query text
    const queryText = extractedPhrases.map((item) => item.phrase).join(' - ');

    // Get skill contexts from the reasons
    const skillContexts = extractedPhrases
      .map((item) => item.reason)
      .join(', ');

    // Create search params
    const searchParams: JobSearchParams = {
      limit,
      // Add any additional filters as needed
    };

    // Use the existing recommendation engine to find matching jobs
    const matchingJobsResult = await this.getRecommendations(
      searchParams,
      queryText,
      skillContexts,
    );

    // Get existing ForYou job_ext_ids for this user (from any date) to avoid recommending the same job twice
    const existingForYouJobs = await this.forYouEntity.find({
      where: { userId },
      select: ['job_ext_id'],
    });

    const existingJobIds = new Set(
      existingForYouJobs.map((job) => job.job_ext_id),
    );

    // Filter out jobs that have already been recommended to this user
    const filteredJobs = matchingJobsResult.jobs.filter(
      (job) => !existingJobIds.has(job.id),
    );

    // Validate that all jobs have valid ext_id values
    const validJobs = filteredJobs.filter((job) => {
      const hasExtId = !!job.id;
      if (!hasExtId) {
        this.logger.warn(
          `Found job without valid ext_id: ${JSON.stringify(job)}`,
        );
      }
      return hasExtId;
    });

    this.logger.log(
      `Found ${validJobs.length} valid matching jobs out of ${filteredJobs.length} total matches`,
    );

    return validJobs.slice(0, limit);
  }

  /**
   * Creates ForYou entities for the matching jobs
   * @param userId The user ID
   * @param matchingJobs The list of matching jobs
   * @param extractedPhrases The extracted phrases with reasons
   * @returns List of ForYou entities
   */
  private async createForYouEntities(
    userId: string,
    matchingJobs: any[],
    extractedPhrases: Array<{ phrase: string; reason: string }>,
  ): Promise<ForYou[]> {
    const forYouEntities: ForYou[] = [];

    for (const job of matchingJobs) {
      // Skip jobs without a valid ext_id
      if (!job.id) {
        this.logger.warn(
          `Skipping job without valid ext_id: ${JSON.stringify(job)}`,
        );
        continue;
      }

      // Generate the 'why' reasons using the extracted phrases
      const whyReasons = await this.generateWhyReasons(job, extractedPhrases);

      const forYouEntity = new ForYou();
      forYouEntity.userId = userId;
      forYouEntity.job_ext_id = job.id;
      forYouEntity.recommended_at = new Date();
      forYouEntity.is_viewed = false;
      forYouEntity.is_liked = null as unknown as boolean; // This will make TypeScript happy while maintaining the nullable behavior
      forYouEntity.classification_data = extractedPhrases;
      forYouEntity.why = whyReasons.reasons;
      forYouEntity.score = whyReasons.score;

      // Save the entity to the database
      await this.forYouEntity.save(forYouEntity);
      forYouEntities.push(forYouEntity);
    }

    return forYouEntities;
  }

  /**
   * Generates reasons why a job was recommended to a user
   * @param job The job
   * @param extractedPhrases The extracted phrases with reasons
   * @returns Object with reasons and score
   */
  private async generateWhyReasons(
    job: any,
    extractedPhrases: Array<{ phrase: string; reason: string }>,
  ): Promise<{ reasons: string[]; score: number }> {
    try {
      // If processedJob is null, fetch the complete job with relations
      let completeJob = job;
      if (!job.processedJob) {
        this.logger.log(
          `ProcessedJob is null for job ${job.id}, fetching complete job data...`,
        );
        const fetchedJob = await this.jobsRepository.findByExtId(job.id);
        if (fetchedJob) {
          completeJob = fetchedJob;
        } else {
          this.logger.warn(
            `Could not fetch complete job data for job ${job.id}`,
          );
        }
      }

      // Use Gemini to generate reasons
      const systemInstruction = `
        **Role:** You are an expert AI assistant acting as a job recommendation explainer.

**Objective:** Given specific information about a job and a list of key phrases representing a user's profile (skills, experience, etc.), generate 1 to 3 concise, specific reasons explaining why this job is a good match for the user, along with a match score from 0-100.

**Inputs:**
1.  **Job Information:** Details about the specific job opportunity (e.g., title, key responsibilities, required skills).
2.  **User Profile Keywords/Phrases:** An array of strings representing the user's most relevant qualifications extracted from their profile.

**Output Requirements:**

1.  **Strict JSON Format:** You MUST return ONLY a valid JSON object containing an array of strings and a score. Do not include any introductory text, explanations, apologies, comments, or markdown formatting outside of the JSON structure itself.
2.  **JSON Schema:** The JSON object MUST adhere strictly to the following schema:

  {
    "reasons": {
      "type": "array",
      "items": {
        "type": "string",
        "description": "A single-sentence reason explaining the job match, starting with 'Because '."
      },
      "minItems": 1,
      "maxItems": 3
    },
    "score": {
      "type": "number",
      "minimum": 0,
      "maximum": 100,
      "description": "A score between 0-100 indicating how well the job matches the user's profile and preferences."
    }
  }

  *Example Output:* {"reasons": ["Because your experience in Python aligns with the job's requirement for backend development.", "Because the job description mentions project management, which is listed as one of your core skills."], "score": 85}
3.  **Reason Criteria:**
  *   **Content:** Each reason must directly link a specific aspect of the **Job Information** to one or more of the **User Profile Keywords/Phrases**.
  *   **Format:** Each reason MUST be a single sentence and MUST start exactly with "Because " (including the space).
  *   **Conciseness:** Aim for each reason to be under 100 characters, focusing on clarity and impact.
  *   **Specificity:** Avoid generic reasons. Be specific about the skill/experience match.
  *   **Quantity:** Generate between 1 and 3 distinct reasons.

4.  **Order of Importance:** The final JSON array MUST be sorted in descending order of perceived importance. The first object should represent the most crucial phrase/reason pair based on the user's strongest qualifications and relevance to target job skills/titles.

5.  **Score Criteria:**
  *   **Scale:** The score should be a number between 0-100.
  *   **Interpretation:** A higher score indicates a stronger match between the user's profile and the job requirements.
  *   **Factors:** Consider skill overlap, experience level, qualification matches, and potential fit.
  *   **Honesty:** The score should honestly reflect the match quality - do not inflate scores for weak matches.

  `;

      const jobData = {
        title:
          completeJob.processedJob?.market_salary?.job_role ||
          completeJob.title,
        ideal_candidate_traits:
          completeJob.processedJob?.ideal_candidate_traits || [],
        keywords: completeJob.processedJob?.keywords || [],
        expected_outcomes: completeJob.processedJob?.expected_outcomes || [],
        skills: completeJob.processedJob?.skills || [],
        userPhrases: extractedPhrases,
      };

      const responseSchema = {
        type: Type.OBJECT,
        properties: {
          reasons: {
            type: Type.ARRAY,
            description:
              "An array containing 1 to 3 strings, each explaining why the job matches the user's profile, starting with 'Because '.",
            minItems: 1,
            maxItems: 3,
            items: {
              type: Type.STRING,
              description:
                "A single-sentence reason starting with 'Because ' explaining the job match, ideally under 100 characters.",
              nullable: false,
            },
          },
          score: {
            type: Type.NUMBER,
            description:
              "A score between 0-100 indicating how well the job matches the user's profile and preferences.",
            minimum: 0,
            maximum: 100,
          },
        },
        required: ['reasons', 'score'],
      };

      const geminiResponse = await this.geminiService.predict(
        JSON.stringify(jobData),
        systemInstruction,
        {
          responseMimeType: 'application/json',
          responseSchema,
        },
      );

      // Parse the JSON response
      try {
        const parsedResponse = JSON.parse(geminiResponse);

        if (
          parsedResponse &&
          parsedResponse.reasons &&
          Array.isArray(parsedResponse.reasons) &&
          typeof parsedResponse.score === 'number'
        ) {
          return {
            reasons: parsedResponse.reasons.slice(0, 5),
            score: Math.max(0, Math.min(100, Math.round(parsedResponse.score))),
          };
        } else {
          const genericResult = this.generateGenericReasons(
            job,
            extractedPhrases,
          );
          return {
            reasons: genericResult,
            score: 50, // Default score for generic reasons
          };
        }
      } catch (e) {
        // If parsing fails, return the raw text (it might already be formatted correctly)
        const textReasons = geminiResponse
          .split('\n')
          .filter((line) => line.trim().startsWith('Because'))
          .map((line) => line.trim())
          .slice(0, 5);

        const genericResult =
          textReasons.length > 0
            ? textReasons
            : this.generateGenericReasons(job, extractedPhrases);

        return {
          reasons: genericResult,
          score: 50, // Default score when parsing fails
        };
      }
    } catch (error) {
      this.logger.error(`Error generating why reasons: ${error.message}`);
      return {
        reasons: this.generateGenericReasons(job, extractedPhrases),
        score: 50, // Default score on error
      };
    }
  }

  /**
   * Generates generic reasons why a job was recommended to a user
   * @param job The job
   * @param extractedPhrases The extracted phrases with reasons
   * @returns List of generic reasons
   */
  private generateGenericReasons(
    job: any,
    extractedPhrases: Array<{ phrase: string; reason: string }>,
  ): string[] {
    const reasons: string[] = [];

    // Generate generic reasons based on job title
    reasons.push(
      `Because your profile indicates you may be interested in a ${job.title} position.`,
    );

    // Add reasons based on extracted phrases
    const phraseEntries = extractedPhrases;
    for (let i = 0; i < Math.min(4, phraseEntries.length); i++) {
      const { reason, phrase } = phraseEntries[i];
      reasons.push(
        `Because your ${reason.toLowerCase()} "${phrase}" matches this job's requirements.`,
      );
    }

    return reasons.slice(0, 5);
  }
}
