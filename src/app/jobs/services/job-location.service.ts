import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import { JobLocation } from '../../../entities/job-location.entity';
import { MunicipalityRepository } from '../../municipality/repository/municipality.repository';
import { RegionRepository } from '../../region/repository/region.repository';
import { Municipality } from '../../../entities/municipality.entity';
import { Region } from '../../../entities/region.entity';
import { Job } from '../../../entities/job.entity';

@Injectable()
export class JobLocationService {
  private readonly logger = new Logger(JobLocationService.name);

  constructor(
    @InjectRepository(JobLocation)
    private readonly jobLocationRepository: Repository<JobLocation>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly municipalityRepository: MunicipalityRepository,
    private readonly regionRepository: RegionRepository,
  ) {}

  async createOrUpdate(
    jobLocationData: Partial<JobLocation> & {
      _municipalityCodes?: string[];
      _regionCodes?: string[];
    },
    transactionManager?: EntityManager,
  ): Promise<JobLocation> {
    const startTime = Date.now();
    this.logger.debug(
      `Starting createOrUpdate for job_id: ${jobLocationData.job_id}`,
    );

    try {
      // Use transaction manager if provided, otherwise use the repository
      const jobRepo = transactionManager
        ? transactionManager.getRepository(Job)
        : this.jobRepository;
      const jobLocationRepo = transactionManager
        ? transactionManager.getRepository(JobLocation)
        : this.jobLocationRepository;

      // Check if the job exists before trying to create a location for it
      if (jobLocationData.job_id) {
        this.logger.debug(
          `Checking if job ${jobLocationData.job_id} exists...`,
        );
        const jobCheckStart = Date.now();
        const jobExists = await jobRepo.findOne({
          where: { ext_id: jobLocationData.job_id },
        });
        this.logger.debug(
          `Job check completed in ${Date.now() - jobCheckStart}ms`,
        );

        if (!jobExists) {
          throw new NotFoundException(
            `Cannot create job location: Job with ext_id "${jobLocationData.job_id}" does not exist.`,
          );
        }
      }

      // Extract the temporary properties
      const municipalityCodes = jobLocationData._municipalityCodes || [];
      const regionCodes = jobLocationData._regionCodes || [];
      this.logger.debug(
        `Processing ${municipalityCodes.length} municipality codes and ${regionCodes.length} region codes`,
      );

      // Remove temporary properties
      delete jobLocationData._municipalityCodes;
      delete jobLocationData._regionCodes;

      // First, check if a job location already exists for this job ID
      this.logger.debug(
        `Checking if job location exists for job_id: ${jobLocationData.job_id}`,
      );
      const locationCheckStart = Date.now();
      let jobLocation = await jobLocationRepo.findOne({
        where: { job_id: jobLocationData.job_id },
      });
      this.logger.debug(
        `Job location check completed in ${Date.now() - locationCheckStart}ms`,
      );

      if (jobLocation) {
        this.logger.debug(
          `Updating existing job location for job_id: ${jobLocationData.job_id}`,
        );
        // Update existing entity with new data
        jobLocationRepo.merge(jobLocation, jobLocationData);

        // Get the existing job location ID
        const jobLocationId = jobLocation.id;

        // Explicitly remove existing relationships from the database
        if (jobLocationId) {
          this.logger.debug(
            `Removing existing relationships for job location ID: ${jobLocationId}`,
          );
          const relationshipRemovalStart = Date.now();
          await jobLocationRepo.manager.query(
            `DELETE FROM job_location_municipalities WHERE job_location_id = $1`,
            [jobLocationId],
          );

          await jobLocationRepo.manager.query(
            `DELETE FROM job_location_regions WHERE job_location_id = $1`,
            [jobLocationId],
          );
          this.logger.debug(
            `Relationship removal completed in ${Date.now() - relationshipRemovalStart}ms`,
          );
        }

        // Reset the relationships in memory
        jobLocation.municipalities = [];
        jobLocation.regions = [];
      } else {
        this.logger.debug(
          `Creating new job location for job_id: ${jobLocationData.job_id}`,
        );
        // Create a new entity if none exists
        jobLocation = jobLocationRepo.create(jobLocationData);

        // Save to get an ID
        const saveStart = Date.now();
        jobLocation = await jobLocationRepo.save(jobLocation);
        this.logger.debug(
          `Initial save completed in ${Date.now() - saveStart}ms`,
        );
      }

      // Fetch municipalities and regions by their codes
      this.logger.debug(
        `Starting to process ${municipalityCodes.length} municipality codes`,
      );
      const municipalities: Municipality[] = [];
      const addedMunicipalityCodes = new Set<string>(); // Track codes we've already added

      if (municipalityCodes.length > 0) {
        const municipalityProcessingStart = Date.now();
        for (const code of municipalityCodes) {
          // Skip if we've already added this municipality
          if (addedMunicipalityCodes.has(code)) {
            continue;
          }

          const municipalityStart = Date.now();
          let municipality: Municipality | null;
          if (transactionManager) {
            municipality = await transactionManager
              .getRepository(Municipality)
              .findOne({ where: { code } });
          } else {
            municipality = await this.municipalityRepository.findByCode(code);
          }
          this.logger.debug(
            `Municipality lookup for code ${code} took ${Date.now() - municipalityStart}ms`,
          );

          if (municipality) {
            municipalities.push(municipality);
            addedMunicipalityCodes.add(code);
          }
        }
        this.logger.debug(
          `All municipality processing completed in ${Date.now() - municipalityProcessingStart}ms`,
        );
      }

      this.logger.debug(
        `Starting to process ${regionCodes.length} region codes`,
      );
      const regions: Region[] = [];
      const addedRegionCodes = new Set<string>(); // Track codes we've already added

      if (regionCodes.length > 0) {
        const regionProcessingStart = Date.now();
        for (const code of regionCodes) {
          // Skip if we've already added this region
          if (addedRegionCodes.has(code)) {
            continue;
          }

          const regionStart = Date.now();
          let region: Region | null;
          if (transactionManager) {
            region = await transactionManager
              .getRepository(Region)
              .findOne({ where: { code } });
          } else {
            region = await this.regionRepository.findByCode(code);
          }
          this.logger.debug(
            `Region lookup for code ${code} took ${Date.now() - regionStart}ms`,
          );

          if (region) {
            regions.push(region);
            addedRegionCodes.add(code);
          }
        }
        this.logger.debug(
          `All region processing completed in ${Date.now() - regionProcessingStart}ms`,
        );
      }

      // Set the relationships
      jobLocation.municipalities = municipalities;
      jobLocation.regions = regions;

      // Save again with the relationships
      this.logger.debug(
        `Saving job location with ${municipalities.length} municipalities and ${regions.length} regions`,
      );
      const finalSaveStart = Date.now();
      const result = await jobLocationRepo.save(jobLocation);
      this.logger.debug(
        `Final save completed in ${Date.now() - finalSaveStart}ms`,
      );

      this.logger.debug(
        `createOrUpdate completed in ${Date.now() - startTime}ms for job_id: ${jobLocationData.job_id}`,
      );
      return result;
    } catch (error) {
      this.logger.error(
        `Error in createOrUpdate for job_id ${jobLocationData.job_id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async findByJobId(jobId: string): Promise<JobLocation | null> {
    return this.jobLocationRepository.findOne({
      where: { job_id: jobId },
      relations: ['municipalities', 'regions'],
    });
  }

  async getAll(): Promise<JobLocation[]> {
    return this.jobLocationRepository.find({
      relations: ['municipalities', 'regions'],
    });
  }
}
