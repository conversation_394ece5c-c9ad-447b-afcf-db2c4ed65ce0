import axios from 'axios';
import { ConfigService } from '@nestjs/config';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class DifyService {
  private readonly logger = new Logger(DifyService.name);

  private difyApiUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.difyApiUrl = this.configService.get<string>('WORKFLOW_API_URL')!;
  }

  /**
   * Makes a call to the Dify API
   * @param user_profile Formatted profile data
   * @param options Configuration options for the API call
   * @returns Array of results from Dify
   */
  async callDifyApi(
    user_profile: string,
    options: {
      apiKey: string;
      responseProperty: string;
      errorMessage: string;
      fallbackMessage: string;
    },
    inputs?: Record<string, any>,
    isWorkflow = false,
    userId?: string,
  ): Promise<string[] | Record<string, any>> {
    try {
      // Security concern: In a production environment, this would use proper
      // authentication and secure API key handling
      let url = `${this.difyApiUrl}/completion-messages`;

      if (isWorkflow) {
        url = `${this.difyApiUrl}/workflows/run`;
      }

      const response = await axios.post(
        url,
        {
          inputs: { user_profile: user_profile, ...inputs },
          response_mode: 'blocking',
          user: userId || 'user-service', // Can be anonymized or use user ID
        },
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer ${options.apiKey}`,
          },
        },
      );

      // Parse Dify response
      const difyResponse = response.data;

      this.logger.log(
        `Dify response: ${JSON.stringify(difyResponse, null, 2)}`,
      );

      // Extract data from response
      let results: string[] = [];
      try {
        if (!isWorkflow) {
          if (difyResponse.answer) {
            const cleaned = this.cleanupText(difyResponse.answer);
            // Try to parse as JSON
            try {
              const parsedResponse = JSON.parse(
                cleaned.replaceAll('```', '').replaceAll('json', ''),
              );
              if (inputs) {
                return parsedResponse;
              }
              results = parsedResponse[options.responseProperty] || [];
            } catch {
              // If not JSON, try to parse text response
              // Split by numbered points or new lines
              results = cleaned
                .split(/\d+\.|\n/)
                .map((line) => line.trim())
                .filter((line) => line.length > 0);
            }
          }
        }
        //Workflow
        if (isWorkflow) {
          if (difyResponse.data.outputs) {
            if (options.responseProperty == 'cv') {
              return difyResponse.data.outputs[options.responseProperty];
            }
            const cleaned = this.cleanupText(
              difyResponse.data.outputs[options.responseProperty],
            );
            // Try to parse as JSON
            try {
              const parsedResponse = JSON.parse(
                cleaned.replaceAll('```', '').replaceAll('json', ''),
              );
              results = parsedResponse;
            } catch {
              // If not JSON, try to parse text response
              // Split by numbered points or new lines
              results = cleaned
                .split(/\d+\.|\n/)
                .map((line) => line.trim())
                .filter((line) => line.length > 0);
            }

            return results;
          }
        }
      } catch (error) {
        this.logger.warn(
          `Error parsing Dify response for ${options.responseProperty}:`,
          error,
        );
        results = [options.fallbackMessage];
      }

      // Return extracted results
      return results;
    } catch (error) {
      this.logger.error(
        `Error calling Dify API for ${options.responseProperty}:`,
        error,
      );
      throw new Error(options.errorMessage);
    }
  }

  /**
   * Cleans up text by removing <details> tags, <think> tags, and JSON code block formatting
   * @param text Text to clean
   * @returns Cleaned text
   */
  cleanupText(text: string) {
    // First, try to extract JSON from code blocks if they exist
    const jsonBlockRegex = /```\s*json\s*([\s\S]*?)```/;
    const jsonBlockMatch = text.match(jsonBlockRegex);

    // If we found a JSON code block and it has content, return that content
    if (jsonBlockMatch && jsonBlockMatch[1] && jsonBlockMatch[1].trim()) {
      return jsonBlockMatch[1].trim();
    }

    // Otherwise, proceed with regular cleanup
    // Remove <details> tags and everything in between
    let withoutDetails = text.replace(/<details[\s\S]*?<\/details>/g, '');
    withoutDetails = withoutDetails.replace(/<think[\s\S]*?<\/think>/g, '');

    // Remove JSON code block formatting if present
    const withoutJsonFormatting = withoutDetails.replace(
      /```\s*json\s*([\s\S]*?)```/g,
      '$1',
    );

    // Trim any extra whitespace
    return withoutJsonFormatting.trim();
  }
}
