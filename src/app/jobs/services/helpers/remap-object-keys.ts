export function remapObjectKeys<E>(
  obj: any,
  dict: Record<string, string>,
): E {
  if (Array.isArray(obj)) {
    return obj.map(item => remapObjectKeys(item, dict)) as any
  } else if (typeof obj === 'object' && obj !== null) {
    return Object.keys(obj).reduce((acc, key) => {
      const translatedKey = dict[key] || key
      acc[translatedKey] = remapObjectKeys(obj[key], dict)
      return acc
    }, {} as any)
  }
  return obj
}