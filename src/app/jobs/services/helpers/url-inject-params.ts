export function injectParamsToURL(
  url: URL,
  params: Record<string, string | string[] | number | number[] | boolean>
): URL {
  if (!params) return url

  const newURL = new URL(url.toString())
  Object.entries(params).forEach(([key, value]) => {
    if(Array.isArray(value)){
      value.forEach((e) => { newURL.searchParams.append(`${key}[]`, String(e)) })
      return
    }

    newURL.searchParams.set(key, value?.toString())
  })

  return newURL
}