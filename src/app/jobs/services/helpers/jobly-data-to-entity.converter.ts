import { Injectable, Logger } from '@nestjs/common';
import { Job } from '../../../../entities/job.entity';
import { JobLocation } from '../../../../entities/job-location.entity';
import { JobTranslation } from '../../../../entities/job-translation.entity';
import { UpsertData } from '../../repository/interfaces/upsert-data.interface';
import { JoblyJob } from '../interfaces/jobly-job.interface';
import {
  DEFAULT_CONTINUITY,
  DEFAULT_FLEXIBILITY,
  DEFAULT_WORKING_TIME,
  joblyEmploymentTypeToFlexibility,
  joblyEmploymentTypeToJobFields,
} from '../constants/jobly-mappings.constants';
import { MunicipalityRepository } from '../../../municipality/repository/municipality.repository';
import { RegionRepository } from '../../../region/repository/region.repository';

/**
 * Converts Jobly job data to the format required for database upserting
 */
@Injectable()
export class JoblyDataToEntityConverter {
  private readonly logger = new Logger(JoblyDataToEntityConverter.name);

  constructor(
    private readonly municipalityRepository: MunicipalityRepository,
    private readonly regionRepository: RegionRepository,
  ) {}

  /**
   * Converts Jobly job data to the format required for database upserting
   * @param joblyJobs Array of Jobly job objects
   * @param employerMap Map of employer names to employer IDs
   * @returns Object containing arrays of Job, JobLocation, and JobTranslation objects for upserting
   */
  async convertJoblyUpsertInput(joblyJobs: JoblyJob[], employerMap: Map<string, number>): Promise<UpsertData> {
    this.logger.log(`Converting ${joblyJobs.length} Jobly jobs for upsert`);
    
    const jobData: Partial<Job>[] = [];
    const jobLocations: Partial<JobLocation>[] = [];
    const jobTranslations: Partial<JobTranslation>[] = [];
    
    // Process each Jobly job
    for (const joblyItem of joblyJobs) {
      try {
        // Convert job data
        const job = this.convertToJobEntity(joblyItem, employerMap);
        jobData.push(job);
        
        // Convert job location data
        const jobLocation = await this.convertToJobLocationEntity(joblyItem);
        jobLocations.push(jobLocation);
        
        // Convert job translation data (Finnish original and English translation)
        const jobTranslationEntries = this.createJobTranslations(joblyItem);
        jobTranslations.push(...jobTranslationEntries);
      } catch (error) {
        this.logger.error(`Error converting Jobly job ID ${joblyItem.id}: ${error.message}`, error.stack);
        // Continue with the next job instead of failing the entire batch
      }
    }
    
    this.logger.log(`Converted ${jobData.length} jobs, ${jobLocations.length} locations, and ${jobTranslations.length} translations`);
    
    return {
      jobData,
      jobLocations,
      jobTranslations
    };
  }
  
  /**
   * Converts a Jobly job to a Job entity
   * @param joblyItem The Jobly job to convert
   * @param employerMap Map of employer names to employer IDs
   * @returns Partial Job entity
   */
  private convertToJobEntity(joblyItem: JoblyJob, employerMap: Map<string, number>): Partial<Job> {
    // Map working_time and continuity from employment_type
    const { working_time, continuity, workplace_flexibility } = this.mapEmploymentType(joblyItem.employment_type);
    
    // Calculate expiry date (publish_date + 30 days)
    const published_date = new Date(joblyItem.publish_date);
    const expires_at = new Date(published_date);
    expires_at.setDate(expires_at.getDate() + 30); // Default expiry is 30 days after publish date
    
    // Create the Job entity
    return {
      ext_id: joblyItem.id.toString(),
      employer_name: joblyItem.company,
      employer_id: employerMap.get(joblyItem.company) || undefined,
      languages: ['en'], // We only store English for Jobly jobs
      working_time,
      continuity,
      workplace_flexibility,
      expires_at,
      title: (joblyItem as any).title_en || joblyItem.title, // Use English translation if available
      description: (joblyItem as any).description_en || joblyItem.description, // Use English translation if available
      application_url: joblyItem.url,
      source: 'Jobly',
      published_date,
      last_import_date: new Date(),
    };
  }
  
  /**
   * Maps Jobly employment type array to our internal fields
   * @param employmentTypes Array of employment type strings from Jobly
   * @returns Object containing working_time, continuity, and workplace_flexibility values
   */
  private mapEmploymentType(employmentTypes: string[]): { 
    working_time: string; 
    continuity: string; 
    workplace_flexibility?: string 
  } {
    let working_time = DEFAULT_WORKING_TIME;
    let continuity = DEFAULT_CONTINUITY;
    let workplace_flexibility: string | undefined;
    
    // Process each employment type
    for (const type of employmentTypes) {
      // Check for working_time and continuity mappings
      if (joblyEmploymentTypeToJobFields[type]) {
        const mapping = joblyEmploymentTypeToJobFields[type];
        if (mapping.working_time) {
          working_time = mapping.working_time;
        }
        if (mapping.continuity) {
          continuity = mapping.continuity;
        }
      }
      
      // Check for workplace flexibility mappings
      if (joblyEmploymentTypeToFlexibility[type]) {
        workplace_flexibility = joblyEmploymentTypeToFlexibility[type];
      }
    }
    
    return { working_time, continuity, workplace_flexibility };
  }
  
  /**
   * Creates job translation entities for both Finnish (original) and English (translated)
   * @param joblyItem The Jobly job data
   * @returns Array of Partial JobTranslation entities
   */
  private createJobTranslations(joblyItem: JoblyJob): Partial<JobTranslation>[] {
    const translations: Partial<JobTranslation>[] = [];
    
    // Add Finnish (original) translation
    translations.push({
      job_id: joblyItem.id.toString(),
      language: 'fi',
      title: joblyItem.title,
      description: joblyItem.description,
    });
    
    // Add English translation if available
    if ((joblyItem as any).title_en || (joblyItem as any).description_en) {
      translations.push({
        job_id: joblyItem.id.toString(),
        language: 'en',
        title: (joblyItem as any).title_en || joblyItem.title,
        description: (joblyItem as any).description_en || joblyItem.description,
      });
    }
    
    return translations;
  }
  
  /**
   * Converts a Jobly job to a JobLocation entity with municipality and region lookups
   * @param joblyItem The Jobly job to convert
   * @returns Partial JobLocation entity
   */
  private async convertToJobLocationEntity(joblyItem: JoblyJob): Promise<Partial<JobLocation>> {
    // Find municipality by name
    let municipalityCode: string | null = null;
    try {
      const municipality = await this.municipalityRepository.findByName(joblyItem.city);
      if (municipality) {
        municipalityCode = municipality.code;
        this.logger.debug(`Found municipality code ${municipalityCode} for ${joblyItem.city}`);
      } else {
        this.logger.debug(`No municipality found for ${joblyItem.city}`);
      }
    } catch (error) {
      this.logger.warn(`Error finding municipality for ${joblyItem.city}: ${error.message}`);
    }
    
    // Find region by name
    let regionCode: string | null = null;
    try {
      const region = await this.regionRepository.findByName(joblyItem.region);
      if (region) {
        regionCode = region.code;
        this.logger.debug(`Found region code ${regionCode} for ${joblyItem.region}`);
      } else {
        this.logger.debug(`No region found for ${joblyItem.region}`);
      }
    } catch (error) {
      this.logger.warn(`Error finding region for ${joblyItem.region}: ${error.message}`);
    }
    
    // Determine if the job is flexible/remote based on employment types
    const isFlexible = this.isFlexibleWorkplace(joblyItem.employment_type);
    
    // Create the JobLocation entity
    const jobLocation: Partial<JobLocation> = {
      job_id: joblyItem.id.toString(),
      flexible: isFlexible,
      country: [joblyItem.country],
    };
    
    // Add internal fields for municipality and region codes
    // These will be used by the JobLocationService to connect to the actual entities
    (jobLocation as any)._municipalityCodes = municipalityCode ? [municipalityCode] : [];
    (jobLocation as any)._regionCodes = regionCode ? [regionCode] : [];
    
    return jobLocation;
  }
  
  /**
   * Determines if a job is flexible/remote based on employment types
   * @param employmentTypes Array of employment type strings from Jobly
   * @returns true if job is flexible/remote, false otherwise
   */
  private isFlexibleWorkplace(employmentTypes: string[]): boolean {
    for (const type of employmentTypes) {
      if (type === 'Etätyö' || type === 'Etätyömahdollisuus') {
        return true;
      }
    }
    return DEFAULT_FLEXIBILITY;
  }
}