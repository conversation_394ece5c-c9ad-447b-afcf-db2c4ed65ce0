import { JobPosting } from '../../../jobs/repository/interfaces/job-data.interface';

import { Job } from '../../../../entities/job.entity';
import { JobLocation } from '../../../../entities/job-location.entity';
import { JobTranslation } from '../../../../entities/job-translation.entity';
import { normalizeJobLanguages } from '../utils/language-utils';

export function convertUpsertInput(
  data: JobPosting[],
  employerMap: Map<string, number>,
) {
  const jobData: Partial<Job>[] = data.map((item) => {
    // Find English title and description for the main Job entity
    const engTitle = item.basicInformation.jobTitle.find(
      (title) => title.languageCode.toLowerCase() === 'en',
    );
    const engDesc = item.basicInformation.jobDescription.find(
      (desc) => desc.languageCode.toLowerCase() === 'en',
    );

    return {
      ext_id: item.jobID,
      employer_name: item.employerName.at(-1)?.value,
      languages: item.jobLanguages,
      working_time: item.basicInformation.workingTime,
      continuity: item.basicInformation.jobContinuity,
      expires_at: item.application.applicationDeadline
        ? new Date(item.application.applicationDeadline)
        : undefined,
      employer_id: employerMap.get(item.employerBusinessID),
      // Add the new fields here
      working_hours: item.basicInformation.employmentRelation?.type,
      workplace_flexibility: item.location.flexibleLocation ? '01' : '02', // Remote: '01', OnSite: '02'
      driver_license_codes:
        item.competenceRequirements.cardsAndPermits?.permitCodes || [],
      salary_type: item.basicInformation.salaryBasis,
      work_begins: item.basicInformation.jobStarts,
      shift_type:
        Array.isArray(item.basicInformation.working?.shiftWork) &&
        item.basicInformation.working.shiftWork.length > 0
          ? item.basicInformation.working.shiftWork[0]
          : null,
      part_time_hours:
        item.basicInformation.employmentRelation?.type === '02' // PartTime
          ? item.basicInformation.working?.workingHours?.length > 0
            ? item.basicInformation.working.workingHours[0]
            : null
          : null,
      title: engTitle?.value || item.basicInformation.jobTitle[0]?.value,
      description:
        engDesc?.value || item.basicInformation.jobDescription[0]?.value,
      application_url: item.application.applicationUrls?.[0] || undefined,
      source: 'Tyomarkkinatori',
      // These fields might be undefined at import time, to be filled during processing
      salary_lower_bound: undefined,
      salary_upper_bound: undefined,
      expired: false,
    };
  });

  // For locations, we no longer map region and municipality directly
  // They will be handled separately in a job locations service
  const jobLocations: Partial<JobLocation>[] = data.map((item) => ({
    job_id: item.jobID,
    flexible: item.location.flexibleLocation,
    country: item.location.country,
    // We store the raw data temporarily to process relationships in the service
    _regionCodes: item.location.region,
    _municipalityCodes: item.location.municipality,
  }));

  const jobTranslations: Partial<JobTranslation>[] = data.flatMap((item) =>
    item.basicInformation.jobTitle.map((title) => ({
      job_id: item.jobID,
      language: title.languageCode,
      title: title.value,
      description:
        item.basicInformation.jobDescription.find(
          (desc) => desc.languageCode === title.languageCode,
        )?.value ?? undefined,
    })),
  );

  return { jobData, jobLocations, jobTranslations } as const;
}

/**
 * Format job entities into the enriched response format
 * @param jobs Array of job entities with relationships
 * @returns Formatted job data for frontend display
 */
export function formatJobsForResponse(jobs: Job[]): any[] {
  // Get current time for time-based badges
  const now = new Date();
  const oneWeekAgo = new Date(now);
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  const oneWeekFromNow = new Date(now);
  oneWeekFromNow.setDate(oneWeekFromNow.getDate() + 7);

  //
  return jobs.map((job) => {
    // Get processed job data if available
    const processedJob = job.processedJob || {};

    // Get company information if available
    const employer = job.employer || {
      name: job.employer_name || 'Unknown Employer',
    };

    // Format location information
    const location = job.location || {};
    const municipality =
      location.municipalities && location.municipalities.length > 0
        ? location.municipalities[0]
        : null;
    const region =
      location.regions && location.regions.length > 0
        ? location.regions[0]
        : null;

    // Check if the job is new or closing soon
    const publishedDate = job.published_date || job.createdAt;
    const isNew = publishedDate && new Date(publishedDate) > oneWeekAgo;
    const closingSoon =
      job.expires_at &&
      new Date(job.expires_at) < oneWeekFromNow &&
      !job.expired;

    // Check if salary information is available
    const hasSalary = !!processedJob.salary;

    // Debug log for skill_contexts
    /*    this.logger.debug(
      `Job ${job.ext_id} skill_contexts: ${JSON.stringify(processedJob.skill_contexts)}`,
    );*/

    return {
      id: job.ext_id,
      title: job.title,
      internship: processedJob.internship,
      employer: {
        id: employer.id,
        name: employer.name,
      },
      location: {
        city: municipality?.classificationName || 'Unknown City',
        region: region?.classificationName || 'Unknown Region',
        country: location.country?.[0] || 'Finland',
        isRemote: location.flexible || false,
      },
      employment: {
        working_time: job.working_time,
        continuity: job.continuity,
        working_hours: job.working_hours,
      },
      timing: {
        published_date: publishedDate?.toISOString(),
        expires_at: job.expires_at?.toISOString(),
        is_new: isNew,
        closing_soon: closingSoon,
      },
      salary: {
        lower_bound: processedJob.salary?.salary_lower_bound,
        upper_bound: processedJob.salary?.salary_upper_bound,
        has_salary: hasSalary,
      },
      application: job.application_url
        ? typeof job.application_url === 'string' &&
          job.application_url.startsWith('{')
          ? JSON.parse(job.application_url)?.value || null
          : job.application_url
        : null,
      summary: {
        elevator_pitch: processedJob.elevator_pitch,
        short_description:
          processedJob.summary || job.description?.substring(0, 150),
        primary_tasks: processedJob.primary_tasks,
      },
      skills: processedJob.skills || [],
      // Ensure skill_contexts is explicitly populated, never undefined
      skill_contexts: Array.isArray(processedJob.skill_contexts)
        ? processedJob.skill_contexts
        : [],
      languages: normalizeJobLanguages(job),
      industry: processedJob.industry?.name,
      occupation: processedJob.occupation?.preferredLabel,
      seniority: processedJob.seniority,
      source: job.source,
      market_salary: processedJob.market_salary,
      evolution: processedJob.evolution,
    };
  });
}

export function formatJobForResponse(job: Job) {
  // Get processed job data if available
  const processedJob = job.processedJob || {};

  // Get company information if available
  const employer = job.employer || {
    name: job.employer_name || 'Unknown Employer',
  };

  // Format location information
  const location = job.location || {};
  const municipality =
    location.municipalities && location.municipalities.length > 0
      ? location.municipalities[0]
      : null;
  const region =
    location.regions && location.regions.length > 0
      ? location.regions[0]
      : null;

  // Check if the job is new or closing soon
  const publishedDate = job.published_date || job.createdAt;
  const isNew =
    publishedDate &&
    new Date(publishedDate) >
      new Date(new Date().getTime() - 7 * 24 * 60 * 60 * 1000);
  const closingSoon =
    job.expires_at &&
    new Date(job.expires_at) <
      new Date(new Date().getTime() + 7 * 24 * 60 * 60 * 1000) &&
    !job.expired;

  // Check if salary information is available
  const hasSalary = !!processedJob.salary;

  // Debug log for skill_contexts
  /*  this.logger.debug(
    `Job ${job.ext_id} skill_contexts: ${JSON.stringify(processedJob.skill_contexts)}`,
  );*/

  return {
    id: job.ext_id,
    title: job.title,
    internship: processedJob.internship,
    employer: {
      id: employer.id,
      name: employer.name,
    },
    location: {
      city: municipality?.classificationName || 'Unknown City',
      region: region?.classificationName || 'Unknown Region',
      country: location.country?.[0] || 'Finland',
      isRemote: location.flexible || false,
    },
    employment: {
      working_time: job.working_time,
      continuity: job.continuity,
      working_hours: job.working_hours,
    },
    timing: {
      published_date: publishedDate?.toISOString(),
      expires_at: job.expires_at?.toISOString(),
      is_new: isNew,
      closing_soon: closingSoon,
    },
    salary: {
      lower_bound: processedJob.salary?.salary_lower_bound,
      upper_bound: processedJob.salary?.salary_upper_bound,
      has_salary: hasSalary,
    },
    application: job.application_url
      ? typeof job.application_url === 'string' &&
        job.application_url.startsWith('{')
        ? JSON.parse(job.application_url)?.value || null
        : job.application_url
      : null,
    summary: {
      elevator_pitch: processedJob.elevator_pitch,
      short_description:
        processedJob.summary || job.description?.substring(0, 150),
      primary_tasks: processedJob.primary_tasks,
    },
    skills: processedJob.skills || [],
    // Ensure skill_contexts is explicitly populated, never undefined
    skill_contexts: Array.isArray(processedJob.skill_contexts)
      ? processedJob.skill_contexts
      : [],
    languages: normalizeJobLanguages(job),
    industry: processedJob.industry?.name,
    occupation: processedJob.occupation?.preferredLabel,
    seniority: processedJob.seniority,
    source: job.source,
    market_salary: processedJob.market_salary,
    evolution: processedJob.evolution,
    contacts: processedJob.contacts,
  };
}