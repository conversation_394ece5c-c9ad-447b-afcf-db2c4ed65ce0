import { Job } from '../../../../entities/job.entity';

/**
 * Normalizes job language requirements from a job object
 * First checks the processed job requirements.languages field
 * If that's empty, falls back to the job.languages field
 *
 * @param job The job entity
 * @returns Array of normalized language strings
 */
export function normalizeJobLanguages(job: Job): string[] {
  // First check if there are processed language requirements
  if (
    job.processedJob?.requirements?.languages &&
    job.processedJob.requirements.languages.length > 0
  ) {
    // Map the language requirements to strings in a normalized format
    return job.processedJob.requirements.languages.map(
      (lang) =>
        `${lang.language}${lang.proficiency ? ` (${lang.proficiency})` : ''}`,
    );
  }

  // If no processed languages, fall back to the original languages array
  if (job.languages && job.languages.length > 0) {
    return job.languages.map((langCode) => {
      switch (langCode.toLowerCase()) {
        case 'fi':
          return 'Finnish';
        case 'sv':
          return 'Swedish';
        case 'en':
          return 'English';
        default:
          return langCode;
      }
    });
  }

  // Return empty array if no languages specified
  return [];
}

export function normalizeJobLanguage(langCode: string): string {
  // First check if there are processed language requirements
  switch (langCode.toLowerCase()) {
    case 'fi':
      return 'Finnish';
    case 'sv':
      return 'Swedish';
    case 'en':
      return 'English';
    default:
      return langCode;
  }
}
