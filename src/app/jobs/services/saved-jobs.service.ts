import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SavedJob } from '../../../entities/saved-job.entity';
import { Job } from '../../../entities/job.entity';

@Injectable()
export class SavedJobsService {
  private readonly logger = new Logger(SavedJobsService.name);

  constructor(
    @InjectRepository(SavedJob)
    private readonly savedJobRepository: Repository<SavedJob>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
  ) {}

  async saveJob(userId: string, extId: string): Promise<SavedJob> {
    const job = await this.jobRepository.findOne({ where: { ext_id: extId } });
    if (!job) {
      throw new NotFoundException(`Job with external ID ${extId} not found`);
    }
    const existing = await this.savedJobRepository.findOne({
      where: { userId, job_ext_id: extId },
    });
    if (existing) {
      return existing;
    }
    const saved = new SavedJob();
    saved.userId = userId;
    saved.job_ext_id = extId;
    try {
      const result = await this.savedJobRepository.save(saved);
      this.logger.log(`Saved job ${extId} for user ${userId}`);
      return result;
    } catch (error) {
      this.logger.error(`Failed to save job: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSavedJobs(userId: string): Promise<SavedJob[]> {
    return this.savedJobRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
    });
  }

  async removeSavedJob(userId: string, extId: string): Promise<void> {
    const existing = await this.savedJobRepository.findOne({
      where: { userId, job_ext_id: extId },
    });
    if (!existing) {
      return;
    }
    await this.savedJobRepository.remove(existing);
    this.logger.log(`Removed saved job ${extId} for user ${userId}`);
  }
}
