import { Injectable, Logger } from '@nestjs/common';
import { invert } from 'lodash';
import axios from 'axios';

import {
  AccessToken,
  ApiConfig,
  JobPostingParams,
} from './interfaces/job-market-api.interface';
import { injectParamsToURL } from './helpers/url-inject-params';
import { remapObjectKeys } from './helpers/remap-object-keys';
import { jobMarketResponseKeysHashTable } from './constants/job-market-keys-translations';
import { IGetJobPostingsResponseFI } from './interfaces/job-market-response-Fi.interface';
import { ConfigService } from '@nestjs/config';
import { SocksProxyAgent } from 'socks-proxy-agent';
import fetch from 'node-fetch';

@Injectable()
export class JobMarketApiClient {
  private accessToken: string;
  private tokenExpiredOn: number;
  private readonly config: ApiConfig;
  private readonly logger = new Logger(JobMarketApiClient.name);

  constructor(private readonly configService: ConfigService) {
    this.config = this.getConfig();
  }

  private getConfig(): ApiConfig {
    return {
      clientId: this.configService.getOrThrow('JOB_MARKET_CLIENT_ID'),
      clientSecret: this.configService.getOrThrow('JOB_MARKET_CLIENT_SECRET'),
      scope: this.configService.getOrThrow('JOB_MARKET_SCOPE'),
      tokenEndpoint: this.configService.getOrThrow('JOB_MARKET_TOKEN_ENDPOINT'),
      baseUrl: this.configService.getOrThrow('JOB_MARKET_BASE_URL'),
      maxRetries: this.configService.get('JOB_MARKET_MAX_RETRIES'),
      timeout: this.configService.get('JOB_MARKET_TIMEOUT'),
      proxyUrl: this.configService.get('JOB_MARKET_PROXY_URL'),
      rateLimit: this.configService.get('JOB_MARKET_RATE_LIMIT'),
      rateWindow: this.configService.get('JOB_MARKET_RATE_WINDOW'),
      socksProxy: this.configService.get('JOB_MARKET_SOCKS_PROXY'),
    };
  }

  private async getAccessToken(): Promise<string> {
    if (this.tokenExpiredOn && Date.now() < this.tokenExpiredOn)
      return this.accessToken;

    const link = new URL(this.config.tokenEndpoint);
    const params = new URLSearchParams({
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
      scope: this.config.scope,
      response_type: 'token',
      grant_type: 'client_credentials',
    });
    link.search = params.toString();

    try {
      const response = await axios.post<AccessToken>(link.toString());
      const accessToken = response.data.access_token;
      if (!accessToken) {
        throw new Error('Access token not found in response');
      }
      this.logger.log('Successfully obtained access token.');
      this.accessToken = accessToken;
      this.tokenExpiredOn = Date.now() + response.data.expires_on * 1000 - 60;
      return accessToken;
    } catch (error) {
      let errorMessage = `Failed to obtain access token from ${link}`;
      let errorContext: any = error; // Default context is the error itself

      if (axios.isAxiosError(error)) {
        errorMessage += `: ${error.message}`;
        if (error.response) {
          // Include response status and data in the context for detailed logging
          errorContext = {
            status: error.response.status,
            data: error.response.data,
            headers: error.response.headers,
            config: error.config, // Include request config as well
          };
          errorMessage += ` (Status: ${error.response.status})`;
        } else if (error.request) {
          // Error occurred setting up the request, no response received
          errorMessage += ` (No response received)`;
          errorContext = { request: error.request, config: error.config };
        }
      } else if (error instanceof Error) {
        errorMessage += `: ${error.message}`;
        errorContext = error; // Use the error object itself, which includes stack
      } else {
        errorMessage += `: ${String(error)}`;
      }

      this.logger.error(errorMessage, errorContext); // Pass potentially enriched context

      throw new Error('Could not fetch access token');
    }
  }

  async getJobPostings(
    params: JobPostingParams,
  ): Promise<IGetJobPostingsResponseFI> {
    const token = await this.getAccessToken();
    const url = `${this.config.baseUrl}/jobpostingprovider/v1/tyopaikat`;

    const invertedTranslations = invert(jobMarketResponseKeysHashTable);
    const requestParams: NonNullable<unknown> = remapObjectKeys(
      params,
      invertedTranslations,
    );

    const link = injectParamsToURL(new URL(url), {
      ...requestParams,
      maara: 100,
    });

    // Only use SOCKS proxy in development environment
    const fetchOptions: any = {
      headers: { Authorization: `Bearer ${token}` },
    };

    if (process.env.NODE_ENV === 'development' && this.config.socksProxy) {
      console.log('Using SOCKS proxy');
      fetchOptions.agent = new SocksProxyAgent(this.config.socksProxy);
    }

    const request = await fetch(link, fetchOptions).catch((error) => {
      throw error;
    });

    return (await request.json()) as IGetJobPostingsResponseFI;
  }
}
