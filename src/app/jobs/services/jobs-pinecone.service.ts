import { Injectable, Logger } from '@nestjs/common';
import { PineconeService } from '../../../pinecone/pinecone.service';
import { Job } from '../../../entities/job.entity';
import { ConfigService } from '@nestjs/config';
import { JobsImportRepository } from '../repository/jobs-import.repository';
import { OpenAIService } from '../../../openai/openai.service';
import { normalizeJobLanguages } from './utils/language-utils';

@Injectable()
export class JobsPineconeService {
  private readonly logger = new Logger(JobsPineconeService.name);
  private readonly CACHE_PREFIX = 'pinecone:job:';
  private readonly CACHE_TTL = 24 * 60 * 60; // 24 hours in seconds

  // Vector dimension, matches PineconeService
  readonly dimension = 3072;
  readonly model = 'multilingual-e5-large';
  readonly modelV2 = 'text-embedding-3-large';

  constructor(
    private readonly pineconeService: PineconeService,
    private readonly configService: ConfigService,
    private readonly jobsRepository: JobsImportRepository,
    // private readonly cacheService: CacheService,
    private readonly openaiService: OpenAIService,
  ) {}

  /**
   * Uploads a job to Pinecone for vector search
   * @param job The job to upload
   * @returns True if successful, false otherwise
   */
  async uploadJob(job: Job, useV2 = true): Promise<boolean> {
    try {
      // Get the English translation for the job
      const enTranslation = job.translation.find(
        (t) => t.language?.toLowerCase() === 'en',
      );

      if (!enTranslation) {
        this.logger.warn(`No English translation found for job ${job.ext_id}`);
        return false;
      }

      // Create a summary for the vector search
      const summary = this.generateJobSummary(job, enTranslation);

      // Get region and municipality names/codes
      const regionNames: string[] = [];
      const regionCodes: string[] = [];
      const municipalityNames: string[] = [];
      const municipalityCodes: string[] = [];

      if (job.location?.regions?.length) {
        job.location.regions.forEach((region) => {
          regionNames.push(region.classificationName);
          regionCodes.push(region.code);
        });
      }

      if (job.location?.municipalities?.length) {
        job.location.municipalities.forEach((municipality) => {
          municipalityNames.push(municipality.classificationName);
          municipalityCodes.push(municipality.code);
        });
      }

      try {
        // Generate embeddings for the job summary
        const jobData = {
          jobTitle:
            job.processedJob.market_salary?.job_role + ' - ' + job.title || '',
          skills: job.processedJob.skills || [],
          skillContexts: job.processedJob.skill_contexts || [],
          primaryTasks: job.processedJob.primary_tasks || [],
          keywords: job.processedJob.keywords || [],
          expectedOutcomes: job.processedJob.expected_outcomes || [],
          languages: normalizeJobLanguages(job),
          certifications: job.processedJob.requirements?.certifications || [],
        };

        let embeddings;

        if (useV2) {
          embeddings = await this.createCombinedEmbeddingV2(jobData);
        } else {
          embeddings = await this.createCombinedEmbedding(jobData);
        }

        // Validate that embeddings were generated successfully
        if (!embeddings || !Array.isArray(embeddings)) {
          throw new Error(
            `Invalid embeddings format generated for job ${job.ext_id}`,
          );
        }

        // Prepare metadata for filtering
        const metadata = {
          ext_id: job.ext_id,
          title: job.title + ' - ' + job.processedJob.market_salary?.job_role,
          employer_name: job.employer_name || '',
          employer_id: job.employer_id?.toString() || '',
          // Handle regions and municipalities as arrays
          region_names: regionNames,
          region_codes: regionCodes,
          municipality_names: municipalityNames,
          municipality_codes: municipalityCodes,
          // If we have processed job data
          industry_code: job.processedJob.industry_code || '',
          occupation_code: job.processedJob.occupation_code || '',
          occupation_code_secondary:
            job.processedJob.occupation_code_secondary || '',
          occupation_code_tertiary:
            job.processedJob.occupation_code_tertiary || '',
          seniority: job.processedJob.seniority || '',
          skills: (job.processedJob.skills || []).slice(0, 10),
          keywords: (job.processedJob.keywords || []).slice(0, 10),
          // Include dates for filtering
          created_at: job.createdAt?.toISOString() || '',
          updated_at: job.updatedAt?.toISOString() || '',
          published_date: job.published_date?.toISOString() || '',
          expires_at: job.expires_at
            ? Math.floor(job.expires_at.getTime() / 1000)
            : 0,

          working_hours: job.working_hours || '',
          working_time: job.working_time || '',
          workplace_flexibility: job.workplace_flexibility || '',
          continuity: job.continuity || '',
          languages: normalizeJobLanguages(job),
          internship: job.processedJob.internship,
          hasSalary:
            job.processedJob.salary !== undefined &&
            job.processedJob.salary.salary_lower_bound > 0,
          // Include the summary in metadata for filtering/retrieving
          summary: this.formatJobDataForEmbedding(jobData),
          country: 'Finland',
        };

        // Get the Pinecone index
        const index = await this.getDefaultIndex();

        // Structure the record for Pinecone using standard format
        const pineconeRecord = {
          id: job.ext_id,
          values: embeddings,
          metadata,
        };

        // Upload to Pinecone
        await index.upsert([pineconeRecord]);

        // Mark job as uploaded in the database
        job.pinecone_uploaded = true;
        job.pinecone_uploaded_at = new Date();
        await this.jobsRepository.save(job);

        // Cache the result
        // await this.cacheService.set(cacheKey, true, this.CACHE_TTL);

        this.logger.log(`Successfully uploaded job ${job.ext_id} to Pinecone`);
        return true;
      } catch (error) {
        this.logger.error(
          `Error uploading job ${job.ext_id} to Pinecone: ${error.message}`,
          error.stack,
        );
        return false;
      }
    } catch (error) {
      this.logger.error(
        `Error uploading job ${job.ext_id} to Pinecone: ${error.message}`,
      );
      return false;
    }
  }

  /**
   * Uploads multiple jobs to Pinecone in batches
   * @param jobs The jobs to upload
   * @param batchSize The size of each batch (default: 100)
   * @returns The number of successfully uploaded jobs
   */
  async uploadJobs(jobs: Job[], batchSize = 100): Promise<number> {
    try {
      let successCount = 0;

      // Process in batches to avoid overloading Pinecone
      for (let i = 0; i < jobs.length; i += batchSize) {
        const batch = jobs.slice(i, i + batchSize);
        const results = await Promise.all(
          batch.map((job) => this.uploadJob(job)),
        );

        // Count successes
        successCount += results.filter(Boolean).length;

        this.logger.log(
          `Processed batch ${i / batchSize + 1}/${Math.ceil(
            jobs.length / batchSize,
          )}: ${results.filter(Boolean).length}/${batch.length} successful`,
        );
      }

      return successCount;
    } catch (error) {
      this.logger.error(`Error in batch upload to Pinecone: ${error.message}`);
      throw error;
    }
  }

  /**
   * Uploads all processed jobs to Pinecone
   * @param batchSize The size of each batch (default: 100)
   * @returns The number of jobs uploaded
   */
  async uploadAllProcessedJobs(batchSize = 100): Promise<number> {
    try {
      const jobsToUpload =
        await this.jobsRepository.findProcessedJobsForPinecone();
      if (jobsToUpload.length === 0) {
        this.logger.log('No new processed jobs found to upload to Pinecone');
        return 0;
      }

      this.logger.log(
        `Uploading ${jobsToUpload.length} processed jobs to Pinecone`,
      );
      return await this.uploadJobs(jobsToUpload, batchSize);
    } catch (error) {
      this.logger.error(`Error uploading processed jobs: ${error.message}`);
      throw error;
    }
  }

  /**
   * Updates all jobs in Pinecone to ensure they have the keyword field
   * @param batchSize The size of each batch (default: 100)
   * @returns The number of jobs updated
   */
  async updateAllJobsWithKeywords(batchSize = 100): Promise<number> {
    try {
      // Get all processed jobs that have already been uploaded to Pinecone
      // Create a custom method in the repository to find jobs that are
      // processed, uploaded to Pinecone, and not expired
      const jobs = await this.jobsRepository.findProcessedJobs();

      // Filter jobs to only include those that have been uploaded to Pinecone
      const uploadedJobs = jobs.filter((job) => job.pinecone_uploaded);

      this.logger.log(
        `Found ${uploadedJobs.length} processed and uploaded jobs to update with keywords in Pinecone`,
      );

      if (uploadedJobs.length === 0) {
        this.logger.log('No jobs to update in Pinecone');
        return 0;
      }

      let successCount = 0;

      // Process in batches to avoid overloading Pinecone
      for (let i = 0; i < uploadedJobs.length; i += batchSize) {
        const batch = uploadedJobs.slice(i, i + batchSize);
        const results = await Promise.all(
          batch.map((job) => this.uploadJob(job, true)),
        );

        // Count successes
        successCount += results.filter(Boolean).length;

        this.logger.log(
          `Processed batch ${i / batchSize + 1}/${Math.ceil(
            uploadedJobs.length / batchSize,
          )}: ${results.filter(Boolean).length}/${batch.length} successfully updated with keywords`,
        );
      }

      this.logger.log(
        `Completed updating ${successCount}/${uploadedJobs.length} jobs with keywords in Pinecone`,
      );
      return successCount;
    } catch (error) {
      this.logger.error(
        `Error updating jobs with keywords in Pinecone: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Generates a rich summary of the job for semantic search
   * @param job The job entity
   * @param translation The English translation of the job
   * @returns A summary string for semantic search
   */
  private generateJobSummary(job: Job, translation: any): string {
    // Start with the title and employer
    let summary = `${translation.title || 'Job Position'} at ${job.employer_name || 'Company'}`;

    // Add location if available
    if (job.location?.municipalities?.length || job.location?.regions?.length) {
      const locationParts: string[] = [];

      if (job.location.municipalities?.length) {
        const municipalityNames: string[] = [];
        for (const m of job.location.municipalities) {
          municipalityNames.push(m.classificationName);
        }
        locationParts.push(municipalityNames.join(', '));
      }

      if (job.location.regions?.length) {
        const regionNames: string[] = [];
        for (const r of job.location.regions) {
          regionNames.push(r.classificationName);
        }
        locationParts.push(regionNames.join(', '));
      }

      if (locationParts.length > 0) {
        summary += ` in ${locationParts.join(', ')}`;
      }
    }

    // Add processed job information if available
    if (job.processedJob) {
      // Add industry and occupation if available
      if (job.processedJob.industry_code || job.processedJob.occupation_code) {
        summary += `. Industry: ${job.processedJob.industry_code || 'Various'}`;
        summary += `. Occupation: ${job.processedJob.occupation_code || 'Various'}`;
      }

      // Add seniority if available
      if (job.processedJob.seniority) {
        summary += `. Seniority: ${job.processedJob.seniority}`;
      }

      // Add skills if available
      if (job.processedJob.skills && job.processedJob.skills.length > 0) {
        summary += `. Skills: ${job.processedJob.skills.join(', ')}`;
      }
    }

    // Add job description if available
    if (translation.description) {
      // Truncate description if too long to avoid potential issues
      const maxLength = 1000;
      const description =
        translation.description.length > maxLength
          ? `${translation.description.substring(0, maxLength)}...`
          : translation.description;

      summary += `\n\n${job.processedJob.keywords.join(', ')}`;
    }

    return summary;
  }

  /**
   * Gets the default Pinecone index for job searches
   * @returns The default Pinecone index
   */
  async getDefaultIndex() {
    return this.pineconeService.getV2Index();
  }

  /**
   * Generate embeddings for text using Pinecone
   * @param text Text to generate embeddings for
   * @param inputType Type of input ('query' or 'passage')
   * @returns Vector embeddings or null if generation fails
   */
  async generateEmbeddings(
    text: string,
    inputType: 'query' | 'passage' = 'query',
  ): Promise<number[] | null> {
    try {
      this.logger.log(
        `Generating embeddings for text (${inputType}): "${text.substring(0, 50)}..."`,
      );
      const embeddings = await this.pineconeService
        .getPineconeClient()
        .inference.embed(this.model, [text], {
          inputType,
          truncate: 'END',
        });

      if (
        embeddings &&
        Array.isArray(embeddings.data) &&
        embeddings.data.length > 0
      ) {
        this.logger.debug(`Successfully generated embeddings`);
        return (embeddings.data[0] as any).values;
      } else {
        this.logger.warn(`Failed to generate embeddings, format unexpected`);
        return null;
      }
    } catch (error) {
      this.logger.error(`Error generating embeddings: ${error.message}`);
      return null;
    }
  }

  async generateEmbeddingsV2(texts: string | string[]): Promise<number[]> {
    const embeddings = await this.openaiService.createEmbedding(
      Array.isArray(texts) ? texts.join('\n') : texts,
    );
    console.log(`Embedding dimension: ${embeddings.length}`);
    return embeddings;
  }

  // Function to create combined embeddings with weights
  async createCombinedEmbedding(jobData: {
    jobTitle: string;
    skills: string[];
    skillContexts: string[];
    primaryTasks: string[];
    keywords: string[];
    expectedOutcomes: string[];
    languages: string[];
    certifications: string[];
  }): Promise<number[]> {
    // Generate embeddings for each field
    const jobTitleEmbedding = await this.generateEmbeddings(
      jobData.jobTitle,
      'passage',
    );

    // For arrays, generate individual embeddings and average them
    const skillsEmbedding = await this.generateAveragedEmbedding(
      jobData.skills,
    );
    const skillContextsEmbedding = await this.generateAveragedEmbedding(
      jobData.skillContexts,
    );
    const primaryTasksEmbedding = await this.generateAveragedEmbedding(
      jobData.primaryTasks,
    );
    const keywordsEmbedding = await this.generateAveragedEmbedding(
      jobData.keywords,
    );
    const expectedOutcomesEmbedding = await this.generateAveragedEmbedding(
      jobData.expectedOutcomes,
    );
    const languagesEmbedding = await this.generateAveragedEmbedding(
      jobData.languages,
    );
    const certificationsEmbedding = await this.generateAveragedEmbedding(
      jobData.certifications,
    );

    // Apply weights to each embedding
    const combinedEmbedding = jobTitleEmbedding!.map(
      (value: number, index: number) =>
        0.2 * value +
        0.35 * skillsEmbedding[index] +
        0.15 * skillContextsEmbedding[index] +
        0.1 * primaryTasksEmbedding[index] +
        0.05 * keywordsEmbedding[index] +
        0.05 * expectedOutcomesEmbedding[index] +
        0.05 * languagesEmbedding[index] +
        0.05 * certificationsEmbedding[index],
    );

    return combinedEmbedding;
  }

  // Helper function to generate averaged embeddings for arrays of strings
  async generateAveragedEmbedding(items: string[]): Promise<number[]> {
    if (!items || items.length === 0) {
      // Return zero vector with appropriate dimensions if no items
      const dimensions = 1024; // Adjust based on your model's output dimensions
      return new Array(dimensions).fill(0);
    }

    // Generate embeddings for each item
    const embeddings = await Promise.all(
      items.map((item) => this.generateEmbeddings(item, 'passage')),
    );

    // Average the embeddings
    const dimensions = embeddings[0]!.length;
    const averagedEmbedding = new Array(dimensions).fill(0);

    for (const embedding of embeddings) {
      for (let i = 0; i < dimensions; i++) {
        averagedEmbedding[i] += embedding![i] / embeddings.length;
      }
    }

    return averagedEmbedding;
  }

  // Format job data into a structured text format for embedding
  private formatJobDataForEmbedding(jobData: {
    jobTitle: string;
    skills: string[];
    skillContexts: string[];
    primaryTasks: string[];
    keywords: string[];
    expectedOutcomes: string[];
    languages: string[];
    certifications: string[];
  }): string {
    return `
Job Title: ${jobData.jobTitle}
Skills: ${jobData.skills.join(', ')}
Skill Contexts: ${jobData.skillContexts.join(', ')}
Primary Tasks: ${jobData.primaryTasks.join(', ')}
Keywords: ${jobData.keywords.join(', ')}
Expected Outcomes: ${jobData.expectedOutcomes.join(', ')}
`.trim();
  }

  // Function to create combined embeddings with weights
  async createCombinedEmbeddingV2(jobData: {
    jobTitle: string;
    skills: string[];
    skillContexts: string[];
    primaryTasks: string[];
    keywords: string[];
    expectedOutcomes: string[];
    languages: string[];
    certifications: string[];
  }): Promise<number[]> {
    // Format the job data into a structured text
    const combinedText = this.formatJobDataForEmbedding(jobData);

    // Generate embeddings for the formatted text
    const combinedEmbedding = this.generateEmbeddingsV2(combinedText);

    return combinedEmbedding;
  }

  /**
   * Updates existing jobs in Pinecone to use Unix timestamp for expires_at field
   * @param batchSize The size of each batch (default: 500)
   * @returns The number of successfully updated jobs and failed jobs
   */
  async updateExpiresAtToUnixTimestamp(
    batchSize = 500,
  ): Promise<{
    updated: number;
    failed: { ext_id: string; reason: string }[];
  }> {
    try {
      this.logger.log(
        'Starting to update expires_at field to Unix timestamp in Pinecone',
      );

      // Use the new repository method to get jobs uploaded to Pinecone
      const jobs = await this.jobsRepository.findPineconeJobs();

      this.logger.log(`Found ${jobs.length} jobs to update in Pinecone`);

      let successCount = 0;
      // Collect failed or skipped jobs with reasons
      const failedJobs: { ext_id: string; reason: string }[] = [];

      // Get the Pinecone index
      const index = await this.getDefaultIndex();

      // Process in batches to avoid overloading Pinecone
      for (let i = 0; i < jobs.length; i += batchSize) {
        const batch = jobs.slice(i, i + batchSize);

        const updatePromises = batch.map(async (job) => {
          try {
            if (!job.expires_at) {
              failedJobs.push({ ext_id: job.ext_id, reason: 'no expires_at' });
              return false;
            }

            // Fetch the existing record from Pinecone to get all current metadata
            const fetchResponse = await index.fetch([job.ext_id]);

            if (!fetchResponse.records || !fetchResponse.records[job.ext_id]) {
              failedJobs.push({
                ext_id: job.ext_id,
                reason: 'not found in Pinecone',
              });
              this.logger.warn(`Job ${job.ext_id} not found in Pinecone`);
              return false;
            }

            const existingRecord = fetchResponse.records[job.ext_id];
            const existingMetadata = existingRecord.metadata!;

            // Determine if region/municipality fields need splitting
            const [regionRaw, regionCodeRaw, muniRaw, muniCodeRaw] = [
              existingMetadata.region_names,
              existingMetadata.region_codes,
              existingMetadata.municipality_names,
              existingMetadata.municipality_codes,
            ];
            const needsArrayConversion = [
              regionRaw,
              regionCodeRaw,
              muniRaw,
              muniCodeRaw,
            ].some((v) => typeof v === 'string');
            const unixExpiresAt = Math.floor(job.expires_at.getTime() / 1000);
            if (!needsArrayConversion) {
              // Only expires_at needs updating
              const updatedMetadata = {
                ...existingMetadata,
                expires_at: unixExpiresAt,
              };
              await index.upsert([
                {
                  id: job.ext_id,
                  values: existingRecord.values,
                  metadata: updatedMetadata,
                },
              ]);
              return true;
            }

            // Normalize region & municipality fields into arrays
            const regionNamesRaw = existingMetadata.region_names;
            const regionNamesArr = Array.isArray(regionNamesRaw)
              ? regionNamesRaw
              : regionNamesRaw
                ? (regionNamesRaw as string).split(',').map((s) => s.trim())
                : [];
            const regionCodesRaw = existingMetadata.region_codes;
            const regionCodesArr = Array.isArray(regionCodesRaw)
              ? regionCodesRaw
              : regionCodesRaw
                ? (regionCodesRaw as string).split(',').map((s) => s.trim())
                : [];
            const muniNamesRaw = existingMetadata.municipality_names;
            const muniNamesArr = Array.isArray(muniNamesRaw)
              ? muniNamesRaw
              : muniNamesRaw
                ? (muniNamesRaw as string).split(',').map((s) => s.trim())
                : [];
            const muniCodesRaw = existingMetadata.municipality_codes;
            const muniCodesArr = Array.isArray(muniCodesRaw)
              ? muniCodesRaw
              : muniCodesRaw
                ? (muniCodesRaw as string).split(',').map((s) => s.trim())
                : [];

            // Update expires_at and array fields
            const updatedMetadata = {
              ...existingMetadata,
              expires_at: unixExpiresAt,
              region_names: regionNamesArr,
              region_codes: regionCodesArr,
              municipality_names: muniNamesArr,
              municipality_codes: muniCodesArr,
            };

            // Structure the record for Pinecone using standard format
            const pineconeRecord = {
              id: job.ext_id,
              values: existingRecord.values,
              metadata: updatedMetadata,
            };

            // Update in Pinecone
            await index.upsert([pineconeRecord]);

            return true;
          } catch (error) {
            this.logger.error(
              `Error updating job ${job.ext_id} in Pinecone: ${error.message}`,
            );
            // Capture error reason
            failedJobs.push({ ext_id: job.ext_id, reason: error.message });
            return false;
          }
        });

        const results = await Promise.all(updatePromises);
        const batchSuccessCount = results.filter(Boolean).length;

        successCount += batchSuccessCount;

        this.logger.log(
          `Processed batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(
            jobs.length / batchSize,
          )}: ${batchSuccessCount}/${batch.length} successful`,
        );
      }

      this.logger.log(
        `Finished updating expires_at field in Pinecone. ${successCount}/${jobs.length} jobs updated successfully`,
      );
      if (failedJobs.length > 0) {
        this.logger.warn(
          `Jobs not updated or skipped (${failedJobs.length}/${jobs.length}): ${failedJobs
            .map((f) => f.ext_id)
            .join(', ')}`,
        );
      }
      return { updated: successCount, failed: failedJobs };
    } catch (error) {
      this.logger.error(
        `Error updating expires_at field in Pinecone: ${error.message}`,
        error.stack,
      );
      return { updated: 0, failed: [] };
    }
  }
}
