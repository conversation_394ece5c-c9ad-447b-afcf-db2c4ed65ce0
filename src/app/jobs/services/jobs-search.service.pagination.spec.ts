import { Test, TestingModule } from '@nestjs/testing';
import { JobsSearchService } from './jobs-search.service';
import {
  JobSearchCacheService,
  JobSearchQueryBuilder,
  JobSearchResultCache,
  JobSearchResultProcessor,
  JobSearchVectorService,
  PaginationFlowService,
} from './search';
import { JobsImportRepository } from '../repository/jobs-import.repository';
import { ForYouRepository } from '../repository/for-you.repository';
import { UserApiService } from '../../user/services/user-api.service';
import { RecommendationEngineService } from './recommendation-engine.service';
import { Logger } from '@nestjs/common';
import { Job } from '../../../entities/job.entity';
import { JobSearchParams } from './interfaces/job-search.interface';

describe('JobsSearchService - Pagination', () => {
  let service: JobsSearchService;
  let queryBuilder: JobSearchQueryBuilder;
  let vectorService: JobSearchVectorService;
  let resultProcessor: JobSearchResultProcessor;
  let cacheService: JobSearchCacheService;
  let resultCache: JobSearchResultCache;
  let paginationFlow: PaginationFlowService;
  let jobsRepository: JobsImportRepository;

  // Mock implementations
  const mockQueryBuilder = {
    buildSearchContext: jest.fn(),
    buildPineconeFilters: jest.fn(),
    buildQueryOptions: jest.fn(),
  };

  const mockVectorService = {
    getDefaultIndex: jest.fn(),
    generateQueryVector: jest.fn(),
    rerankResults: jest.fn(),
  };

  const mockResultProcessor = {
    processResults: jest.fn(),
  };

  const mockCacheService = {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn(),
  };

  const mockResultCache = {
    getFullResults: jest.fn(),
    setFullResults: jest.fn(),
    getScoreMap: jest.fn(),
    setScoreMap: jest.fn(),
    getPagedResults: jest.fn(),
    calculatePaginationMetadata: jest.fn(),
    MAX_RESULTS: 200,
  };

  const mockPaginationFlow = {
    getCachedPagedResults: jest.fn(),
    storeResultsForPagination: jest.fn(),
    getContextForFullResultsFetch: jest.fn(),
  };

  const mockJobsRepository = {
    findJobsWithDetails: jest.fn(),
    findByIds: jest.fn(),
    findByExtId: jest.fn(),
  };

  const mockForYouRepository = {
    saveForYouJobs: jest.fn(),
    getForYouJobs: jest.fn(),
  };

  const mockUserApiService = {
    getUserKeywordsData: jest.fn(),
  };

  const mockRecommendationEngine = {
    getRecommendations: jest.fn(),
  };

  beforeEach(async () => {
    jest.clearAllMocks();

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobsSearchService,
        { provide: JobSearchQueryBuilder, useValue: mockQueryBuilder },
        { provide: JobSearchVectorService, useValue: mockVectorService },
        { provide: JobSearchResultProcessor, useValue: mockResultProcessor },
        { provide: JobSearchCacheService, useValue: mockCacheService },
        { provide: JobSearchResultCache, useValue: mockResultCache },
        { provide: PaginationFlowService, useValue: mockPaginationFlow },
        { provide: JobsImportRepository, useValue: mockJobsRepository },
        { provide: ForYouRepository, useValue: mockForYouRepository },
        { provide: UserApiService, useValue: mockUserApiService },
        {
          provide: RecommendationEngineService,
          useValue: mockRecommendationEngine,
        },
      ],
    }).compile();

    service = module.get<JobsSearchService>(JobsSearchService);
    queryBuilder = module.get<JobSearchQueryBuilder>(JobSearchQueryBuilder);
    vectorService = module.get<JobSearchVectorService>(JobSearchVectorService);
    resultProcessor = module.get<JobSearchResultProcessor>(
      JobSearchResultProcessor,
    );
    cacheService = module.get<JobSearchCacheService>(JobSearchCacheService);
    resultCache = module.get<JobSearchResultCache>(JobSearchResultCache);
    paginationFlow = module.get<PaginationFlowService>(PaginationFlowService);
    jobsRepository = module.get<JobsImportRepository>(JobsImportRepository);

    // Silence the logger during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('searchJobs with pagination', () => {
    const searchParams: JobSearchParams = {
      query: 'developer',
      page: 2,
      limit: 10,
      user_id: 'test-user',
      useCache: true,
    };

    const mockSearchContext = {
      searchParams,
      keywords: 'developer',
      skillContexts: [],
      pagination: { page: 2, limit: 10 },
      sorting: { sortDirection: 'desc' },
      filters: {},
      useVectorSearch: true,
      userId: 'test-user',
      useCache: true,
    };

    const mockExpandedContext = {
      ...mockSearchContext,
      pagination: { page: 1, limit: 50 }, // Expanded for full results fetch
    };

    const mockCachedResult = {
      jobs: [{ id: '1', title: 'Cached Job' }],
      meta: { total: 1, page: 2, limit: 10, has_next: false },
    };

    const mockCachedPagedResult = {
      jobs: [{ id: '2', title: 'Cached Paged Job' }],
      meta: { total: 30, page: 2, limit: 10, has_next: true },
    };

    const mockJobs = Array(30)
      .fill(0)
      .map((_, i) => ({ id: i + 1 }) as unknown as Job);
    const mockScoreMap = new Map(mockJobs.map((job) => [String(job.id), 0.9]));

    const mockPineconeResponse = {
      matches: Array(30)
        .fill(0)
        .map((_, i) => ({
          id: String(i + 1),
          score: 0.9,
          metadata: { skills: 'JavaScript, React', title: 'Developer Job' },
        })),
    };

    const mockResults = {
      jobs: mockJobs
        .slice(10, 20)
        .map((job) => ({ id: String(job.id), title: 'Job' })),
      meta: { total: 30, page: 2, limit: 10, has_next: true },
    };

    it('should return cached results when available in page-specific cache', async () => {
      // Arrange
      mockQueryBuilder.buildSearchContext.mockReturnValue(mockSearchContext);
      mockCacheService.get.mockResolvedValue(mockCachedResult);

      // Act
      const result = await service.searchJobs(searchParams);

      // Assert
      expect(queryBuilder.buildSearchContext).toHaveBeenCalledWith(
        searchParams,
        undefined,
        [],
      );
      expect(cacheService.get).toHaveBeenCalledWith(mockSearchContext);
      expect(paginationFlow.getCachedPagedResults).not.toHaveBeenCalled();
      expect(result).toEqual(mockCachedResult);
    });

    it('should use pagination flow when page-specific cache misses but user-specific cache hits', async () => {
      // Arrange
      mockQueryBuilder.buildSearchContext.mockReturnValue(mockSearchContext);
      mockCacheService.get.mockResolvedValue(null); // Standard cache miss
      mockPaginationFlow.getCachedPagedResults.mockResolvedValue(
        mockCachedPagedResult,
      ); // But user cache hit

      // Act
      const result = await service.searchJobs(searchParams);

      // Assert
      expect(queryBuilder.buildSearchContext).toHaveBeenCalledWith(
        searchParams,
        undefined,
        [],
      );
      expect(cacheService.get).toHaveBeenCalledWith(mockSearchContext);
      expect(paginationFlow.getCachedPagedResults).toHaveBeenCalledWith(
        mockSearchContext,
      );
      expect(cacheService.set).toHaveBeenCalledWith(
        mockSearchContext,
        mockCachedPagedResult,
      );
      expect(result).toEqual(mockCachedPagedResult);
    });

    it('should fetch expanded results when both caches miss', async () => {
      // Arrange
      mockQueryBuilder.buildSearchContext.mockReturnValue(mockSearchContext);
      mockCacheService.get.mockResolvedValue(null); // Standard cache miss
      mockPaginationFlow.getCachedPagedResults.mockResolvedValue(null); // User cache miss
      mockPaginationFlow.getContextForFullResultsFetch.mockReturnValue(
        mockExpandedContext,
      );

      const mockPineconeIndex = {
        query: jest.fn().mockResolvedValue(mockPineconeResponse),
      };
      mockVectorService.getDefaultIndex.mockResolvedValue(mockPineconeIndex);
      mockQueryBuilder.buildPineconeFilters.mockReturnValue({});
      mockVectorService.generateQueryVector.mockResolvedValue([]);
      mockQueryBuilder.buildQueryOptions.mockReturnValue({});
      mockVectorService.rerankResults.mockResolvedValue(
        mockPineconeResponse.matches.map((m) => ({
          id: m.id,
          score: m.score,
          context: '',
        })),
      );
      mockJobsRepository.findJobsWithDetails.mockResolvedValue(mockJobs);
      mockResultProcessor.processResults.mockReturnValue(mockResults);

      // Act
      const result = await service.searchJobs(searchParams);

      // Assert
      expect(queryBuilder.buildSearchContext).toHaveBeenCalledWith(
        searchParams,
        undefined,
        [],
      );
      expect(cacheService.get).toHaveBeenCalledWith(mockSearchContext);
      expect(paginationFlow.getCachedPagedResults).toHaveBeenCalledWith(
        mockSearchContext,
      );
      expect(paginationFlow.getContextForFullResultsFetch).toHaveBeenCalledWith(
        mockSearchContext,
      );

      // Should use expanded context for Pinecone query
      expect(vectorService.getDefaultIndex).toHaveBeenCalled();
      expect(queryBuilder.buildPineconeFilters).toHaveBeenCalledWith(
        mockExpandedContext,
      );
      expect(vectorService.generateQueryVector).toHaveBeenCalledWith(
        mockExpandedContext,
      );
      expect(queryBuilder.buildQueryOptions).toHaveBeenCalled();
      expect(mockPineconeIndex.query).toHaveBeenCalled();

      // Should store full results for future pagination
      expect(paginationFlow.storeResultsForPagination).toHaveBeenCalledWith(
        mockSearchContext,
        mockJobs,
        expect.any(Map),
      );

      // Should process results with original pagination context
      expect(resultProcessor.processResults).toHaveBeenCalledWith(
        mockSearchContext,
        mockJobs,
        expect.any(Map),
        mockPineconeResponse.matches.length,
      );

      // Should cache the results
      expect(cacheService.set).toHaveBeenCalledWith(
        mockSearchContext,
        mockResults,
      );

      expect(result).toEqual(mockResults);
    });

    it('should not use pagination flow when useCache is false', async () => {
      // Arrange
      const noCache = { ...searchParams, useCache: false };
      const noCacheContext = { ...mockSearchContext, useCache: false };

      mockQueryBuilder.buildSearchContext.mockReturnValue(noCacheContext);
      mockCacheService.get.mockResolvedValue(null);

      const mockPineconeIndex = {
        query: jest.fn().mockResolvedValue(mockPineconeResponse),
      };
      mockVectorService.getDefaultIndex.mockResolvedValue(mockPineconeIndex);
      mockQueryBuilder.buildPineconeFilters.mockReturnValue({});
      mockVectorService.generateQueryVector.mockResolvedValue([]);
      mockQueryBuilder.buildQueryOptions.mockReturnValue({});
      mockVectorService.rerankResults.mockResolvedValue(
        mockPineconeResponse.matches.map((m) => ({
          id: m.id,
          score: m.score,
          context: '',
        })),
      );
      mockJobsRepository.findJobsWithDetails.mockResolvedValue(mockJobs);
      mockResultProcessor.processResults.mockReturnValue(mockResults);

      // Act
      const result = await service.searchJobs(noCache);

      // Assert
      expect(paginationFlow.getCachedPagedResults).not.toHaveBeenCalled();
      expect(
        paginationFlow.getContextForFullResultsFetch,
      ).not.toHaveBeenCalled();
      expect(paginationFlow.storeResultsForPagination).not.toHaveBeenCalled();
      expect(result).toEqual(mockResults);
    });

    it('should not use pagination flow when userId is not provided', async () => {
      // Arrange
      const noUser = { ...searchParams, user_id: undefined };
      const noUserContext = { ...mockSearchContext, userId: undefined };

      mockQueryBuilder.buildSearchContext.mockReturnValue(noUserContext);
      mockCacheService.get.mockResolvedValue(null);

      const mockPineconeIndex = {
        query: jest.fn().mockResolvedValue(mockPineconeResponse),
      };
      mockVectorService.getDefaultIndex.mockResolvedValue(mockPineconeIndex);
      mockQueryBuilder.buildPineconeFilters.mockReturnValue({});
      mockVectorService.generateQueryVector.mockResolvedValue([]);
      mockQueryBuilder.buildQueryOptions.mockReturnValue({});
      mockVectorService.rerankResults.mockResolvedValue(
        mockPineconeResponse.matches.map((m) => ({
          id: m.id,
          score: m.score,
          context: '',
        })),
      );
      mockJobsRepository.findJobsWithDetails.mockResolvedValue(mockJobs);
      mockResultProcessor.processResults.mockReturnValue(mockResults);

      // Act
      const result = await service.searchJobs(noUser);

      // Assert
      expect(paginationFlow.getCachedPagedResults).not.toHaveBeenCalled();
      expect(
        paginationFlow.getContextForFullResultsFetch,
      ).not.toHaveBeenCalled();
      expect(paginationFlow.storeResultsForPagination).not.toHaveBeenCalled();
      expect(result).toEqual(mockResults);
    });
  });
});