/**
 * Mappings for converting Jobly employment types to our internal formats
 */

/**
 * Maps Jobly employment type values to our internal working_time and continuity fields
 */
export const joblyEmploymentTypeToJobFields = {
  "Vakituinen": { continuity: "01" /* Permanent */ },
  "Kokopäiväinen": { working_time: "01" /* Full-time */ },
  "Määräaikainen": { continuity: "02" /* Temporary */ },
  "Osa-aikainen": { working_time: "02" /* Part-time */ },
  "Osa-aikainen ja tuntityö": { working_time: "02" /* Part-time */ },
  "Keikkatyö": { working_time: "03", continuity: "03" /* Gig/on-call work */ },
  "Har<PERSON>ittelu": { continuity: "04" /* Internship */ },
  "Kesätyö": { continuity: "02" /* Temporary */ },
  "Vapaaehtoistyö": { working_time: "04" /* Volunteer */ },
  "Vuokratyö": { continuity: "05" /* Agency/temp work */ },
};

/**
 * Default values for working_time and continuity if no mapping exists
 */
export const DEFAULT_WORKING_TIME = "01"; // Full-time
export const DEFAULT_CONTINUITY = "01";  // Permanent

/**
 * Maps Jobly employment type values to our workplace_flexibility field
 */
export const joblyEmploymentTypeToFlexibility = {
  "Etätyö": "remote",
  "Etätyömahdollisuus": "hybrid",
};

/**
 * Default value for flexible field in JobLocation
 * Defaults to false unless specifically marked as remote/hybrid
 */
export const DEFAULT_FLEXIBILITY = false;