export const jobMarketResponseKeysHashTable = {
  "sivutus": "pagination",
  "sivu": "page",
  "maara": "amount",
  "ilmoituksienMaara": "numberOfAnnouncements",
  "ilmoitukset": "jobs",
  "ilmoituksenID": "jobID",
  "tyollistaja": "employer",
  "ilmoittajanYTunnus": "employerBusinessID",
  "ilmoittajanNimi": "employerName",
  "kieli": "language",
  "kieliKoodi": "languageCode",
  "arvo": "value",
  "luontipvm": "creationDate",
  "muokattupvm": "modificationDate",
  "julkaisupvm": "publicationDate",
  "ilmoituksenKielet": "announcementLanguages",
  "perustiedot": "basicInformation",
  "tyonOtsikko": "jobTitle",
  "tyonTiivistelma": "jobSummary",
  "tyonKuvaus": "jobDescription",
  "paikkojenMaara": "numberOfPositions",
  "palvelussuhde": "employmentRelation",
  "tyyppi": "type",
  "tyosuhde": "employment",
  "vuokratyo": "temporaryWork",
  "rekrytointiToimeksianto": "recruitmentAssignment",
  "tyoharjoittelu": "internship",
  "oppisopimus": "apprenticeship",
  "vuorotteluvapaanSijaisuus": "substituteForJobAlternationLeave",
  "tyonJatkuvuus": "jobContinuity",
  "tyoAika": "workingTime",
  "kutsutaanTarvittaessa": "onCall",
  "palkanPeruste": "salaryBasis",
  "palkanLisatieto": "salaryAdditionalInfo",
  "tyoAlkaa": "jobStarts",
  "tyoAlkaaLisatieto": "jobStartsAdditionalInfo",
  "tyoskentely": "working",
  "tyoskentelyAika": "workingHours",
  "vuorotyo": "shiftWork",
  "kuuluuMatkustamista": "includesTravel",
  "osaamisvaatimukset": "competenceRequirements",
  "ammatit": "professions",
  "ammattiryhma": "professionalGroup",
  "osaamiset": "skills",
  "kielitaidot": "languageSkills",
  "kortitJaLuvat": "cardsAndPermits",
  "lupaKoodit": "permitCodes",
  "kortitJaLuvatLisatieto": "cardsAndPermitsAdditionalInfo",
  "rikosrekisteriote": "criminalRecord",
  "sijainti": "location",
  "sijaintiJoustava": "flexibleLocation",
  "maa": "country",
  "maakunta": "region",
  "kunta": "municipality",
  "toimipaikka": "workplace",
  "toimipaikanNimi": "workplaceName",
  "postiosoite": "postalAddress",
  "postinumero": "postalCode",
  "postitoimipaikka": "postOffice",
  "hakeminen": "application",
  "hakuaikaPaattyy": "applicationDeadline",
  "ilmoittajanYhteystiedot": "employerContactDetails",
  "etunimi": "firstName",
  "sukunimi": "lastName",
  "sposti": "email",
  "puhelinNro": "phoneNumber",
  "hakemuksenUrls": "applicationUrls",
  "hakuohjeet": "applicationInstructions",
  "ilmoituksenOhjaus": "announcementGuidance",
  "markkinointikuvaus": "marketingDescription",
  "tyoKielet": "jobLanguages",
  "luokittelunNimi": "classificationName",
  "luokiteltuArvo": "classifiedValue",
  "ajokortti": "driverLicense",
  "vaaditutAjokorttiluokat": "requiredDriverLicenseCategories",
  "ajokortinLisatieto": "driverLicenseAdditionalInfo",
  "tyoAlkaaPvm": "jobStartDate",
  "kielitaito": "languageSkill",
  "kielitaidonTaso": "languageSkillLevel",
  "kielitaidonLisatieto": "languageSkillAdditionalInfo",
  "yrittajyys": "entrepreneurship",
  "franchising": "franchising",
  "franchisingSopimusaika": "franchisingContractPeriod",
  "omaRahoitusVaaditaan": "ownFundingRequired",
  "omaRahoitusLisatietoja": "ownFundingAdditionalInfo",
  "koulutusaste": "educationLevel",
  "virkasuhde": "civilService",
  "vuokratyoToimeksiantaja": "temporaryWorkCommissioner"
}