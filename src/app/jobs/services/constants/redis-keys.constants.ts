// KEYS FOR REDIS. TAKEN FROM OLD JOBS SERVICE
export const redisConstantKeys = {
  JOBS_IMPORT_SET: process.env.REDIS_KEY_JOBS_IMPORT_SET ?? 'jobs:posting:import', // SET of job IDs that are imported
  JOBS_IMPORT_STATUS: process.env.REDIS_KEY_JOBS_IMPORT_SET ?? 'jobs:posting:import:status', // Status of job import
  JOBS_IMPORT_LOCK: process.env.REDIS_KEY_JOBS_IMPORT_LOCK ?? 'jobs:posting:import' // Lock to not call twice
}