import { Injectable, Logger } from '@nestjs/common';
import { difference } from 'lodash';
import { <PERSON>ron } from '@nestjs/schedule';
import { CacheService } from '../../../cache/cache.service';
import { JobMarketApiClient } from './job-market-api.service';
import { JoblyApiClient } from './jobly-api.service';
import { remapObjectKeys } from './helpers/remap-object-keys';
import {
  IGetJobPostingsResponseEN,
  IJobMarketTranslatable,
} from './interfaces/job-market-response-En.interface';
import { JobPosting } from '../repository/interfaces/job-data.interface';
import { jobMarketResponseKeysHashTable } from './constants/job-market-keys-translations';
import { VertexTranslationsService } from '../../translation/application/vertex-translation.service';
import { JobsImportRepository } from '../repository/jobs-import.repository';
import { convertUpsertInput } from './helpers/job-data-to-entity.converter';
import { JoblyDataToEntityConverter } from './helpers/jobly-data-to-entity.converter';
import { EmployerService } from '../../employer/services/employer.service';
import { IClassification } from '../repository/interfaces/classification.interface';
import { JobSeniority } from '../../../entities/job-seniority.enum';
import { JoblyJob } from './interfaces/jobly-job.interface';
import { JobMetadataService } from './job-metadata.service';
import { jobMarketConstants } from './constants/job-market.constants';

interface IGetJobPostingsParams {
  categoryCode?: string;
  maxPages?: number;
  limit?: number;
  languages: string[];
}

@Injectable()
export class JobsImportService {
  private readonly logger = new Logger(JobsImportService.name);
  private readonly CACHE_PREFIX = 'job_import:';
  private readonly CACHE_TTL = 3600; // 1 hour
  private importCacheKey: string = 'import:processing';

  constructor(
    private readonly cache: CacheService,
    private readonly jobMarketApiClient: JobMarketApiClient,
    private readonly joblyApiClient: JoblyApiClient,
    private readonly translationService: VertexTranslationsService,
    private readonly jobsRepository: JobsImportRepository,
    private readonly employerService: EmployerService,
    private readonly joblyDataConverter: JoblyDataToEntityConverter,
    private readonly jobMetadataService: JobMetadataService,
  ) {}

  // TODO: fix cache
  async import(params: IGetJobPostingsParams) {
    // Check if we have a cached result for this import
    const cacheKey = `${this.CACHE_PREFIX}import`;

    try {
      // Add timeout to prevent the Redis call from hanging indefinitely
      const cachePromise = this.cache.get<boolean>(cacheKey);
      const timeoutPromise = new Promise<undefined>((_, reject) => {
        setTimeout(
          () => reject(new Error('Cache get operation timed out')),
          5000,
        );
      });

      const cachedResult = await Promise.race([cachePromise, timeoutPromise]);

      if (cachedResult !== undefined) {
        this.logger.log(`Using cached result for import`);
        return;
      }
    } catch (error) {
      this.logger.warn(
        `Cache error: ${error.message} - proceeding with import`,
      );
      // Continue with the import operation rather than failing
    }

    this.logger.log(`Importing jobs`);

    // First, update expired jobs to ensure we don't process them
    await this.jobsRepository.updateExpiredJobs();

    const { maxPages, categoryCode, languages, limit = 100 } = params;
    let [page, hasMorePages, processed] = [1, true, 0];
    const employerMap = new Map<string, number>();

    // This map will store classification data available during import
    // to be used for integrated processing
    const classificationMap: Record<string, IClassification> = {};

    try {
      // Check if we're already processing an import
      let isProcessing = false;
      try {
        const processingPromise = this.cache.get<boolean>(this.importCacheKey);
        const timeoutPromise = new Promise<undefined>((_, reject) => {
          setTimeout(
            () => reject(new Error('Cache get operation timed out')),
            5000,
          );
        });

        const result = await Promise.race([processingPromise, timeoutPromise]);
        isProcessing = result === true; // Ensure it's explicitly a boolean
      } catch (error) {
        this.logger.warn(
          `Cache error while checking processing status: ${error.message}`,
        );
        // Assume not processing if we can't access the cache
        isProcessing = false;
      }

      // If we're already processing, log and exit
      if (isProcessing) {
        this.logger.log(`Import already in progress, skipping`);
        // return;
      }

      // Set processing flag to prevent concurrent imports
      try {
        const setCachePromise = this.cache.set(this.importCacheKey, true);
        const timeoutPromise = new Promise<undefined>((_, reject) => {
          setTimeout(
            () => reject(new Error('Cache set operation timed out')),
            5000,
          );
        });

        await Promise.race([setCachePromise, timeoutPromise]);
      } catch (error) {
        this.logger.warn(
          `Cache error while setting processing flag: ${error.message}`,
        );
        // Continue with import even if we can't set the flag
      }

      console.info(`Import jobs started.`);

      do {
        const responseFi = await this.jobMarketApiClient.getJobPostings({
          language: jobMarketConstants.JobPostingLanguage.Finnish,
          employer: jobMarketConstants.EmployerTypes.COMPANY,
          jobContinuity: jobMarketConstants.ContinuityTypes.OpenEnded,
          workingTime: jobMarketConstants.WorkingHours.FullTime,
          ...(categoryCode ? { professionalGroup: categoryCode } : {}),
          amount: limit,
          page,
        });
        console.log(`Fetched jobs records: ${responseFi.ilmoitukset?.length}`);

        const mappedKeysResponse = remapObjectKeys<IGetJobPostingsResponseEN>(
          responseFi,
          jobMarketResponseKeysHashTable,
        );
        if (!mappedKeysResponse.jobs?.length) break;

        // Process employers first
        for (const job of mappedKeysResponse.jobs) {
          const employer =
            await this.employerService.createOrUpdateEmployerFromJobPosting(
              job,
            );
          if (employer.business_id) {
            employerMap.set(employer.business_id, employer.id);
          }
          console.log(
            `Processed employer: ${employer.name} (${employer.business_id}) -> ID: ${employer.id}`,
          );

          // Generate classification data during import
          // This is where you'd integrate with your classification service
          // For now, we'll create a basic structure using available data
          if (job.jobID) {
            classificationMap[job.jobID] =
              this.generateBasicClassification(job);
          }
        }

        const jobIds = mappedKeysResponse.jobs.map((job) => job.jobID);
        const existingJobIds = await this.jobsRepository.findByExtIds(jobIds);
        console.log(
          `Found ${existingJobIds.length} existing jobs that will skip translation`,
        );

        // Split jobs into existing (no translation needed) and new (need translation)
        const existingJobs = mappedKeysResponse.jobs.filter((job) =>
          existingJobIds.includes(job.jobID),
        );

        const newJobs = mappedKeysResponse.jobs.filter(
          (job) => !existingJobIds.includes(job.jobID),
        );

        // Process existing jobs as a batch
        if (existingJobs.length > 0) {
          const convertedExistingJobs = convertUpsertInput(
            existingJobs,
            employerMap,
          );

          // Create classification map for existing jobs
          const existingJobsClassification = {};
          existingJobs.forEach((job) => {
            if (job.jobID) {
              existingJobsClassification[job.jobID] =
                this.generateBasicClassification(job);
            }
          });

          // Save existing jobs
          // await this.jobsRepository.upsert(convertedExistingJobs, existingJobsClassification);
          processed += existingJobs.length;
          console.log(`Processed ${existingJobs.length} existing jobs`);
        }

        // Process new jobs in parallel batches of 10
        let successfullyTranslated = 0;

        // Process jobs in batches
        for (let i = 0; i < newJobs.length; i += 10) {
          // Get current batch (up to 10 jobs)
          const currentBatch = newJobs.slice(i, i + 10);

          // Create an array of promises for this batch
          const batchPromises = currentBatch.map(async (job) => {
            try {
              // Translate this job
              const translatedJob = await this.translateJob(job, languages);

              // Convert single job for saving
              const convertedJob = convertUpsertInput(
                [translatedJob],
                employerMap,
              );

              // Create classification for this job
              const jobClassification = {};
              if (job.jobID) {
                jobClassification[job.jobID] =
                  this.generateBasicClassification(job);
              }

              // Save immediately after translation
              await this.jobsRepository.upsert(convertedJob, jobClassification);

              successfullyTranslated++;
              processed++;

              console.log(
                `Translated and saved job ID: ${job.jobID} (${successfullyTranslated}/${newJobs.length} new jobs)`,
              );

              return { success: true, jobId: job.jobID };
            } catch (error) {
              // If translation fails, log and continue to next job
              this.logger.error(
                `Error translating job ID: ${job.jobID}: ${error.message}`,
                error.stack,
              );
              return { success: false, jobId: job.jobID, error };
            }
          });

          // Wait for all jobs in this batch to complete (successfully or with error)
          const batchResults = await Promise.all(batchPromises);

          // Summary of this batch
          const successCount = batchResults.filter((r) => r.success).length;
          const failCount = batchResults.length - successCount;

          if (failCount > 0) {
            this.logger.warn(
              `Batch ${Math.floor(i / 10) + 1}: ${successCount} succeeded, ${failCount} failed`,
            );

            // If we're hitting rate limits consistently, we might want to add a delay
            if (failCount >= 5) {
              const delayMs = 10000; // 10 seconds
              this.logger.warn(
                `Multiple failures detected, waiting ${delayMs / 1000}s before next batch...`,
              );
              await new Promise((resolve) => setTimeout(resolve, delayMs));
            }
          }
        }

        page++;
        if (maxPages && page > maxPages) hasMorePages = false;
        if (mappedKeysResponse.numberOfAnnouncements < limit)
          hasMorePages = false;

        console.info(
          `Page ${page}: Processed ${processed} jobs (${successfullyTranslated} newly translated)`,
        );
      } while (hasMorePages);
    } finally {
      // Mark import as completed
      try {
        const deleteCachePromise = this.cache.delete(this.importCacheKey);
        const timeoutPromise = new Promise<undefined>((_, reject) => {
          setTimeout(
            () => reject(new Error('Cache delete operation timed out')),
            5000,
          );
        });

        await Promise.race([deleteCachePromise, timeoutPromise]);

        const setCachePromise = this.cache.set(cacheKey, true, this.CACHE_TTL);
        const setTimeoutPromise = new Promise<undefined>((_, reject) => {
          setTimeout(
            () => reject(new Error('Cache set operation timed out')),
            5000,
          );
        });

        await Promise.race([setCachePromise, setTimeoutPromise]);
      } catch (error) {
        this.logger.warn(
          `Cache error while completing import: ${error.message}`,
        );
        // Import completed successfully even if we can't update cache state
      }
    }

    return {
      processed,
      success: true,
    };
  }

  private async addTranslation(
    responseFi: IGetJobPostingsResponseEN,
    langCodes: string[],
  ): Promise<IGetJobPostingsResponseEN> {
    const responseEn = remapObjectKeys<IGetJobPostingsResponseEN>(
      responseFi,
      jobMarketResponseKeysHashTable,
    );

    const translatedJobs = responseEn.jobs.map(async (item) => {
      return this.translateJob(item, langCodes);
    });

    return { ...responseEn, jobs: await Promise.all(translatedJobs) };
  }

  private async translateJob(
    job: JobPosting,
    langCodes: string[],
  ): Promise<JobPosting> {
    const { jobTitle: titles, jobDescription: descriptions } =
      job.basicInformation;

    const titleTranslations = await this.translateMissing(titles, langCodes);
    const descTranslations = await this.translateMissing(
      descriptions,
      langCodes,
    );

    return {
      ...job,
      basicInformation: {
        ...job.basicInformation,
        jobTitle: titleTranslations,
        jobDescription: descTranslations,
      },
    };
  }

  private async translateMissing(
    data: IJobMarketTranslatable[],
    langCodes: string[],
  ) {
    const dataLangCodes = data.map((item) => item.languageCode);

    const missingLangCodes = difference(
      langCodes.map((lang) => lang.toLowerCase()),
      dataLangCodes.map((lang) => lang.toLowerCase()),
    );
    if (!missingLangCodes.length) return data;

    const [{ languageCode: source, value: text }] = data;

    const translationPromises = missingLangCodes.map(async (langCode) => ({
      languageCode: langCode,
      value: await this.translationService.translate(text, {
        source,
        target: langCode,
      }),
    }));

    const translations = await Promise.all(translationPromises);

    return [...data, ...translations];
  }

  /**
   * Import jobs from Jobly API
   * @param languagesToTranslateTo Languages to translate job data to
   * @returns Import results
   */
  async importFromJobly(
    languagesToTranslateTo: string[] = ['en'],
    limitJobs?: number, // Optional parameter to limit jobs for testing
  ): Promise<{ processed: number; success: boolean }> {
    this.logger.log('Starting Jobly job import');

    // Get the cache keys from the Jobly API client
    const importCacheKey = this.joblyApiClient.getCacheKey();
    const importCompletedCacheKey = this.joblyApiClient.getCompletedCacheKey();
    const cacheTtl = this.joblyApiClient.getCacheTtl();

    // Check if we're already processing an import
    try {
      const isProcessing = await this.cache.get<boolean>(importCacheKey);
      if (isProcessing) {
        this.logger.log('Jobly import already in progress, skipping');
        return { processed: 0, success: false };
      }

      // Set processing flag to prevent concurrent imports
      await this.cache.set(importCacheKey, true);
    } catch (error) {
      this.logger.warn(
        `Cache error while checking/setting processing flag: ${error.message}`,
      );
      // Continue with import even if we can't set the flag
    }

    try {
      // First, update expired jobs to ensure we don't process them
      await this.jobsRepository.updateExpiredJobs();

      // Fetch jobs from Jobly API
      this.logger.log('Fetching jobs from Jobly API');
      let joblyJobs = await this.joblyApiClient.getJobPostings();

      // Limit jobs for testing if specified
      if (limitJobs && limitJobs > 0) {
        joblyJobs = joblyJobs.slice(0, limitJobs);
        this.logger.log(
          `Limited to ${limitJobs} jobs for testing. Processing ${joblyJobs.length} jobs from Jobly API`,
        );
      } else {
        this.logger.log(`Fetched ${joblyJobs.length} jobs from Jobly API`);
      }

      // Create a list of the current job IDs
      const currentJoblyExtIds = joblyJobs.map((job) => job.id.toString());

      // Mark jobs that are no longer in the feed as expired
      const expiredCount = await this.jobsRepository.markSourceJobsAsExpired(
        currentJoblyExtIds,
        'Jobly',
      );
      this.logger.log(`Marked ${expiredCount} Jobly jobs as expired`);

      // Process employers first - in parallel for better performance
      this.logger.log('Processing employers from Jobly jobs in parallel');
      const employerMap = new Map<string, number>();

      // Get unique companies to avoid duplicate processing
      const uniqueCompanies = Array.from(
        new Set(joblyJobs.map((job) => job.company)),
      );
      this.logger.log(
        `Found ${uniqueCompanies.length} unique companies to process`,
      );

      // Process employers in parallel batches of 10
      const batchSize = 10;
      for (let i = 0; i < uniqueCompanies.length; i += batchSize) {
        const batch = uniqueCompanies.slice(i, i + batchSize);

        // Create promises for this batch
        const batchPromises = batch.map(async (companyName) => {
          try {
            // Find the first job for this company to get employer data
            const jobForCompany = joblyJobs.find(
              (job) => job.company === companyName,
            );
            if (!jobForCompany) {
              this.logger.warn(`No job found for company: ${companyName}`);
              return { success: false, companyName, error: 'No job found' };
            }

            this.logger.debug(`Processing employer: ${companyName}`);
            const employer =
              await this.employerService.createOrUpdateEmployerFromJobly(
                jobForCompany,
              );

            if (employer && employer.id) {
              // Store in map for all jobs from this company
              employerMap.set(companyName, employer.id);
              this.logger.debug(
                `Processed employer: ${companyName} (ID: ${employer.id})${jobForCompany.company_logo ? ' with logo' : ''}`,
              );
              return { success: true, companyName, employerId: employer.id };
            } else {
              this.logger.warn(
                `Failed to process employer: ${companyName} - No employer ID returned`,
              );
              return {
                success: false,
                companyName,
                error: 'No employer ID returned',
              };
            }
          } catch (error) {
            this.logger.error(
              `Error processing employer ${companyName}: ${error.message}`,
              error.stack,
            );
            return { success: false, companyName, error: error.message };
          }
        });

        // Wait for all employers in this batch to complete
        const batchResults = await Promise.all(batchPromises);

        // Log batch summary
        const successCount = batchResults.filter((r) => r.success).length;
        const failCount = batchResults.length - successCount;

        this.logger.log(
          `Employer batch ${Math.floor(i / batchSize) + 1}: ${successCount} succeeded, ${failCount} failed`,
        );

        // Add a small delay between batches to avoid overwhelming the database
        if (i + batchSize < uniqueCompanies.length) {
          await new Promise((resolve) => setTimeout(resolve, 100));
        }
      }

      this.logger.log(
        `Processed ${employerMap.size} unique employers in parallel`,
      );

      // Find existing job IDs to avoid unnecessary processing
      const jobIds = joblyJobs.map((job) => job.id.toString());
      const existingJobIds = await this.jobsRepository.findByExtIds(jobIds);
      // ... (rest of the code remains the same)
      this.logger.log(
        `Found ${existingJobIds.length} existing jobs in the database`,
      );

      // Translate jobly jobs to English if needed
      this.logger.log('Translating Jobly jobs');
      const translatedJobs = await this.translateJoblyJobs(
        joblyJobs,
        languagesToTranslateTo,
      );
      this.logger.log(`Translated ${translatedJobs.length} Jobly jobs`);

      // Convert to the format required for upserting
      this.logger.log('Converting Jobly jobs to database format');
      const convertedData =
        await this.joblyDataConverter.convertJoblyUpsertInput(
          translatedJobs,
          employerMap,
        );

      // Create classification data for each job in parallel
      this.logger.log('Generating classifications for Jobly jobs');
      const classificationsMap: Record<string, IClassification> = {};

      // Process classifications in parallel batches of 20 (lightweight operation)
      const classificationBatchSize = 20;
      for (let i = 0; i < translatedJobs.length; i += classificationBatchSize) {
        const batch = translatedJobs.slice(i, i + classificationBatchSize);

        // Create promises for this batch
        const batchPromises = batch.map(async (joblyJob) => {
          const jobId = joblyJob.id.toString();
          const classification = this.generateBasicClassification(joblyJob);
          return { jobId, classification };
        });

        // Wait for all classifications in this batch to complete
        const batchResults = await Promise.all(batchPromises);

        // Add results to the map
        batchResults.forEach(({ jobId, classification }) => {
          classificationsMap[jobId] = classification;
        });

        this.logger.debug(
          `Classification batch ${Math.floor(i / classificationBatchSize) + 1}: Generated ${batchResults.length} classifications`,
        );
      }
      this.logger.log(
        `Generated ${Object.keys(classificationsMap).length} job classifications`,
      );

      // Save to database
      this.logger.log('Saving Jobly jobs to database');
      await this.jobsRepository.upsert(convertedData, classificationsMap);

      // Extract additional metadata using AI for each job
      this.logger.log('Starting metadata extraction for Jobly jobs');

      // Get all inserted/updated jobs from the database
      const savedJobs = await this.jobsRepository.findJobsByExtIds(
        joblyJobs.map((job) => job.id.toString()),
      );

      // Process metadata for each job in parallel batches
      const metadataBatchSize = 5; // Smaller batch size for AI processing to avoid rate limits
      for (let i = 0; i < savedJobs.length; i += metadataBatchSize) {
        const batch = savedJobs.slice(i, i + metadataBatchSize);

        // Create promises for this batch
        const batchPromises = batch.map(async (job) => {
          try {
            // Find the original jobly job with translations
            const joblyJob = translatedJobs.find(
              (j) => j.id.toString() === job.ext_id,
            );

            if (joblyJob) {
              // For Jobly jobs, we always use the original description and title
              // The translations are handled separately in the translation process
              const description = joblyJob.description;
              const title = joblyJob.title;

              // Extract and save metadata using AI
              await this.jobMetadataService.extractAndSaveMetadata(
                job,
                description,
                title,
                'en',
              );

              this.logger.debug(
                `Metadata extraction completed for job ${job.ext_id}`,
              );
              return { success: true, jobId: job.ext_id };
            } else {
              this.logger.warn(
                `No matching Jobly job found for job ${job.ext_id}`,
              );
              return {
                success: false,
                jobId: job.ext_id,
                error: 'No matching Jobly job found',
              };
            }
          } catch (metadataError) {
            this.logger.error(
              `Error extracting metadata for job ${job.ext_id}: ${metadataError.message}`,
              metadataError.stack,
            );
            return {
              success: false,
              jobId: job.ext_id,
              error: metadataError.message,
            };
          }
        });

        // Wait for all metadata extractions in this batch to complete
        const batchResults = await Promise.all(batchPromises);

        // Log batch summary
        const successCount = batchResults.filter((r) => r.success).length;
        const failCount = batchResults.length - successCount;

        this.logger.log(
          `Metadata extraction batch ${Math.floor(i / metadataBatchSize) + 1}: ${successCount} succeeded, ${failCount} failed`,
        );

        // Add delay between batches to respect AI service rate limits
        if (i + metadataBatchSize < savedJobs.length) {
          await new Promise((resolve) => setTimeout(resolve, 1000)); // 1 second delay for AI processing
        }
      }

      this.logger.log(`Successfully processed ${joblyJobs.length} Jobly jobs`);

      // Clear the import cache after successful import
      await this.clearJoblyImportCache();
      this.logger.log('Cleared Jobly import cache after successful import');

      return {
        processed: joblyJobs.length,
        success: true,
      };
    } catch (error) {
      this.logger.error(
        `Error importing from Jobly: ${error.message}`,
        error.stack,
      );
      return {
        processed: 0,
        success: false,
      };
    } finally {
      // Clear the processing flag
      try {
        await this.cache.delete(importCacheKey);
        await this.cache.set(importCompletedCacheKey, true, cacheTtl);
      } catch (error) {
        this.logger.warn(
          `Cache error while completing import: ${error.message}`,
        );
      }
    }
  }

  /**
   * Clear Jobly import cache to allow new imports
   */
  async clearJoblyImportCache(): Promise<void> {
    const importCacheKey = this.joblyApiClient.getCacheKey();
    const importCompletedCacheKey = this.joblyApiClient.getCompletedCacheKey();

    try {
      await this.cache.delete(importCacheKey);
      await this.cache.delete(importCompletedCacheKey);
      this.logger.log('Jobly import cache cleared successfully');
    } catch (error) {
      this.logger.error(
        `Error clearing Jobly import cache: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Translate Jobly jobs to the required languages
   * Currently we only translate to English for simplicity
   */
  private async translateJoblyJobs(
    joblyJobs: JoblyJob[],
    languagesToTranslateTo: string[],
  ): Promise<JoblyJob[]> {
    // If English is not in the list, add it since we always need English
    if (!languagesToTranslateTo.includes('en')) {
      languagesToTranslateTo.push('en');
    }

    // Process jobs in batches of 5 for translation
    const translatedJobs: JoblyJob[] = [];

    for (let i = 0; i < joblyJobs.length; i += 5) {
      const batch = joblyJobs.slice(i, i + 5);
      const batchPromises = batch.map(async (job) => {
        try {
          // For the MVP, we'll only translate title and description to English
          // Assuming job is in Finnish
          const translatedTitle = await this.translationService.translate(
            job.title,
            {
              source: 'fi',
              target: 'en',
            },
          );

          const translatedDesc = await this.translationService.translate(
            job.description,
            {
              source: 'fi',
              target: 'en',
            },
          );

          // Return a new job object with the translations
          return {
            ...job,
            title_en: translatedTitle,
            description_en: translatedDesc,
          };
        } catch (error) {
          this.logger.error(
            `Error translating job ID ${job.id}: ${error.message}`,
          );
          // If translation fails, return the original job
          return {
            ...job,
            title_en: job.title, // Use original as fallback
            description_en: job.description, // Use original as fallback
          };
        }
      });

      // Wait for all translations in this batch and add them to the result
      const batchResults = await Promise.all(batchPromises);
      translatedJobs.push(...batchResults);

      // Add a small delay between batches to avoid rate limits
      if (i + 5 < joblyJobs.length) {
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    return translatedJobs;
  }

  /**
   * Generate basic classification data for any job type
   * Supports both Jobly jobs and job market API jobs
   */
  private generateBasicClassification(job: any): IClassification {
    let industry: string;
    let occupationCode: string;

    // Handle different job data structures
    if (job.industry && Array.isArray(job.industry)) {
      // Jobly job structure
      industry = job.industry.length > 0 ? job.industry[0] : 'FREELANCE';
      occupationCode = job.occupation?.length > 0 ? job.occupation[0] : '00000';
    } else {
      // Job market API structure
      industry = job.categories?.[0]?.code || '00';
      occupationCode = job.occupationCodes?.[0] || '00000';
    }

    // Extract skills from job description
    const skills: string[] = [];

    // For MVP, initialize empty arrays for new fields
    const primaryTasks: string[] = [];
    const keywords: string[] = [];
    const skillContexts: string[] = [];
    const expectedOutcomes: string[] = [];
    const possibleCareerOutcomes: string[] = [];
    const typicalDayTasks: string[] = [];
    const cultureKeywords: string[] = [];
    const idealCandidateTraits: string[] = [];
    const workLifeBalanceSignals: string[] = [];

    return {
      industry,
      occupation: occupationCode,
      skills,
      seniority: JobSeniority.NONE, // Use the JobSeniority enum
      primary_tasks: primaryTasks,
      keywords: keywords,
      skill_contexts: skillContexts,
      expected_outcomes: expectedOutcomes,
      possible_career_outcomes: possibleCareerOutcomes,
      typical_day_tasks: typicalDayTasks,
      culture_keywords: cultureKeywords,
      ideal_candidate_traits: idealCandidateTraits,
      work_life_balance_signals: workLifeBalanceSignals,
      role_impact_summary: {
        role_impact_summary: '',
        collaboration_focus: undefined,
        hiring_urgency_hint: false,
        is_career_switcher_friendly: false,
        is_student_friendly: false,
        learning_opportunity_emphasis: false,
        travel_required: undefined,
        work_pace: undefined,
      },
      internship: false,
      contacts: {
        phone: [],
        email: [],
        links: [],
        other: [],
      },
      requirements: {
        languages: [],
        certifications: [],
      },
    };
  }

  /**
   * Scheduled job to import from Jobly every hour
   * Runs at minute 15 of every hour (e.g., 1:15, 2:15, etc.)
   * DISABLED IN PRODUCTION ENVIRONMENT
   */
  @Cron('0 15 * * * *')
  async scheduledJoblyImport() {
    // Disable Jobly import in production
    if (process.env.NODE_ENV === 'production') {
      this.logger.log('Scheduled Jobly import is disabled in production environment');
      return;
    }
    
    this.logger.log('Running scheduled Jobly import');
    try {
      const result = await this.importFromJobly(['en']);
      this.logger.log(
        `Scheduled Jobly import completed: ${result.processed} jobs processed`,
      );
    } catch (error) {
      this.logger.error(
        `Error in scheduled Jobly import: ${error.message}`,
        error.stack,
      );
    }
  }
}
