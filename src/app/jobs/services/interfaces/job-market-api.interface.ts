export interface JobPostingParams {
    page: number;
    amount: number;
    country?: string;
    language?: string;
    employer?: string;
    jobContinuity?: string;
    workingTime?: string;
    professionalGroup?: string;
}

export interface AccessToken {
    access_token: string;
    expires_on: number;
    token_type: string;
}

export interface ApiError extends Error {
    statusCode?: number;
    retryable: boolean;
}

export interface ApiConfig {
    baseUrl: string;
    tokenEndpoint: string;
    clientId: string;
    clientSecret: string;
    scope: string;
    proxyUrl?: string;
    timeout?: number;
    maxRetries?: number;
    rateLimit?: number;
    rateWindow?: number;
    socksProxy?: string;
}