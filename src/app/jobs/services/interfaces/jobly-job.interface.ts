/**
 * Interface representing a job posting from the Jobly API
 */
export interface JoblyJob {
  id: number;
  publish_date: string;  // Format: "YYYY-MM-DD"
  title: string;
  city: string;
  region: string;
  country: string;
  country_code_alpha_2: string;  // ISO 3166-1 alpha-2 country code
  description: string;
  url: string;  // Direct URL to the job posting
  employment_type: string[];  // e.g., ["<PERSON><PERSON><PERSON><PERSON>", "Kokopäiväinen"]
  occupation: string[];  // Job categories
  industry: string[];  // Industry categories (empty in example)
  company: string;  // Company name
  company_logo: string;  // URL to company logo
}