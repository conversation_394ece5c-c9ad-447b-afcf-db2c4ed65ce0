export interface KieliArvo {
  kielikoodi: string;
  arvo: string;
}

export interface PerusTiedot {
  tyonOtsikko: KieliArvo[];
  tyonTiivistelma: KieliArvo[];
  tyonKuvaus: KieliArvo[];
  paikkojenMaara: number;
  palvelussuhde: {
    tyyppi: string;
    tyosuhde: {
      vuokratyo: boolean;
      rekrytointiToimeksianto: boolean;
      tyoharjoittelu: boolean;
      oppisopimus: boolean;
      vuorotteluvapaanSijaisuus: boolean;
    }
  };
  tyonJatkuvuus: string;
  tyoAika: string;
  tyoTunnitAjanjakso: string;
  kutsutaanTarvittaessa: boolean;
  palkanPeruste: string;
  palkanLisatieto: KieliArvo[];
  tyoAlkaa: string;
  tyoAlkaaLisatieto: KieliArvo[];
  maaraaikaisuudenSyy: KieliArvo[];
  tyoskentely: {
    tyoskentelyAika: string[];
    vuorotyo: string[];
  };
  kuuluuMatkustamista: boolean;
}

export interface JobPostingFI {
  ilmoituksenID: string;
  tyollistaja: string;
  ilmoittajanYTunnus: string;
  ilmoittajanNimi: KieliArvo[];
  luontipvm: Date;
  muokattupvm: Date;
  julkaisupvm: string;
  ilmoituksenKielet: string[];
  perustiedot: PerusTiedot;
  osaamisvaatimukset: {
    ammatit: string[];
    osaamiset: string[];
    kielitaidot: string[];
    kortitJaLuvat: {
      lupaKoodit: string[];
      kortitJaLuvatLisatieto: string[];
    };
    rikosrekisteriote: boolean;
  };
  sijainti: {
    sijaintiJoustava: boolean;
    maa: string[];
    maakunta: string[];
    kunta: string[];
    toimipaikka: {
      toimipaikanNimi: KieliArvo[];
      postiosoite: string;
      postinumero: string;
      postitoimipaikka: string;
    };
  };
  hakeminen: any;
  ilmoituksenOhjaus: boolean;
  markkinointikuvaus: any;
  tyoKielet: {
    ilmoittajanYhteystiedot: any;
    hakuohjeet: KieliArvo[];
  };
}

export interface IGetJobPostingsResponseFI {
  sivutus: {
    sivu: number,
    maara: number
  };
  ilmoituksienMaara: number;
  ilmoitukset: JobPostingFI[];
}