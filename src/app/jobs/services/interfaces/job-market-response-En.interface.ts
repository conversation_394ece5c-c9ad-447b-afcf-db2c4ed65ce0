export interface IClassifiedItem {
  classificationName: string;
  classifiedValue: string;
}

export interface IJobMarketTranslatable {
  languageCode: string;
  value: string;
}

export interface JobPostingEN {
  jobID: string;
  employer: string;
  employerBusinessID: string;
  employerName: IJobMarketTranslatable[];
  creationDate: string;
  modificationDate: string;
  publicationDate: string;
  announcementLanguages: string[];
  basicInformation: {
    jobTitle: IJobMarketTranslatable[];
    jobSummary: IJobMarketTranslatable[];
    jobDescription: IJobMarketTranslatable[];
    numberOfPositions: number;
    employmentRelation: {
      type: string;
      employment: {
        temporaryWork: boolean;
        recruitmentAssignment: boolean;
        internship: boolean;
        apprenticeship: boolean;
        substituteForJobAlternationLeave: boolean;
      };
    };
    jobContinuity: string;
    workingTime: string;
    onCall: boolean;
    salaryBasis: string;
    salaryAdditionalInfo: IJobMarketTranslatable[];
    jobStarts: string;
    jobStartDate: string;
    jobStartsAdditionalInfo: IJobMarketTranslatable[];
    working: {
      workingHours: any[];
      shiftWork: any[];
    };
    includesTravel: boolean;
  };
  competenceRequirements: {
    professions: IClassifiedItem[];
    skills: IClassifiedItem[];
    languageSkills: {
      languageSkill: string;
      languageSkillLevel: string;
      languageSkillAdditionalInfo: [];
    }[];
    cardsAndPermits: {
      permitCodes: string[];
      cardsAndPermitsAdditionalInfo: IJobMarketTranslatable[];
    };
    criminalRecord: boolean;
  };
  location: {
    flexibleLocation: boolean;
    country: string[];
    region: string[];
    municipality: string[];
    workplace: {
      workplaceName: any[];
      postalAddress: string;
      postalCode: string;
      postOffice: string;
    };
  };
  application: {
    applicationDeadline: string;
    employerContactDetails: {
      firstName: string;
      lastName: string;
      email: string;
      phoneNumber: string;
    }[];
    applicationUrls: any[];
    applicationInstructions: IJobMarketTranslatable[];
  };
  announcementGuidance: boolean;
  marketingDescription: any[];
  jobLanguages: string[];
}

export interface IGetJobPostingsResponseEN {
  pagination: {
    page: number;
    amount: number;
  };
  numberOfAnnouncements: number;
  jobs: JobPostingEN[];
}
