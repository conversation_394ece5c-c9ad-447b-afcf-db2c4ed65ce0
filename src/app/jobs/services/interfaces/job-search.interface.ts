export interface JobSearchParams {
  smart_profile?: any;
  query?: string;
  keywords?: string;
  page?: number;
  limit?: number;
  offset?: number; // Added for compatibility with API spec
  user_id?: string; // For filtering user applications
  sort_by?: string; // For sorting results
  sort_direction?: 'asc' | 'desc'; // Sort direction
  top20?: boolean;
  useCache?: boolean; // Whether to use cached full results

  // Location filters - allow single string or array of strings
  municipality_names?: string | string[];
  municipality_codes?: string | string[];
  region_names?: string | string[];
  region_codes?: string | string[];
  country?: string | string[]; // Assuming country could also be an array

  [key: string]: any;
}

export interface JobSearchResult {
  jobs: EnrichedJobData[];
  meta: {
    total: number;
    page: number;
    limit: number;
    has_next: boolean;
  };
}

export interface EnrichedJobData {
  id: string;
  title: string;
  rankingScore?: number;
  employer: {
    id: number;
    name: string;
  };
  location: {
    city: string;
    region: string;
    country: string;
    isRemote: boolean;
  };
  employment: {
    working_time: string;
    working_hours: string;
    continuity: string;
  };
  timing: {
    published_date: string;
    expires_at: string;
    is_new: boolean;
    closing_soon: boolean;
  };
  salary: {
    lower_bound: number;
    upper_bound: number;
    has_salary: boolean;
  };
  application: string | null;
  summary: {
    elevator_pitch: string;
    short_description: string;
    primary_tasks: string | string[];
  };
  skills: string[];
  skill_contexts: string[];
  languages: string[];
  industry: string;
  occupation: string;
  seniority: string;
  source: string;
  market_salary?: {
    job_role: string;
    median_salary: number;
    salary_lower_bound: number;
    salary_upper_bound: number;
    currency: string;
    salary_period: string;
    data_source: string;
    data_reliability: string;
  };
  evolution?: {
    evolution_summary: string;
    automation_impact: string;
    relevance_prediction: string;
  };
  ranking_score?: number;
  
  // User interaction fields
  is_viewed?: boolean;
  is_liked?: boolean;
  classification_data?: Record<string, any>;
}