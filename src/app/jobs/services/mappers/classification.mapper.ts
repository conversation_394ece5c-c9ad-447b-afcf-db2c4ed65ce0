import { IClassification } from '../../repository/interfaces/classification.interface';
import { JobSeniority } from 'src/entities/job-seniority.enum';

/**
 * Maps the API response to the IClassification interface
 * @param response The raw API response
 * @returns A properly formatted IClassification object
 */
export function mapResponseToClassification(response: any): IClassification {
  const { result } = response;
  
  // Get occupations if available (up to 3)
  const occupations = result.occupations && Array.isArray(result.occupations) 
    ? result.occupations.map(o => o.toString()) 
    : [];
  
  // Primary occupation (first in list or empty)
  const occupation = occupations.length > 0 ? occupations[0] : '';
  
  // Secondary occupation (second in list or empty)
  const occupation_secondary = occupations.length > 1 ? occupations[1] : '';
  
  // Tertiary occupation (third in list or empty)
  const occupation_tertiary = occupations.length > 2 ? occupations[2] : '';
  
  // Map seniority to our enum format
  const seniority = mapSeniority(result.seniority.seniority_level);
  
  // Extract arrays, prioritizing nested structures over top-level ones
  // For each field, check nested structure first, then fall back to top-level if not available
  
  // Skills - prioritize data from keywords object
  const skills = (result.keywords?.skills && Array.isArray(result.keywords.skills)) 
    ? result.keywords.skills
    : (Array.isArray(result.skills) ? result.skills : []);
  
  // Primary tasks - prioritize nested data in expected_outcomes
  const primary_tasks = Array.isArray(result.expected_outcomes?.primary_tasks) 
    ? result.expected_outcomes.primary_tasks 
    : (Array.isArray(result.primary_tasks) ? result.primary_tasks : []);
  
  // Keywords - prioritize data from keywords object
  const keywords = (result.keywords?.keywords && Array.isArray(result.keywords.keywords))
    ? result.keywords.keywords
    : (Array.isArray(result.keywords) ? result.keywords : []);
  
  // Skill contexts - prioritize data from keywords object
  const skill_contexts = (result.keywords?.skill_contexts && Array.isArray(result.keywords.skill_contexts))
    ? result.keywords.skill_contexts
    : (Array.isArray(result.skill_contexts) ? result.skill_contexts : []);
  
  // Expected outcomes - prioritize nested structure
  const expected_outcomes = Array.isArray(result.expected_outcomes?.expected_outcomes) 
    ? result.expected_outcomes.expected_outcomes 
    : (Array.isArray(result.expected_outcomes) ? result.expected_outcomes : []);
  
  // Career outcomes - prioritize nested structure in culture_keywords
  const possible_career_outcomes = Array.isArray(result.culture_keywords?.possible_career_outcomes) 
    ? result.culture_keywords.possible_career_outcomes 
    : (Array.isArray(result.possible_career_outcomes) ? result.possible_career_outcomes : []);
  
  // Typical day tasks - prioritize nested structure in expected_outcomes
  const typical_day_tasks = Array.isArray(result.expected_outcomes?.typical_day_tasks) 
    ? result.expected_outcomes.typical_day_tasks 
    : (Array.isArray(result.typical_day_tasks) ? result.typical_day_tasks : []);
  
  // Culture keywords - prioritize nested structure
  const culture_keywords = Array.isArray(result.culture_keywords?.culture_keywords) 
    ? result.culture_keywords.culture_keywords 
    : (Array.isArray(result.culture_keywords) ? result.culture_keywords : []);
  
  // Candidate traits - prioritize nested structure in culture_keywords
  const ideal_candidate_traits = Array.isArray(result.culture_keywords?.ideal_candidate_traits) 
    ? result.culture_keywords.ideal_candidate_traits 
    : (Array.isArray(result.ideal_candidate_traits) ? result.ideal_candidate_traits : []);
  
  // Work-life balance - prioritize nested structure in culture_keywords
  const work_life_balance_signals = Array.isArray(result.culture_keywords?.work_life_balance_signals) 
    ? result.culture_keywords.work_life_balance_signals 
    : (Array.isArray(result.work_life_balance_signals) ? result.work_life_balance_signals : []);
  
  // Role impact summary - extract as complete object with all fields
  let roleImpactText = result.role_impact_summary?.role_impact_summary || result.role_impact_summary || '';
  if(roleImpactText === '' && (result.role_impact_summary_aux?.role_impact_summary || result.role_impact_summary_aux)) {
    roleImpactText = result.role_impact_summary_aux?.role_impact_summary || result.role_impact_summary_aux || '';
  }
  
  // Create the full role impact summary object with all the new fields
  const role_impact_summary = {
    role_impact_summary: roleImpactText,
    collaboration_focus: result.role_impact_summary?.collaboration_focus || null,
    hiring_urgency_hint: result.role_impact_summary?.hiring_urgency_hint || false,
    is_career_switcher_friendly: result.role_impact_summary?.is_career_switcher_friendly || false,
    is_student_friendly: result.role_impact_summary?.is_student_friendly || false,
    learning_opportunity_emphasis: result.role_impact_summary?.learning_opportunity_emphasis || false,
    travel_required: result.role_impact_summary?.travel_required || null,
    work_pace: result.role_impact_summary?.work_pace || null
  };

  // Salary information - use either existing structure or build from separate fields
  const salary = result.salary || {
    salary_lower_bound: result.salary_lower || 0,
    salary_upper_bound: result.salary_upper || 0,
    period: result.salary_period || 'none'
  };

  // IS this an Internship position?
  const internship = result.salary ? result.salary.internship : false;

  // Market salary - handle nested or top-level structure
  const market_salary = result.market_salary || undefined;

  // Evolution - handle nested or top-level structure
  const evolution = result.evolution || undefined;

  // Contacts - handle nested or top-level structure
  const contacts = result.contacts || undefined;

  // Requirements - handle nested or top-level structure
  const requirements = result.requirements || undefined;

  return {
    industry: result.seniority?.industry_code ? result.seniority.industry_code : 'FREELANCE',
    occupation,
    occupation_secondary,
    occupation_tertiary,
    seniority,
    skills,
    salary,
    elevator_pitch: result.seniority.elevator_pitch || '',
    primary_tasks,
    keywords,
    skill_contexts,
    expected_outcomes,
    possible_career_outcomes,
    typical_day_tasks,
    culture_keywords,
    ideal_candidate_traits,
    work_life_balance_signals,
    role_impact_summary,
    market_salary,
    evolution,
    internship,
    contacts,
    requirements
  };
}

/**
 * Maps the API seniority value to our JobSeniority enum format
 * @param seniority The seniority value from the API
 * @returns The corresponding JobSeniority enum value
 */
function mapSeniority(seniority: string): string {
  if (!seniority) return JobSeniority.NONE;
  
  // API returns values like "SENIOR" - we'll map them to our enum
  switch (seniority.toUpperCase()) {
    case 'ENTRY':
      return JobSeniority.ENTRY;
    case 'JUNIOR':
      return JobSeniority.JUNIOR;
    case 'MID':
      return JobSeniority.MID;
    case 'SENIOR':
      return JobSeniority.SENIOR;
    case 'LEAD':
      return JobSeniority.LEAD;
    case 'MANAGERIAL':
      return JobSeniority.MANAGERIAL;
    case 'EXECUTIVE':
      return JobSeniority.EXECUTIVE;
    default:
      return JobSeniority.NONE;
  }
}
