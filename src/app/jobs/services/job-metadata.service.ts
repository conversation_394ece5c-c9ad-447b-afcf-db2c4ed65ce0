import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobMetadata } from '../../../entities/job-metadata.entity';
import { Job } from '../../../entities/job.entity';
import { GeminiService } from '../../../gemini/gemini.service';
import { JobMetadataDto } from '../../../gemini/dto/job-metadata.dto';

/**
 * Service for handling job metadata extraction and storage
 */
@Injectable()
export class JobMetadataService {
  private readonly logger = new Logger(JobMetadataService.name);
  private readonly CONFIDENCE_THRESHOLD = 0.7; // Minimum confidence score to update Job entity fields

  constructor(
    @InjectRepository(JobMetadata)
    private readonly jobMetadataRepository: Repository<JobMetadata>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
    private readonly geminiService: GeminiService,
  ) {}

  /**
   * Extracts and saves metadata from a job description
   * Updates Job entity fields with high-confidence values
   * Saves all metadata to the job_metadata table
   * 
   * @param job The job entity to update with metadata
   * @param description The job description to extract metadata from
   * @param title Optional job title for better extraction context
   * @param language Language of the job content (default: 'en')
   */
  async extractAndSaveMetadata(
    job: Job,
    description: string,
    title?: string,
    language: string = 'en',
  ): Promise<void> {
    try {
      this.logger.log(`Extracting metadata for job ${job.ext_id}`);
      
      // Use the job title if provided, otherwise use the job's title field
      const jobTitle = title || job.title || 'Job Posting';
      
      // Extract metadata using Gemini
      const metadata = await this.geminiService.extractJobMetadata(
        jobTitle,
        description,
        language,
      );
      
      // First, update the Job entity fields with high-confidence values
      await this.updateJobFields(job, metadata);
      
      // Then, save all extracted metadata to the job_metadata table
      await this.saveMetadataEntries(job.ext_id, metadata);
      
      this.logger.log(`Successfully processed metadata for job ${job.ext_id}`);
    } catch (error) {
      this.logger.error(
        `Error extracting metadata for job ${job.ext_id}: ${error.message}`,
        error.stack,
      );
      // Don't rethrow - we don't want to break the import process if metadata extraction fails
    }
  }
  
  /**
   * Updates Job entity fields with extracted metadata
   * Only updates fields if confidence score exceeds threshold
   * 
   * @param job The job entity to update
   * @param metadata The extracted metadata
   */
  private async updateJobFields(job: Job, metadata: JobMetadataDto): Promise<void> {
    const confidenceScores = metadata.confidence_scores || {};
    let hasUpdates = false;
    
    try {
      // Update application_url if confidence is high enough
      if (
        metadata.application_url &&
        (confidenceScores.application_url === undefined || 
         confidenceScores.application_url >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.application_url = metadata.application_url;
        hasUpdates = true;
      }
      
      // Update expires_at if confidence is high enough
      if (
        metadata.expires_at &&
        (confidenceScores.expires_at === undefined || 
         confidenceScores.expires_at >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.expires_at = new Date(metadata.expires_at);
        hasUpdates = true;
        this.logger.log(`Updating expires_at for job ${job.ext_id} to ${metadata.expires_at}`);
      }
      
      // Update workplace_flexibility if confidence is high enough
      if (
        metadata.workplace_flexibility &&
        (confidenceScores.workplace_flexibility === undefined || 
         confidenceScores.workplace_flexibility >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.workplace_flexibility = metadata.workplace_flexibility;
        hasUpdates = true;
      }
      
      // Update salary_type if confidence is high enough
      if (
        metadata.salary_type &&
        (confidenceScores.salary_type === undefined || 
         confidenceScores.salary_type >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.salary_type = metadata.salary_type;
        hasUpdates = true;
      }
      
      // Update shift_type if confidence is high enough
      if (
        metadata.shift_type &&
        (confidenceScores.shift_type === undefined || 
         confidenceScores.shift_type >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.shift_type = metadata.shift_type;
        hasUpdates = true;
      }
      
      // Update part_time_hours if confidence is high enough
      if (
        metadata.part_time_hours &&
        (confidenceScores.part_time_hours === undefined || 
         confidenceScores.part_time_hours >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.part_time_hours = metadata.part_time_hours;
        hasUpdates = true;
      }
      
      // Update work_begins if confidence is high enough
      if (
        metadata.work_begins &&
        (confidenceScores.work_begins === undefined || 
         confidenceScores.work_begins >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.work_begins = metadata.work_begins;
        hasUpdates = true;
      }
      
      // Update driver_license_codes if confidence is high enough
      if (
        metadata.driver_license_codes &&
        metadata.driver_license_codes.length > 0 &&
        (confidenceScores.driver_license_codes === undefined || 
         confidenceScores.driver_license_codes >= this.CONFIDENCE_THRESHOLD)
      ) {
        job.driver_license_codes = metadata.driver_license_codes;
        hasUpdates = true;
      }
      
      // Save the job if any updates were made
      if (hasUpdates) {
        await this.jobRepository.save(job);
        this.logger.log(
          `Saved updated job fields for job ${job.ext_id}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error updating job fields for ${job.ext_id}: ${error.message}`,
        error.stack,
      );
    }
  }
  
  /**
   * Saves all extracted metadata to the job_metadata table
   * 
   * @param extId The external ID of the job
   * @param metadata The extracted metadata
   */
  private async saveMetadataEntries(
    extId: string,
    metadata: JobMetadataDto,
  ): Promise<void> {
    try {
      // Create a batch of metadata entries to save
      const entries: Partial<JobMetadata>[] = [];
      
      // Process each field in the metadata object
      for (const [key, value] of Object.entries(metadata)) {
        // Skip confidence_scores as we'll include them in the individual entries
        if (key === 'confidence_scores' || value === undefined || value === null) {
          continue;
        }
        
        // Create metadata entry
        entries.push({
          ext_id: extId,
          key,
          value,
          source: 'ai_extraction',
        });
      }
      
      // Save to database
      if (entries.length > 0) {
        // Use upsert to handle duplicate key constraints
        await this.jobMetadataRepository.upsert(entries, {
          conflictPaths: ['ext_id', 'key'],
          skipUpdateIfNoValuesChanged: true,
        });
        this.logger.log(`Saved ${entries.length} metadata entries for job ${extId}`);
      }
    } catch (error) {
      this.logger.error(
        `Error saving metadata entries for job ${extId}: ${error.message}`,
        error.stack,
      );
    }
  }
  
  /**
   * Retrieves metadata for a specific job
   * 
   * @param extId External ID of the job
   * @returns Record of metadata key-value pairs
   */
  async getMetadata(extId: string): Promise<Record<string, any>> {
    try {
      const metadata = await this.jobMetadataRepository.find({
        where: { ext_id: extId },
      });
      
      // Convert to a key-value record
      return metadata.reduce((acc, item) => {
        acc[item.key] = item.value;
        return acc;
      }, {} as Record<string, any>);
    } catch (error) {
      this.logger.error(
        `Error retrieving metadata for job ${extId}: ${error.message}`,
        error.stack,
      );
      return {};
    }
  }
  
  /**
   * Retrieves a specific metadata field for a job
   * 
   * @param extId External ID of the job
   * @param key Metadata key to retrieve
   * @returns Value of the metadata field or undefined if not found
   */
  async getMetadataField(extId: string, key: string): Promise<any | undefined> {
    try {
      const metadata = await this.jobMetadataRepository.findOne({
        where: { ext_id: extId, key },
      });
      
      return metadata?.value;
    } catch (error) {
      this.logger.error(
        `Error retrieving metadata field ${key} for job ${extId}: ${error.message}`,
        error.stack,
      );
      return undefined;
    }
  }
}