import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { existsSync, mkdirSync, writeFileSync } from 'fs';
import { join } from 'path';

import { JobsImportRepository } from '../repository/jobs-import.repository';

@Injectable()
export class JobsExportService {
  private readonly logger = new Logger(JobsExportService.name);

  constructor(
    private readonly jobsRepository: JobsImportRepository,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Exports unprocessed jobs to a CSV file
   * @param batchSize Maximum number of jobs to include in the batch
   * @returns Path to the generated CSV file or empty string if no jobs to export
   */
  async exportUnprocessedJobsToCSV(batchSize = 1000): Promise<string> {
    // Generate version with timestamp
    const timestamp = new Date()
      .toISOString()
      .replace(/[^0-9]/g, '')
      .slice(0, 14);
    const filename = `job_batch_${timestamp}.csv`;

    // Get unprocessed jobs
    const unprocessedJobs =
      await this.jobsRepository.findUnprocessedJobs(batchSize);

    if (unprocessedJobs.length === 0) {
      this.logger.log('No unprocessed jobs found');
      return '';
    }

    // Create CSV content
    const csvHeader = 'employer,job_description,ext_id\n';
    const csvRows = unprocessedJobs
      .map((job) => {
        const enTranslation = job.translation.find(
          (t) => t.language?.toLowerCase() === 'en',
        );
        if (!enTranslation) return null;

        // Format CSV row and escape commas/quotes
        return `"${this.escapeCsv(job.employer_name || '')}","${this.escapeCsv(enTranslation.description || '')}","${job.ext_id}"`;
      })
      .filter(Boolean);

    const csvContent = csvHeader + csvRows.join('\n');

    // Write to file
    const exportPath = this.configService.get('EXPORT_PATH', './exports');
    if (!existsSync(exportPath)) {
      mkdirSync(exportPath, { recursive: true });
    }

    const filePath = join(exportPath, filename);
    writeFileSync(filePath, csvContent);

    // Mark jobs as queued in the database
    const jobIds = unprocessedJobs.map((job) => job.ext_id);
    await this.jobsRepository.markJobsAsQueued(jobIds);

    this.logger.log(`Exported ${csvRows.length} jobs to ${filePath}`);
    return filePath;
  }

  /**
   * Escapes special characters for CSV format
   */
  private escapeCsv(text: string): string {
    if (!text) return '';
    return text.replace(/"/g, '""');
  }
}
