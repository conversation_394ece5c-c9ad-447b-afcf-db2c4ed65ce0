import { Injectable, Logger } from '@nestjs/common';
import { JobsImportRepository } from '../repository/jobs-import.repository';
import { KeywordDataDto } from '../../react-app/dto/keywords-response.dto';
import {
  JobSearchParams,
  JobSearchResult,
} from './interfaces/job-search.interface';
import { KeywordsData } from '../../react-app/dto/smart-profile-search.dto';
import { ForYouRepository } from '../repository/for-you.repository';
import { UserApiService } from '../../../app/user/services/user-api.service';
import { RecommendationEngineService } from './recommendation-engine.service';
import {
  formatJobForResponse,
  formatJobsForResponse,
} from './helpers/job-data-to-entity.converter';
import {
  JobSearchCacheService,
  JobSearchQueryBuilder,
  JobSearchResultCache,
  JobSearchResultProcessor,
  JobSearchVectorService,
  PaginationFlowService,
  SearchContext,
} from './search';

@Injectable()
export class JobsSearchService {
  private readonly logger = new Logger(JobsSearchService.name);

  constructor(
    private readonly queryBuilder: JobSearchQueryBuilder,
    private readonly vectorService: JobSearchVectorService,
    private readonly resultProcessor: JobSearchResultProcessor,
    private readonly cacheService: JobSearchCacheService,
    private readonly resultCache: JobSearchResultCache,
    private readonly paginationFlow: PaginationFlowService,
    private readonly jobsRepository: JobsImportRepository,
    private readonly forYouRepository: ForYouRepository,
    private readonly userApiService: UserApiService,
    private readonly recommendationEngine: RecommendationEngineService,
  ) {}

  /**
   * Search for jobs with filtering and pagination
   * @param searchParams Search parameters including filters and pagination
   * @returns Job search results with metadata
   */
  async searchJobs(
    searchParams: JobSearchParams,
    keywords?: string,
    skill_contexts: string[] = [],
  ): Promise<JobSearchResult> {
    this.logger.log(
      `Searching for jobs with params: ${JSON.stringify(searchParams)}`,
    );

    try {
      // Build search context
      const searchContext = this.queryBuilder.buildSearchContext(
        searchParams,
        keywords,
        skill_contexts,
      );

      // Check if we should use direct database query instead of Pinecone
      // This happens when there are no search keywords/query AND no filters
      const hasNoSearchTerms = !searchContext.keywords && !searchContext.useVectorSearch;
      const hasNoFilters = this.hasNoUserFilters(searchContext.filters);
      
      if (hasNoSearchTerms && hasNoFilters) {
        this.logger.log('Using direct database query for unfiltered job browsing');
        return await this.getJobsDirectFromDatabase(searchContext);
      }

      // Check standard cache first for this specific page/limit combo
      const cachedResult = await this.cacheService.get(searchContext);
      if (cachedResult) {
        this.logger.debug('Returning cached search results for specific page');
        return cachedResult;
      }

      // Check for cached full results for paginated access if user ID is provided
      if (searchContext.userId && searchContext.useCache !== false) {
        const cachedPagedResults =
          await this.paginationFlow.getCachedPagedResults(searchContext);
        if (cachedPagedResults) {
          this.logger.debug('Returning paginated results from cache');

          // Also cache this specific page/limit combination for faster access next time
          await this.cacheService.set(searchContext, cachedPagedResults);

          return cachedPagedResults;
        }
      }

      // No cached results found, prepare to fetch from Pinecone
      let effectiveContext = searchContext;
      let originalPage = searchContext.pagination.page;
      let originalLimit = searchContext.pagination.limit;

      // If caching is enabled and we have a user ID, fetch more results at once for caching
      if (searchContext.userId && searchContext.useCache !== false) {
        effectiveContext =
          this.paginationFlow.getContextForFullResultsFetch(searchContext);
        this.logger.debug(
          `Expanded search context for full results fetch: page=${effectiveContext.pagination.page}, limit=${effectiveContext.pagination.limit}`,
        );
      }

      // Get the Pinecone index
      const index = await this.vectorService.getDefaultIndex();

      // Create filter object for Pinecone
      const pineconeFilters =
        this.queryBuilder.buildPineconeFilters(effectiveContext);

      // Generate query vector
      const queryVector =
        await this.vectorService.generateQueryVector(effectiveContext);

      // Build query options
      const queryOptions = this.queryBuilder.buildQueryOptions(
        effectiveContext,
        queryVector,
        pineconeFilters,
      );

      // Execute Pinecone query
      this.logger.log(
        `Querying Pinecone with filters: ${JSON.stringify(pineconeFilters)}`,
      );
      const queryResponse = await index.query(queryOptions);

      // Handle empty results
      if (!queryResponse.matches || queryResponse.matches.length === 0) {
        const emptyResult = {
          jobs: [],
          meta: {
            total: 0,
            page: originalPage,
            limit: originalLimit,
            has_next: false,
          },
        };

        // Cache empty results (with shorter TTL)
        await this.cacheService.set(searchContext, emptyResult, 300); // 5 minutes
        return emptyResult;
      }

      const sortedMatches = queryResponse.matches;
      let jobResults: { id: string; score: number; context: string }[] = [];

      // Process vector search results if applicable
      if (effectiveContext.useVectorSearch) {
        // Prepare documents for reranking
        const documents = sortedMatches.map((match) => ({
          id: match.id,
          skills: String(match.metadata!.skills), // Convert to string to ensure compatibility
          title: String(match.metadata!.title),
        }));

        // Perform reranking
        jobResults = await this.vectorService.rerankResults(
          effectiveContext,
          documents,
        );
      }

      // Extract just the IDs for fetching from the database
      const extIds = effectiveContext.useVectorSearch
        ? jobResults.map((result) => result.id)
        : sortedMatches.map((match) => match.id);

      // Log the number of IDs we're about to fetch
      this.logger.debug(`Fetching ${extIds.length} jobs from database`);

      // Fetch full job details with relations
      const jobs = await this.jobsRepository.findJobsWithDetails(extIds);

      // Log the actual number of jobs retrieved
      this.logger.debug(`Retrieved ${jobs.length} jobs from database`);

      // Create a map of job IDs to scores for easier lookup
      const scoreMap = new Map(
        jobResults.map((result) => [result.id, result.score]),
      );

      // If caching is enabled and we have a user ID, store full results for future pagination
      if (searchContext.userId && searchContext.useCache !== false) {
        await this.paginationFlow.storeResultsForPagination(
          searchContext,
          jobs,
          scoreMap,
        );

        // Restore original pagination settings for the current request
        searchContext.pagination.page = originalPage;
        searchContext.pagination.limit = originalLimit;
      }

      // For pagination, we need to slice the jobs to the requested page
      let jobsForCurrentPage = jobs;

      // If we expanded the search for caching, we need to get only the requested page
      if (searchContext !== effectiveContext) {
        const startIndex = (originalPage - 1) * originalLimit;
        const endIndex = Math.min(originalPage * originalLimit, jobs.length);
        jobsForCurrentPage = jobs.slice(startIndex, endIndex);
        this.logger.debug(
          `Sliced ${jobs.length} jobs to ${jobsForCurrentPage.length} for page ${originalPage}`,
        );
      }

      // Process results and format for response
      // IMPORTANT: Use the total number of jobs for meta.total, not just the current page
      const results = this.resultProcessor.processResults(
        searchContext,
        jobsForCurrentPage,
        scoreMap,
        jobs.length, // Use the FULL count for total
      );

      // Cache this specific page/limit combination
      await this.cacheService.set(searchContext, results);

      return results;
    } catch (error) {
      this.logger.error(
        `Error during job search: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get top matching jobs for a user based on their profile
   * @param searchParams Search parameters including smart profile and pagination
   * @returns Job search results with metadata
   */
  async getTopJobs(searchParams: JobSearchParams): Promise<JobSearchResult> {
    // For MVP, we'll implement a simplified version that just sorts by recency
    // In a future version, this would use vector similarity with the user profile
    const { smart_profile, ...restParams } = searchParams;
    const enhancedParams = {
      ...restParams,
      sort_by: 'score', // Default sort by published date for top jobs
      sort_direction: 'desc' as 'asc' | 'desc',
    };

    const keywordData = smart_profile as KeywordDataDto;
    const keyword = Array.isArray(keywordData?.keywords)
      ? keywordData.keywords.join(' OR ')
      : smart_profile;

    const initialRecommendations =
      await this.recommendationEngine.getRecommendations(
        enhancedParams,
        keyword,
        keywordData?.skill_contexts && keywordData.skill_contexts.length > 0
          ? keywordData.skill_contexts
          : typeof smart_profile === 'string' && smart_profile.trim() !== ''
            ? smart_profile.split(' OR ')
            : [],
      );
    return initialRecommendations;

    // return this.searchJobs(enhancedParams, keyword, keywordData.skill_contexts);
  }

  /**
   * Get jobs that the user has applied to
   * @param searchParams Search parameters including user_id and pagination
   * @returns Job search results with metadata
   */
  async getUserApplications(
    searchParams: JobSearchParams,
  ): Promise<JobSearchResult> {
    const { user_id } = searchParams;

    if (!user_id) {
      this.logger.warn('No user_id provided for applications search');
      return {
        jobs: [],
        meta: {
          total: 0,
          page: 1,
          limit: searchParams.limit || 20,
          has_next: false,
        },
      };
    }

    // For MVP, we'll add a simple filter for user_id
    // In a real implementation, this would query a user_applications table
    const enhancedParams = {
      ...searchParams,
      user_application_id: user_id,
    };

    return this.searchJobs(enhancedParams);
  }

  /**
   * Retrieve jobs by their IDs
   * @param jobIds Array of job IDs to retrieve
   * @returns Array of job objects with their details
   */
  async getJobsByIds(jobIds: number[]): Promise<any[]> {
    this.logger.log(`Retrieving jobs by IDs: ${jobIds.join(', ')}`);

    if (!jobIds.length) {
      return [];
    }

    try {
      // Retrieve jobs from the repository
      const jobs = await this.jobsRepository.findByIds(jobIds);

      // Format jobs for response
      return formatJobsForResponse(jobs);
    } catch (error) {
      this.logger.error(
        `Error retrieving jobs by IDs: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  async prepareForClassifier(extId: string, keywordData: KeywordsData) {
    try {
      const job = await this.jobsRepository.findByExtId(extId);

      // Check if job exists and has processed job data
      if (!job || !job.processedJob || !job.processedJob.keywords) {
        this.logger.warn(
          `Job with extId ${extId} not found or missing processed data`,
        );
        return {
          resume_keywords: '',
          job_description_keywords: '',
          job_tasks: '',
        };
      }

      // Process user keywords
      let keyword = '';
      try {
        if (
          Array.isArray(keywordData.skill_contexts) &&
          keywordData.skill_contexts.length > 0
        ) {
          keyword = keywordData.skill_contexts.join(', ');
          keyword += ' OR ';
        }

        if (
          Array.isArray(keywordData.keywords) &&
          keywordData.keywords.length > 0
        ) {
          keyword += keywordData.keywords.join(' OR ');
        }

        if (
          Array.isArray(keywordData.languages) &&
          keywordData.languages.length > 0
        ) {
          keyword += 'My languages skills are: ';
          keyword += keywordData.languages.join(', ');
        }
      } catch (keywordError) {
        this.logger.error(`Error processing keywords: ${keywordError.message}`);
        keyword = '';
      }

      return {
        resume_keywords: keyword,
        job_description_keywords: job.processedJob.keywords.join(' OR '),
        job_tasks: JSON.stringify(job.processedJob.primary_tasks), // For MVP, we'll use primary tasks as job tasks
      };
    } catch (error) {
      this.logger.error(
        `Error in prepareForClassifier for job ${extId}: ${error.message}`,
      );
      return {
        resume_keywords: '',
        job_description_keywords: '',
        job_tasks: '',
      };
    }
  }

  /**
   * Generate and persist "For You" job recommendations for a specific user
   * @param userId User ID to generate recommendations for
   * @param limit Number of top jobs to include in recommendations
   * @returns The list of recommended jobs
   */
  async forYou(userId: string, limit: number = 10): Promise<JobSearchResult> {
    this.logger.log(
      `Generating "For You" recommendations for user ${userId} with limit ${limit}`,
    );

    try {
      // Get user keyword data from user service
      const keywordsData =
        await this.userApiService.getUserKeywordsData(userId);

      if (!keywordsData || !keywordsData.data) {
        this.logger.warn(`No keyword data found for user ${userId}`);
        return {
          jobs: [],
          meta: {
            total: 0,
            page: 1,
            limit,
            has_next: false,
          },
        };
      }

      // Prepare search parameters for getTopJobs
      const searchParams: JobSearchParams = {
        smart_profile: keywordsData.data,
        limit,
        page: 1,
      };

      // Get top jobs based on the user's profile
      const topJobsResult = await this.getTopJobs(searchParams);

      if (!topJobsResult.jobs || topJobsResult.jobs.length === 0) {
        this.logger.warn(`No top jobs found for user ${userId}`);
        return {
          jobs: [],
          meta: {
            total: 0,
            page: 1,
            limit,
            has_next: false,
          },
        };
      }

      // Extract job IDs from the result
      const jobIds = topJobsResult.jobs
        .map((job) => {
          // Extract external ID from the job entity
          const jobEntity = job as unknown as { ext_id: string };
          return jobEntity.ext_id;
        })
        .filter((id) => id !== undefined && id !== null);

      // Persist the recommendations to ForYou entity
      await this.forYouRepository.saveForYouJobs(userId, jobIds);

      this.logger.log(
        `Successfully saved ${jobIds.length} "For You" recommendations for user ${userId}`,
      );

      return topJobsResult;
    } catch (error) {
      this.logger.error(
        `Error generating "For You" recommendations for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get persisted "For You" job recommendations for a specific user
   * @param userId User ID to get recommendations for
   * @returns The list of recommended jobs
   */
  async getForYouJobs(userId: string): Promise<JobSearchResult> {
    this.logger.log(`Getting "For You" recommendations for user ${userId}`);

    try {
      // Get jobs from ForYou repository
      const recommendations = await this.forYouRepository.getForYouJobs(userId);

      if (!recommendations || recommendations.length === 0) {
        this.logger.warn(
          `No "For You" recommendations found for user ${userId}`,
        );
        return {
          jobs: [],
          meta: {
            total: 0,
            page: 1,
            limit: 10,
            has_next: false,
          },
        };
      }

      // Extract jobs and add interaction data
      const jobsWithInteraction = recommendations.map((item) => {
        const job = formatJobForResponse(item.job);

        // Add interaction data to the job object
        return {
          ...job,
          is_viewed: item.forYou.is_viewed,
          is_liked: item.forYou.is_liked,
          classification_data: item.forYou.classification_data,
        };
      });

      return {
        jobs: jobsWithInteraction,
        meta: {
          total: jobsWithInteraction.length,
          page: 1,
          limit: jobsWithInteraction.length,
          has_next: false,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting "For You" recommendations for user ${userId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check if the filters object contains only default/system filters
   * @param filters The filters object to check
   * @returns true if no user-specific filters are present
   */
  private hasNoUserFilters(filters: Record<string, any>): boolean {
    // List of filter keys that are considered user-specific
    const userFilterKeys = [
      'municipality_names',
      'municipality_codes', 
      'region_names',
      'region_codes',
      'country',
      'skills',
      'industry',
      'occupation',
      'seniority',
      'employer',
      'employment_type',
      'working_time',
      'continuity',
      // Add any other filter keys that users can set
    ];

    // Check if any user filter is present and has a value
    for (const key of userFilterKeys) {
      const value = filters[key];
      if (value !== undefined && value !== null && value !== '') {
        // Check for empty arrays as well
        if (Array.isArray(value) && value.length > 0) {
          return false;
        } else if (!Array.isArray(value)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Get jobs directly from the database without using Pinecone
   * Used when no search terms or filters are applied
   * @param context The search context
   * @returns Job search results
   */
  private async getJobsDirectFromDatabase(
    context: SearchContext
  ): Promise<JobSearchResult> {
    const { page, limit } = context.pagination;
    const { sortBy, sortDirection } = context.sorting;

    // Check cache first
    const cacheKey = `direct_db:${page}:${limit}:${sortBy}:${sortDirection}`;
    const cachedResult = await this.cacheService.get(context);
    if (cachedResult) {
      this.logger.debug('Returning cached direct database results');
      return cachedResult;
    }

    // Fetch from database
    const { jobs, total } = await this.jobsRepository.findUnexpiredJobsWithPagination(
      page,
      limit,
      sortBy || 'published_date',
      sortDirection?.toUpperCase() as 'ASC' | 'DESC' || 'DESC'
    );

    // Format jobs for response
    const formattedJobs = formatJobsForResponse(jobs);

    // Create result object
    const result: JobSearchResult = {
      jobs: formattedJobs,
      meta: {
        total,
        page,
        limit,
        has_next: page * limit < total,
      },
    };

    // Cache the result
    await this.cacheService.set(context, result);

    this.logger.log(
      `Direct database query returned ${jobs.length} jobs out of ${total} total`
    );

    return result;
  }
}