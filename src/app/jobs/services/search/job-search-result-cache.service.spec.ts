import { Test, TestingModule } from '@nestjs/testing';
import { JobSearchResultCache } from './job-search-result-cache.service';
import { CacheService } from '../../../../cache/cache.service';
import { SearchContext } from './search-context.interface';
import { Job } from '../../../../entities/job.entity';
import { Logger } from '@nestjs/common';

// Mock implementations
const mockCacheService = {
  get: jest.fn(),
  set: jest.fn(),
  delete: jest.fn(),
};

describe('JobSearchResultCache', () => {
  let service: JobSearchResultCache;
  let cacheService: CacheService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobSearchResultCache,
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    service = module.get<JobSearchResultCache>(JobSearchResultCache);
    cacheService = module.get<CacheService>(CacheService);

    // Silence the logger during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('generateUserCacheKey', () => {
    it('should generate a unique key based on search context without pagination', () => {
      // Arrange
      const userId = 'test-user';
      const searchContext: SearchContext = {
        searchParams: { query: 'test' },
        keywords: 'test',
        skillContexts: ['skill1', 'skill2'],
        pagination: { page: 2, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: { location: { municipalityNames: 'Helsinki' } },
        useVectorSearch: true,
      };

      // Act
      const key1 = service.generateUserCacheKey(userId, searchContext);

      // Change pagination - should still generate the same key
      const searchContext2 = {
        ...searchContext,
        pagination: { page: 3, limit: 20 },
      };
      const key2 = service.generateUserCacheKey(userId, searchContext2);

      // Should be the same because pagination is excluded
      expect(key1).toEqual(key2);

      // Change a filter - should generate a different key
      const searchContext3 = {
        ...searchContext,
        filters: { location: { municipalityNames: 'Tampere' } },
      };
      const key3 = service.generateUserCacheKey(userId, searchContext3);

      // Should be different because filters are included
      expect(key1).not.toEqual(key3);
    });
  });

  describe('getFullResults', () => {
    it('should return cached results when available', async () => {
      // Arrange
      const userId = 'test-user';
      const searchContext: SearchContext = {
        searchParams: { query: 'test' },
        keywords: 'test',
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
      };

      const cachedJobs = [{ id: 1 } as unknown as Job];
      jest.spyOn(cacheService, 'get').mockResolvedValue(cachedJobs);

      // Act
      const result = await service.getFullResults(userId, searchContext);

      // Assert
      expect(cacheService.get).toHaveBeenCalled();
      expect(result).toEqual(cachedJobs);
    });

    it('should return null when no cached results exist', async () => {
      // Arrange
      const userId = 'test-user';
      const searchContext: SearchContext = {
        searchParams: { query: 'test' },
        keywords: 'test',
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
      };

      jest.spyOn(cacheService, 'get').mockResolvedValue(null);

      // Act
      const result = await service.getFullResults(userId, searchContext);

      // Assert
      expect(cacheService.get).toHaveBeenCalled();
      expect(result).toBeNull();
    });
  });

  describe('getPagedResults', () => {
    it('should return the correct page of results', () => {
      // Arrange
      const jobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);

      // Act - Get page 2 with 10 items per page
      const result = service.getPagedResults(jobs, 2, 10);

      // Assert - Should return items 11-20
      expect(result.length).toBe(10);
      expect(result[0].id).toBe(11);
      expect(result[9].id).toBe(20);
    });

    it('should handle partial pages correctly', () => {
      // Arrange
      const jobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);

      // Act - Get page 3 with 10 items per page (should return 5 items)
      const result = service.getPagedResults(jobs, 3, 10);

      // Assert
      expect(result.length).toBe(5);
      expect(result[0].id).toBe(21);
      expect(result[4].id).toBe(25);
    });
  });

  describe('calculatePaginationMetadata', () => {
    it('should calculate correct has_next for full pages', () => {
      // Arrange
      const jobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);

      // Act
      const metadata = service.calculatePaginationMetadata(jobs, 2, 10);

      // Assert
      expect(metadata).toEqual({
        total: 25,
        page: 2,
        limit: 10,
        has_next: true,
      });
    });

    it('should calculate correct has_next for last page', () => {
      // Arrange
      const jobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);

      // Act
      const metadata = service.calculatePaginationMetadata(jobs, 3, 10);

      // Assert
      expect(metadata).toEqual({
        total: 25,
        page: 3,
        limit: 10,
        has_next: false,
      });
    });
  });
});