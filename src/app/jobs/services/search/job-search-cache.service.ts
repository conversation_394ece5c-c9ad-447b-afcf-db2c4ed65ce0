import { Injectable, Logger } from '@nestjs/common';
import { SearchContext } from './search-context.interface';
import { JobSearchResult } from '../interfaces/job-search.interface';
import { createHash } from 'crypto';
import { CacheService } from '../../../../cache/cache.service';

@Injectable()
export class JobSearchCacheService {
  private readonly logger = new Logger(JobSearchCacheService.name);
  private readonly CACHE_PREFIX = 'job_search:';
  private readonly DEFAULT_TTL = '15m';

  constructor(private readonly cacheService: CacheService) {}

  /**
   * Generate a cache key from search context
   */
  generateCacheKey(context: SearchContext): string {
    // Create a normalized object with all the relevant properties for caching
    const cacheKeyObject = {
      // Base search parameters
      keywords: context.keywords || '',
      skillContexts: context.skillContexts || [],

      // Include ALL filters for proper cache key generation
      filters: { ...context.filters },

      // Include pagination and sorting
      pagination: { ...context.pagination },
      sorting: { ...context.sorting },
    };

    // Special handling for location filters
    if (context.filters?.location) {
      // Ensure municipality names are normalized if present
      if (
        context.filters.location.municipalityNames &&
        typeof context.filters.location.municipalityNames === 'string'
      ) {
        const municipalities = context.filters.location.municipalityNames
          .split(',')
          .map((m) => m.trim())
          .sort();
        cacheKeyObject.filters.location.municipalityNames =
          municipalities.join(',');
      }
    }

    // Sort the object keys for deterministic serialization
    const orderedObject = this.sortObjectKeys(cacheKeyObject);

    // Create a deterministic string representation
    const stringified = JSON.stringify(orderedObject);
    this.logger.debug(`Generating cache key for params: ${stringified}`);

    // Generate MD5 hash of the string key
    const hash = createHash('md5').update(stringified).digest('hex');

    return `${this.CACHE_PREFIX}${hash}`;
  }

  /**
   * Recursively sort object keys to ensure consistent JSON serialization
   */
  private sortObjectKeys(obj: any): any {
    // If not an object or is null, return as is
    if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
      return obj;
    }

    // Sort the keys and create a new object with sorted keys
    return Object.keys(obj)
      .sort()
      .reduce((result, key) => {
        // Recursively sort nested objects
        result[key] = this.sortObjectKeys(obj[key]);
        return result;
      }, {});
  }

  /**
   * Store a list of generated cache keys for future bulk operations
   * @param key The cache key to add to the list
   */
  private async trackCacheKey(key: string): Promise<void> {
    try {
      // Get the current list of keys
      const keysList =
        (await this.cacheService.get<string[]>(`${this.CACHE_PREFIX}keys`)) ||
        [];

      // Add the new key if it's not already in the list
      if (!keysList.includes(key)) {
        keysList.push(key);
        // Store the updated list
        await this.cacheService.set(
          `${this.CACHE_PREFIX}keys`,
          keysList,
          this.DEFAULT_TTL,
        );
      }
    } catch (error) {
      this.logger.warn(`Error tracking cache key: ${error.message}`);
    }
  }

  /**
   * Get search results from cache
   */
  async get(context: SearchContext): Promise<JobSearchResult | null> {
    const cacheKey = this.generateCacheKey(context);

    try {
      const cachedResult =
        await this.cacheService.get<JobSearchResult>(cacheKey);

      if (cachedResult) {
        this.logger.debug(`Cache hit for key: ${cacheKey}`);
        return cachedResult;
      }

      this.logger.debug(`Cache miss for key: ${cacheKey}`);
      return null;
    } catch (error) {
      this.logger.warn(`Error retrieving from cache: ${error.message}`);
      return null;
    }
  }

  /**
   * Store search results in cache
   */
  async set(
    context: SearchContext,
    result: JobSearchResult,
    ttl: number | string = this.DEFAULT_TTL,
  ): Promise<void> {
    const cacheKey = this.generateCacheKey(context);

    try {
      // Validate the search result before caching
      // Don't cache if we have meta.total > 0 but empty jobs array
      if (result.meta.total > 0 && (!result.jobs || result.jobs.length === 0)) {
        this.logger.warn(
          `Refusing to cache inconsistent results: meta.total=${result.meta.total} but jobs array is empty`,
        );
        return;
      }

      await this.cacheService.set(cacheKey, result, ttl);
      // Track this key for future bulk operations
      await this.trackCacheKey(cacheKey);
      this.logger.debug(
        `Cached results for key: ${cacheKey} with TTL: ${ttl} with ${result.jobs.length} jobs`,
      );
    } catch (error) {
      this.logger.warn(`Error storing in cache: ${error.message}`);
    }
  }

  /**
   * Invalidate cache for a specific context
   */
  async invalidate(context: SearchContext): Promise<void> {
    const cacheKey = this.generateCacheKey(context);

    try {
      await this.cacheService.delete(cacheKey);
      this.logger.debug(`Invalidated cache for key: ${cacheKey}`);
    } catch (error) {
      this.logger.warn(`Error invalidating cache: ${error.message}`);
    }
  }

  /**
   * Invalidate all job search caches
   */
  async invalidateAll(): Promise<void> {
    try {
      // Get the list of tracked keys
      const keysList = await this.cacheService.get<string[]>(
        `${this.CACHE_PREFIX}keys`,
      );

      if (keysList && keysList.length > 0) {
        // Delete each key individually
        for (const key of keysList) {
          await this.cacheService.delete(key);
        }

        // Clear the keys list itself
        await this.cacheService.delete(`${this.CACHE_PREFIX}keys`);

        this.logger.debug(`Invalidated ${keysList.length} cache entries`);
      } else {
        this.logger.debug('No cached entries to invalidate');
      }
    } catch (error) {
      this.logger.warn(`Error invalidating all caches: ${error.message}`);
    }
  }
}