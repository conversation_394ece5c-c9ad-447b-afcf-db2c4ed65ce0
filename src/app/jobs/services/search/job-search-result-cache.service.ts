import { Injectable, Logger } from '@nestjs/common';
import { Job } from '../../../../entities/job.entity';
import { SearchContext } from './search-context.interface';
import { createHash } from 'crypto';
import { CacheService } from '../../../../cache/cache.service';

@Injectable()
export class JobSearchResultCache {
  private readonly logger = new Logger(JobSearchResultCache.name);
  private readonly USER_CACHE_PREFIX = 'user_job_search:';
  private readonly DEFAULT_TTL = '30m'; // 30 minutes ttl
  readonly MAX_RESULTS = 200; // Maximum results to fetch and cache

  constructor(private readonly cacheService: CacheService) {}

  /**
   * Generate a user-specific cache key for storing large result sets
   * Excludes pagination parameters to enable caching of the full result set
   */
  generateUserCacheKey(userId: string, searchContext: SearchContext): string {
    // Create a normalized object with all the relevant search properties (exclude pagination)
    const cacheKeyObject = {
      // Base search parameters
      keywords: searchContext.keywords || '',
      skillContexts: searchContext.skillContexts || [],

      // Include ALL filters for proper cache key generation
      filters: { ...searchContext.filters },

      // Include sorting but NOT pagination
      sorting: { ...searchContext.sorting },
    };

    // Create a deterministic string representation
    const orderedObject = this.sortObjectKeys(cacheKeyObject);

    // Generate MD5 hash of the string key
    const stringified = JSON.stringify(orderedObject);
    this.logger.debug(`Generating user cache key for params: ${stringified}`);
    const hash = createHash('md5').update(stringified).digest('hex');

    return `${this.USER_CACHE_PREFIX}${userId}:${hash}`;
  }

  /**
   * Get cached full result set by user ID and search context
   */
  async getFullResults(
    userId: string,
    searchContext: SearchContext,
  ): Promise<Job[] | null> {
    const cacheKey = this.generateUserCacheKey(userId, searchContext);

    try {
      const cachedFullResults = await this.cacheService.get<Job[]>(cacheKey);

      if (cachedFullResults) {
        this.logger.debug(`Cache hit for full results key: ${cacheKey}`);
        return cachedFullResults;
      }

      this.logger.debug(`Cache miss for full results key: ${cacheKey}`);
      return null;
    } catch (error) {
      this.logger.warn(
        `Error retrieving full results from cache: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Store full result set by user ID and search context
   */
  async setFullResults(
    userId: string,
    searchContext: SearchContext,
    jobs: Job[],
  ): Promise<void> {
    const cacheKey = this.generateUserCacheKey(userId, searchContext);

    try {
      // Limit the number of jobs stored to prevent excessive memory usage
      const jobsToCache = jobs.slice(0, this.MAX_RESULTS);
      await this.cacheService.set(cacheKey, jobsToCache, this.DEFAULT_TTL);
      this.logger.debug(
        `Cached ${jobsToCache.length} jobs for key: ${cacheKey}`,
      );
    } catch (error) {
      this.logger.warn(`Error storing full results in cache: ${error.message}`);
    }
  }

  /**
   * Get a paginated slice of the cached results
   */
  getPagedResults(jobs: Job[], page: number, limit: number): Job[] {
    // Ensure we have an array to work with
    if (!Array.isArray(jobs)) {
      this.logger.error('getPagedResults received non-array input for jobs');
      return [];
    }

    // Make sure we have valid pagination parameters
    if (page < 1) page = 1;
    if (limit < 1) limit = 10;

    const startIndex = (page - 1) * limit;

    // If startIndex is beyond the array bounds, return an empty array
    if (startIndex >= jobs.length) {
      this.logger.warn(
        `Requested page ${page} is beyond available results (${jobs.length} items)`,
      );
      return [];
    }

    const endIndex = Math.min(page * limit, jobs.length);
    this.logger.debug(
      `Paginating results: page=${page}, limit=${limit}, startIndex=${startIndex}, endIndex=${endIndex}, will return ${endIndex - startIndex} jobs`,
    );

    const result = jobs.slice(startIndex, endIndex);

    // Double-check we got something
    if (result.length === 0 && jobs.length > 0) {
      this.logger.warn(
        `Pagination returned 0 jobs despite having ${jobs.length} total jobs. This might be a bug.`,
      );
    }

    return result;
  }

  /**
   * Calculate metadata for paginated results
   */
  calculatePaginationMetadata(
    jobs: Job[],
    page: number,
    limit: number,
  ): {
    total: number;
    page: number;
    limit: number;
    has_next: boolean;
  } {
    const total = jobs.length;
    const hasNextPage = page * limit < total;

    return {
      total,
      page,
      limit,
      has_next: hasNextPage,
    };
  }

  /**
   * Store score map for vector search results
   * This allows us to maintain ranking scores when paginating from cache
   */
  async setScoreMap(
    userId: string,
    searchContext: SearchContext,
    scoreMap: Map<string, number>,
  ): Promise<void> {
    const cacheKey = `${this.generateUserCacheKey(userId, searchContext)}:scores`;

    try {
      // Convert Map to a plain object for caching
      const scoreObject = Object.fromEntries(scoreMap);
      await this.cacheService.set(cacheKey, scoreObject, this.DEFAULT_TTL);
      this.logger.debug(`Cached score map for key: ${cacheKey}`);
    } catch (error) {
      this.logger.warn(`Error storing score map in cache: ${error.message}`);
    }
  }

  /**
   * Get score map for vector search results
   */
  async getScoreMap(
    userId: string,
    searchContext: SearchContext,
  ): Promise<Map<string, number> | null> {
    const cacheKey = `${this.generateUserCacheKey(userId, searchContext)}:scores`;

    try {
      const cachedScores =
        await this.cacheService.get<Record<string, number>>(cacheKey);

      if (cachedScores) {
        this.logger.debug(`Cache hit for score map key: ${cacheKey}`);
        // Convert plain object back to Map
        return new Map(Object.entries(cachedScores));
      }

      this.logger.debug(`Cache miss for score map key: ${cacheKey}`);
      return null;
    } catch (error) {
      this.logger.warn(
        `Error retrieving score map from cache: ${error.message}`,
      );
      return null;
    }
  }

  /**
   * Invalidate user's cached search results
   */
  async invalidateUserCache(userId: string): Promise<void> {
    try {
      // We would need a registry of user cache keys to effectively clean all of them
      // For MVP, we can't implement full invalidation without key tracking
      this.logger.debug(`Invalidation for user ${userId} would go here`);
      // Future improvement: track keys by user ID and invalidate all
    } catch (error) {
      this.logger.warn(`Error invalidating user cache: ${error.message}`);
    }
  }

  /**
   * Recursively sort object keys to ensure consistent JSON serialization
   */
  private sortObjectKeys(obj: any): any {
    // If not an object or is null, return as is
    if (obj === null || typeof obj !== 'object' || Array.isArray(obj)) {
      return obj;
    }

    // Sort the keys and create a new object with sorted keys
    return Object.keys(obj)
      .sort()
      .reduce((result, key) => {
        // Recursively sort nested objects
        result[key] = this.sortObjectKeys(obj[key]);
        return result;
      }, {});
  }
}