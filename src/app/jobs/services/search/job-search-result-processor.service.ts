import { Injectable, Logger } from '@nestjs/common';
import { Job } from '../../../../entities/job.entity';
import { JobSearchResult } from '../interfaces/job-search.interface';
import { SearchContext } from './search-context.interface';
import { formatJobsForResponse } from '../helpers/job-data-to-entity.converter';

@Injectable()
export class JobSearchResultProcessor {
  private readonly logger = new Logger(JobSearchResultProcessor.name);

  /**
   * Process the search results
   */
  processResults(
    context: SearchContext,
    jobs: Job[],
    scoreMap: Map<string, number>,
    totalResults: number,
  ): JobSearchResult {
    const { pagination, sorting, useVectorSearch } = context;
    const { page, limit } = pagination;
    const { sortBy, sortDirection } = sorting;

    // Apply sorting if requested
    if (sortBy) {
      this.sortJobs(jobs, sortBy, sortDirection);
    }

    // Calculate pagination
    const startIndex = (page - 1) * limit;
    const endIndex = page * limit;
    const paginatedJobs = jobs.slice(startIndex, endIndex);

    // Format the enriched job data
    const formattedJobs = formatJobsForResponse(paginatedJobs);

    // Log what we're returning
    this.logger.debug(
      `Formatted ${formattedJobs.length} jobs for page ${page}, total=${totalResults}`,
    );

    // Add ranking scores to the formatted jobs
    formattedJobs.forEach((job) => {
      job.rankingScore = scoreMap.get(job.id) || 0;
    });

    // Determine if there are more results
    // Calculate based on total matches
    const lastItemIndex = page * limit;
    const hasNextPage = lastItemIndex < totalResults;

    return {
      jobs: formattedJobs,
      meta: {
        total: totalResults, // Always use the actual total count of results
        page,
        limit,
        has_next: hasNextPage,
      },
    };
  }

  /**
   * Sort jobs based on specified field and direction
   */
  sortJobs(jobs: Job[], sortBy: string, direction: 'asc' | 'desc'): void {
    jobs.sort((a, b) => {
      let valueA: any;
      let valueB: any;

      // Handle nested properties with dot notation (e.g., "timing.published_date")
      if (sortBy.includes('.')) {
        const parts = sortBy.split('.');
        valueA = parts.reduce((obj, key) => obj && obj[key], a);
        valueB = parts.reduce((obj, key) => obj && obj[key], b);
      } else {
        valueA = a[sortBy];
        valueB = b[sortBy];
      }

      // Handle dates
      if (valueA instanceof Date && valueB instanceof Date) {
        return direction === 'asc'
          ? valueA.getTime() - valueB.getTime()
          : valueB.getTime() - valueA.getTime();
      }

      // Handle strings
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return direction === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA);
      }

      // Handle numbers
      if (typeof valueA === 'number' && typeof valueB === 'number') {
        return direction === 'asc' ? valueA - valueB : valueB - valueA;
      }

      // Default case
      return 0;
    });
  }
}