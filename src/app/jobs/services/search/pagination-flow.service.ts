import { Injectable, Logger } from '@nestjs/common';
import { Job } from '../../../../entities/job.entity';
import { JobSearchResult } from '../interfaces/job-search.interface';
import { SearchContext } from './search-context.interface';
import { JobSearchResultCache } from './job-search-result-cache.service';
import { JobSearchResultProcessor } from './job-search-result-processor.service';

/**
 * Service to handle paginated access to search results
 * This service is responsible for managing the flow of pagination
 * when results are retrieved from cache vs. when they need to be fetched fresh
 */
@Injectable()
export class PaginationFlowService {
  private readonly logger = new Logger(PaginationFlowService.name);

  constructor(
    private readonly resultCache: JobSearchResultCache,
    private readonly resultProcessor: JobSearchResultProcessor,
  ) {}

  /**
   * Check if cached results exist for a search context and handle pagination
   * @returns The paginated search result or null if not found in cache
   */
  async getCachedPagedResults(
    context: SearchContext
  ): Promise<JobSearchResult | null> {
    // Only proceed if caching is enabled and there's a user ID
    if (!context.useCache || !context.userId) {
      this.logger.debug('Cache disabled or no user ID, skipping cache check');
      return null;
    }

    try {
      // Get full cached results for this search context
      const cachedFullResults = await this.resultCache.getFullResults(
        context.userId,
        context
      );

      // If no cached results, return null
      if (!cachedFullResults || cachedFullResults.length === 0) {
        return null;
      }

      this.logger.debug(
        `Found ${cachedFullResults.length} cached results for user ${context.userId}`
      );

      // Get cached score map if available (for preserving rankings)
      const scoreMap = await this.resultCache.getScoreMap(context.userId, context) || 
                        new Map<string, number>();

      // The controller sends page 1 but offset 10, which implies we want page 2
      // Convert offset-based pagination to page-based pagination if needed
      let effectivePage = context.pagination.page;
      const effectiveLimit = context.pagination.limit;
      
      // If offset is provided in searchParams, calculate the effective page
      if (context.searchParams.offset !== undefined) {
        effectivePage = Math.floor(context.searchParams.offset / effectiveLimit) + 1;
        this.logger.debug(`Converting offset ${context.searchParams.offset} to page ${effectivePage}`);
      }
      
      // Get the requested page from cached full results
      const pagedJobs = this.resultCache.getPagedResults(
        cachedFullResults,
        effectivePage,
        effectiveLimit
      );

      // Check if we got any jobs for this page
      if (pagedJobs.length === 0) {
        // We have cached results, but the paged subset is empty
        if (effectivePage > 1 && cachedFullResults.length > 0) {
          // This is likely due to requesting a page beyond available results
          this.logger.warn(`Requested page ${effectivePage} is beyond available cached results. Total cached jobs: ${cachedFullResults.length}`);
          
          // Log pagination details for debugging
          this.logger.debug(`Pagination details: origPage=${context.pagination.page}, offset=${context.searchParams.offset}, limit=${context.pagination.limit}, effectivePage=${effectivePage}`);
          
          // Return the last available page instead of null
          // Calculate the max valid page
          const maxValidPage = Math.ceil(cachedFullResults.length / context.pagination.limit);
          this.logger.debug(`Returning last valid page ${maxValidPage} instead of requested page ${effectivePage}`);
          
          // Get the valid page of results
          if (maxValidPage >= 1) {
            const validPagedJobs = this.resultCache.getPagedResults(
              cachedFullResults,
              maxValidPage,
              context.pagination.limit
            );
            
            // Create a new context with the effective page for result processing
            const validContext = {
              ...context,
              pagination: {
                ...context.pagination,
                page: maxValidPage
              }
            };
            
            // Process with the valid page
            const results = this.resultProcessor.processResults(
              validContext,
              cachedFullResults,
              scoreMap,
              cachedFullResults.length
            );
            
            return results;
          }
        } else if (cachedFullResults.length === 0) {
          // We have a cache entry but it's empty - this shouldn't happen
          this.logger.warn(`Found cached results for user ${context.userId} but the array is empty`);
        }
        
        return null; // Let the system fall back to standard search
      }
      
      this.logger.debug(`Got ${pagedJobs.length} jobs for page ${effectivePage} from cache`)
      
      // Create a new context with the effective page for result processing
      const effectiveContext = {
        ...context,
        pagination: {
          ...context.pagination,
          page: effectivePage
        }
      };
      
      // Process the paginated slice of results
      // Important: We pass the TOTAL number of cached results as totalResults to ensure meta.total is correct
      const results = this.resultProcessor.processResults(
        effectiveContext,
        cachedFullResults,
        scoreMap,
        cachedFullResults.length
      );

      return results;
    } catch (error) {
      // Log error but don't fail - fall back to standard search
      this.logger.error(
        `Error retrieving cached paged results: ${error.message}`,
        error.stack
      );
      return null;
    }
  }

  /**
   * Store search results in cache for future pagination
   */
  async storeResultsForPagination(
    context: SearchContext,
    jobs: Job[],
    scoreMap: Map<string, number>
  ): Promise<void> {
    // Only proceed if there's a user ID
    if (!context.userId) {
      return;
    }

    try {
      // Store full results in cache
      await this.resultCache.setFullResults(context.userId, context, jobs);

      // Store score map separately
      await this.resultCache.setScoreMap(context.userId, context, scoreMap);

      this.logger.debug(
        `Stored ${jobs.length} results and scores for user ${context.userId} for future pagination`
      );
    } catch (error) {
      // Log error but don't fail
      this.logger.error(
        `Error storing results for pagination: ${error.message}`,
        error.stack
      );
    }
  }

  /**
   * Adjust search context for fetching more results
   * This creates a copy of the context with modified pagination
   * to fetch more results than requested for caching purposes
   */
  getContextForFullResultsFetch(context: SearchContext): SearchContext {
    const maxResults = this.resultCache.MAX_RESULTS;
    
    // Create a copy of the context to avoid modifying the original
    const expandedContext: SearchContext = {
      ...context,
      pagination: {
        page: 1, // Always start from first page when fetching full results
        // Get more results than requested, but not more than MAX_RESULTS
        limit: Math.min(maxResults, context.pagination.limit * 5),
      },
    };

    return expandedContext;
  }
}