import { Injectable, Logger } from '@nestjs/common';
import { JobsPineconeService } from '../jobs-pinecone.service';
import { PineconeService } from '../../../../pinecone/pinecone.service';
import { SearchContext } from './search-context.interface';

@Injectable()
export class JobSearchVectorService {
  private readonly logger = new Logger(JobSearchVectorService.name);

  constructor(
    private readonly pineconeService: JobsPineconeService,
    private readonly service: PineconeService,
  ) {}

  /**
   * Gets the default Pinecone index
   */
  async getDefaultIndex() {
    return this.pineconeService.getDefaultIndex();
  }

  /**
   * Generates query vector from text
   */
  async generateQueryVector(context: SearchContext): Promise<number[]> {
    const { keywords, useVectorSearch } = context;

    // If no text query, return default vector
    if (!useVectorSearch) {
      this.logger.log('No keywords provided, using metadata filtering only');
      return new Array(this.pineconeService.dimension).fill(1);
    }

    try {
      this.logger.log(`Generating embeddings for text query: "${keywords}"`);
      const queryEmbeddings = await this.pineconeService.generateEmbeddingsV2(
        keywords!,
      );

      if (queryEmbeddings) {
        this.logger.debug(
          `Successfully generated embeddings for query "${keywords}"`,
        );
        return queryEmbeddings;
      } else {
        this.logger.warn(
          `Failed to generate embeddings for query "${keywords}", using fallback`,
        );
        return new Array(this.pineconeService.dimension).fill(1);
      }
    } catch (error) {
      this.logger.error(
        `Error generating embeddings for query "${keywords}": ${error.message}`,
      );
      return new Array(this.pineconeService.dimension).fill(1);
    }
  }

  /**
   * Performs reranking of search results
   */
  async rerankResults(
    context: SearchContext,
    documents: { id: string; skills: string; title: string }[],
  ): Promise<{ id: string; score: number; context: string }[]> {
    const { keywords, skillContexts, pagination } = context;
    const { limit } = pagination;
    let jobResults: { id: string; score: number; context: string }[] = [];

    if (skillContexts.length > 0) {
      // A set to track which job IDs we've already added to ensure no duplicates
      const seenJobIds = new Set<string>();

      // Process each skill context individually
      for (const skillContext of skillContexts) {
        const rerankedResults = await this.service
          .getPineconeClient()
          .inference.rerank(
            'cohere-rerank-3.5',
            skillContext, // Individual skill context
            documents,
            {
              topN: limit, // Get top N for each skill
              returnDocuments: true,
              rankFields: ['title', 'skills'],
            },
          );

        // Add top results from this skill context that haven't been seen yet
        for (const match of rerankedResults.data) {
          if (match.score > 0.0 && !seenJobIds.has(match.document!.id)) {
            jobResults.push({
              id: match.document!.id,
              score: match.score,
              context: skillContext,
            });
            seenJobIds.add(match.document!.id);

            // Once we've collected enough results, stop
            if (jobResults.length >= limit) {
              break;
            }
          }
        }
      }

      // Sort by score to ensure best results are first
      jobResults.sort((a, b) => b.score - a.score);

      // Limit to requested size
      jobResults = jobResults.slice(0, limit);
    } else {
      // If no skill contexts, use the keywords as before
      const rerankedResults = await this.service
        .getPineconeClient()
        .inference.rerank(
          'cohere-rerank-3.5',
          keywords!, // Original user keywords
          documents,
          {
            topN: limit, // Final number of recommendations
            returnDocuments: true,
            rankFields: ['title', 'skills'],
          },
        );

      // Extract ids and scores from the reranked results (filtering by score threshold)
      jobResults = rerankedResults.data
        .filter((match) => match.score > 0.0) // Only include results above the threshold
        .map((match) => ({
          id: match.document!.id,
          score: match.score,
          context: 'Keywords',
        }));
    }

    return jobResults;
  }
}