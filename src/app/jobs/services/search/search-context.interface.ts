import { JobSearchParams } from '../interfaces/job-search.interface';

/**
 * Represents a search context used for job searches
 */
export interface SearchContext {
  /**
   * Original search parameters from the request
   */
  searchParams: JobSearchParams;
  
  /**
   * Optional keywords for semantic search
   */
  keywords?: string;
  
  /**
   * Skill contexts for multi-context search
   */
  skillContexts: string[];
  
  /**
   * Pagination details
   */
  pagination: {
    page: number;
    limit: number;
  };
  
  /**
   * Sorting details
   */
  sorting: {
    sortBy?: string;
    sortDirection: 'asc' | 'desc';
  };
  
  /**
   * Filter criteria
   */
  filters: Record<string, any>;
  
  /**
   * Whether to use vector search
   */
  useVectorSearch: boolean;
  
  /**
   * User ID for personalized caching (if available)
   */
  userId?: string;
  
  /**
   * Whether to use cached full results for pagination
   */
  useCache?: boolean;
}