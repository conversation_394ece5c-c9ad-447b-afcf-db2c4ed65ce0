import { Test, TestingModule } from '@nestjs/testing';
import { PaginationFlowService } from './pagination-flow.service';
import { JobSearchResultCache } from './job-search-result-cache.service';
import { JobSearchResultProcessor } from './job-search-result-processor.service';
import { SearchContext } from './search-context.interface';
import { Job } from '../../../../entities/job.entity';
import { Logger } from '@nestjs/common';

describe('PaginationFlowService', () => {
  let service: PaginationFlowService;
  let resultCache: JobSearchResultCache;
  let resultProcessor: JobSearchResultProcessor;

  // Mock implementations
  const mockResultCache = {
    getFullResults: jest.fn(),
    setFullResults: jest.fn(),
    getScoreMap: jest.fn(),
    setScoreMap: jest.fn(),
    getPagedResults: jest.fn(),
    MAX_RESULTS: 200,
  };

  const mockResultProcessor = {
    processResults: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        PaginationFlowService,
        { provide: JobSearchResultCache, useValue: mockResultCache },
        { provide: JobSearchResultProcessor, useValue: mockResultProcessor },
      ],
    }).compile();

    service = module.get<PaginationFlowService>(PaginationFlowService);
    resultCache = module.get<JobSearchResultCache>(JobSearchResultCache);
    resultProcessor = module.get<JobSearchResultProcessor>(
      JobSearchResultProcessor,
    );

    // Silence the logger during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCachedPagedResults', () => {
    it('should return null when cache is disabled', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: {},
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: false,
        useCache: false,
        userId: 'user-1',
      };

      // Act
      const result = await service.getCachedPagedResults(context);

      // Assert
      expect(result).toBeNull();
      expect(resultCache.getFullResults).not.toHaveBeenCalled();
    });

    it('should return null when userId is not provided', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: {},
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: false,
        useCache: true,
      };

      // Act
      const result = await service.getCachedPagedResults(context);

      // Assert
      expect(result).toBeNull();
      expect(resultCache.getFullResults).not.toHaveBeenCalled();
    });

    it('should return paginated results from cache when available', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 2, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
        useCache: true,
        userId: 'user-1',
      };

      const cachedJobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);
      const pagedJobs = cachedJobs.slice(10, 20); // Page 2 with 10 items per page
      const scoreMap = new Map(cachedJobs.map((job) => [String(job.id), 0.9]));
      const expectedResult = {
        jobs: pagedJobs.map((job) => ({ id: String(job.id) })),
        meta: { total: 25, page: 2, limit: 10, has_next: true },
      };

      // Setup mocks
      mockResultCache.getFullResults.mockResolvedValue(cachedJobs);
      mockResultCache.getScoreMap.mockResolvedValue(scoreMap);
      mockResultCache.getPagedResults.mockReturnValue(pagedJobs);
      mockResultProcessor.processResults.mockReturnValue(expectedResult);

      // Act
      const result = await service.getCachedPagedResults(context);

      // Assert
      expect(resultCache.getFullResults).toHaveBeenCalledWith(
        'user-1',
        context,
      );
      expect(resultCache.getScoreMap).toHaveBeenCalledWith('user-1', context);
      expect(resultCache.getPagedResults).toHaveBeenCalledWith(
        cachedJobs,
        2,
        10,
      );
      expect(resultProcessor.processResults).toHaveBeenCalledWith(
        context,
        pagedJobs,
        scoreMap,
        25,
      );
      expect(result).toEqual(expectedResult);
    });

    it('should return null when no cached results exist', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
        useCache: true,
        userId: 'user-1',
      };

      // Setup mocks
      mockResultCache.getFullResults.mockResolvedValue(null);

      // Act
      const result = await service.getCachedPagedResults(context);

      // Assert
      expect(result).toBeNull();
      expect(resultCache.getFullResults).toHaveBeenCalledWith(
        'user-1',
        context,
      );
      expect(resultCache.getPagedResults).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully and return null', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
        useCache: true,
        userId: 'user-1',
      };

      // Setup mocks to throw error
      mockResultCache.getFullResults.mockRejectedValue(
        new Error('Cache error'),
      );

      // Act
      const result = await service.getCachedPagedResults(context);

      // Assert
      expect(result).toBeNull();
    });
  });

  describe('storeResultsForPagination', () => {
    it('should store results and score map in cache', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
        userId: 'user-1',
      };

      const jobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);
      const scoreMap = new Map(jobs.map((job) => [String(job.id), 0.9]));

      // Act
      await service.storeResultsForPagination(context, jobs, scoreMap);

      // Assert
      expect(resultCache.setFullResults).toHaveBeenCalledWith(
        'user-1',
        context,
        jobs,
      );
      expect(resultCache.setScoreMap).toHaveBeenCalledWith(
        'user-1',
        context,
        scoreMap,
      );
    });

    it('should not attempt to store results when userId is not provided', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
      };

      const jobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);
      const scoreMap = new Map(jobs.map((job) => [String(job.id), 0.9]));

      // Act
      await service.storeResultsForPagination(context, jobs, scoreMap);

      // Assert
      expect(resultCache.setFullResults).not.toHaveBeenCalled();
      expect(resultCache.setScoreMap).not.toHaveBeenCalled();
    });

    it('should handle errors gracefully', async () => {
      // Arrange
      const context: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
        userId: 'user-1',
      };

      const jobs = Array(25)
        .fill(0)
        .map((_, i) => ({ id: i + 1 }) as unknown as Job);
      const scoreMap = new Map(jobs.map((job) => [String(job.id), 0.9]));

      // Setup mocks to throw error
      mockResultCache.setFullResults.mockRejectedValue(
        new Error('Cache error'),
      );

      // Act & Assert - should not throw
      await expect(
        service.storeResultsForPagination(context, jobs, scoreMap),
      ).resolves.not.toThrow();
    });
  });

  describe('getContextForFullResultsFetch', () => {
    it('should create a context with expanded limit and page 1', () => {
      // Arrange
      const originalContext: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 3, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
        userId: 'user-1',
      };

      // Act
      const result = service.getContextForFullResultsFetch(originalContext);

      // Assert
      expect(result).not.toBe(originalContext); // Should be a new object
      expect(result.pagination.page).toBe(1); // Always page 1
      expect(result.pagination.limit).toBe(50); // 5x original limit (10*5=50)

      // Other fields should be preserved
      expect(result.userId).toBe(originalContext.userId);
      expect(result.filters).toBe(originalContext.filters);
    });

    it('should respect MAX_RESULTS when expanding context', () => {
      // Arrange
      const originalContext: SearchContext = {
        searchParams: { query: 'test' },
        skillContexts: [],
        pagination: { page: 1, limit: 100 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
        userId: 'user-1',
      };

      // Act
      const result = service.getContextForFullResultsFetch(originalContext);

      // Assert
      // Should limit to MAX_RESULTS (200) instead of the 5x calculation (500)
      expect(result.pagination.limit).toBe(200);
    });
  });
});