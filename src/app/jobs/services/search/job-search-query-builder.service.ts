import { Injectable, Logger } from '@nestjs/common';
import { JobSearchParams } from '../interfaces/job-search.interface';
import { SearchContext } from './search-context.interface';

@Injectable()
export class JobSearchQueryBuilder {
  private readonly logger = new Logger(JobSearchQueryBuilder.name);

  /**
   * Builds a search context from the provided search parameters
   */
  buildSearchContext(
    searchParams: JobSearchParams,
    keywords?: string,
    skillContexts: string[] = [],
  ): SearchContext {
    this.logger.debug(
      `Building search context with params: ${JSON.stringify(searchParams)}`,
    );

    // Extract filters and pagination from request
    const {
      smart_profile,
      smartProfile,
      rerank,
      page,
      limit = searchParams.limit || 20,
      offset,
      sort_by,
      sort_direction = 'desc',
      user_id,
      useCache = true,
      ...filters
    } = searchParams;

    let { query } = searchParams;

    // If keywords are provided, override query
    if (keywords && keywords.trim()) {
      query = keywords.trim();
    }

    // Convert offset/limit to page/limit if offset is provided
    // But store the original offset so we can use it later
    const effectivePage =
      offset !== undefined ? Math.floor(offset / limit) + 1 : page || 1;

    this.logger.debug(
      `Pagination calculation: offset=${offset}, limit=${limit}, derived page=${effectivePage}`,
    );

    // Determine if we should use vector search
    const useVectorSearch = !!(keywords || (query && query.trim()));

    return {
      searchParams,
      keywords: query,
      skillContexts,
      pagination: {
        page: effectivePage,
        limit,
      },
      sorting: {
        sortBy: sort_by,
        sortDirection: sort_direction as 'asc' | 'desc',
      },
      filters,
      useVectorSearch,
      userId: user_id,
      useCache,
    };
  }

  /**
   * Builds Pinecone filter object from search context
   */
  buildPineconeFilters(context: SearchContext): Record<string, any> {
    const pineconeFilters: Record<string, any> = {};

    // Add default filter to exclude expired jobs
    const currentTimestamp = Math.floor(Date.now() / 1000);
    pineconeFilters.expires_at = { $gt: currentTimestamp };

    const locationFilterKeys = [
      'municipality_names',
      'municipality_codes',
      'region_names',
      'region_codes',
    ];

    // Add filters to the query
    Object.entries(context.filters).forEach(([key, value]) => {
      if (
        value !== undefined &&
        value !== null &&
        value !== '' &&
        key !== 'query'
      ) {
        if (locationFilterKeys.includes(key)) {
          // For specific location keys, ensure value is an array and use $in
          const filterValue = Array.isArray(value) ? value : [value];
          if (filterValue.length > 0) {
            pineconeFilters[key] = { $in: filterValue };
          }
        } else if (Array.isArray(value)) {
          // Handle other array values (e.g., skills)
          pineconeFilters[key] = { $in: value };
        } else {
          // Handle other scalar values
          pineconeFilters[key] = { $eq: value };
        }
      }
    });

    this.logger.debug(
      `Built Pinecone filters: ${JSON.stringify(pineconeFilters)}`,
    );
    return pineconeFilters;
  }

  /**
   * Builds Pinecone query options
   */
  buildQueryOptions(
    context: SearchContext,
    queryVector: number[],
    pineconeFilters: Record<string, any>,
  ): any {
    const { pagination, useVectorSearch } = context;
    const { limit } = pagination;

    // Prepare the query options
    const queryOptions: any = {
      includeMetadata: true,
      vector: queryVector,
    };

    // If using vector search, include the vector in the query
    if (useVectorSearch) {
      queryOptions.topK = limit * 5; // Request more than we need in case some jobs are missing
    } else {
      // When not using vector search, we rely solely on metadata filtering
      queryOptions.topK = limit + 1; // +1 to see if it has_next
    }

    // Apply filters if they exist
    if (Object.keys(pineconeFilters).length > 0) {
      queryOptions.filter = pineconeFilters;
    }

    return queryOptions;
  }
}