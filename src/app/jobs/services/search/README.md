# Job Search Service Architecture

This directory contains the refactored job search service components. The refactoring aims to address several issues with the original monolithic design:

1. **Single Responsibility Principle**: Each service now has a clearly defined responsibility
2. **Improved Testability**: Components can be tested in isolation
3. **Increased Maintainability**: Easier to understand and modify individual components
4. **Performance**: Added caching and improved query optimization
5. **Flexibility**: Easier to add new features and modify existing ones

## Components

### SearchContext Interface

Defines the core data structure used by all search-related services, encapsulating:
- Original search parameters
- Pagination details
- Sorting criteria
- Filter settings
- Semantic search configuration

### JobSearchQueryBuilder

Responsible for:
- Building search contexts from request parameters
- Constructing filter objects for Pinecone queries
- Managing pagination parameters
- Building query options

### JobSearchVectorService

Handles vector operations:
- Generating embeddings for search queries
- Managing connections to the vector database
- Performing reranking of search results
- Handling multi-context searches

### JobSearchResultProcessor

Processes search results:
- Formats job data for API responses
- Handles sorting and pagination of results
- Calculates pagination metadata
- Formats final response

### JobSearchCacheService

Manages caching of search results:
- Generates deterministic cache keys from search contexts
- Stores and retrieves cached results
- Implements invalidation strategies
- Manages TTL for cached results

## Data Flow

1. Client sends search request to JobsSearchService
2. JobsSearchService uses QueryBuilder to build a SearchContext
3. Cache is checked for existing results with the same parameters
4. If cache hit, return cached results
5. Otherwise, generate query vector using VectorService
6. Execute search against Pinecone using the vector and filters
7. Process the raw results with ResultProcessor
8. Cache the final results
9. Return formatted results to client

## Caching Strategy

- Cache keys are generated using MD5 hash of search parameters
- Default TTL is 1 hour for regular results, 5 minutes for empty results
- Cache can be selectively invalidated based on specific criteria
- Cached results include full response format (jobs and metadata)

## Usage

```typescript
// Example search with basic parameters
const result = await jobsSearchService.searchJobs({
  limit: 20,
  page: 1,
  query: 'software developer',
});

// Example search with filters
const result = await jobsSearchService.searchJobs({
  limit: 20,
  page: 1,
  query: 'software developer',
  location: 'Helsinki',
  employment_type: 'full_time',
});

// Example search with smart profile
const result = await jobsSearchService.getTopJobs({
  smart_profile: {
    keywords: ['javascript', 'react', 'node.js'],
    skill_contexts: ['Frontend Development', 'Web Applications']
  },
  limit: 10,
  page: 1,
});
```