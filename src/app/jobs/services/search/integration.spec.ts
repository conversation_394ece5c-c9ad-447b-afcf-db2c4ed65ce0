import { Test, TestingModule } from '@nestjs/testing';
import { JobsSearchService } from '../jobs-search.service';
import { 
  JobSearchQueryBuilder,
  JobSearchVectorService,
  JobSearchResultProcessor,
  JobSearchCacheService,
} from './index';
import { JobsImportRepository } from '../../repository/jobs-import.repository';
import { ForYouRepository } from '../../repository/for-you.repository';
import { UserApiService } from '../../../user/services/user-api.service';
import { RecommendationEngineService } from '../recommendation-engine.service';
import { JobsPineconeService } from '../jobs-pinecone.service';
import { PineconeService } from '../../../../pinecone/pinecone.service';
import { CacheService } from '../../../../cache/cache.service';
import { Logger } from '@nestjs/common';
import { SearchContext } from './search-context.interface';

// Mock implementations
const mockJobsImportRepository = {
  findJobsWithDetails: jest.fn(),
  findByIds: jest.fn(),
  findByExtId: jest.fn(),
};

const mockPineconeIndex = {
  query: jest.fn().mockResolvedValue({
    matches: [
      { 
        id: 'job1', 
        score: 0.9, 
        metadata: { 
          title: 'Software Engineer',
          skills: 'JavaScript, TypeScript, React'
        }
      },
      { 
        id: 'job2', 
        score: 0.8, 
        metadata: { 
          title: 'Frontend Developer',
          skills: 'HTML, CSS, JavaScript, React'
        }
      },
      { 
        id: 'job3', 
        score: 0.7, 
        metadata: { 
          title: 'Backend Developer',
          skills: 'Node.js, TypeScript, Express'
        }
      }
    ]
  })
};

const mockPineconeService = {
  getDefaultIndex: jest.fn().mockResolvedValue(mockPineconeIndex),
  generateEmbeddingsV2: jest.fn().mockResolvedValue([0.1, 0.2, 0.3]),
  dimension: 1536,
};

const mockPineconeClientService = {
  getPineconeClient: jest.fn().mockReturnValue({
    inference: {
      rerank: jest.fn().mockResolvedValue({
        data: [
          { score: 0.95, document: { id: 'job1' } },
          { score: 0.85, document: { id: 'job2' } },
          { score: 0.75, document: { id: 'job3' } },
        ]
      }),
    },
  }),
};

const mockCacheService = {
  get: jest.fn().mockResolvedValue(null),
  set: jest.fn().mockResolvedValue(undefined),
  delete: jest.fn(),
};

// Mock job data
const mockJobs = [
  { 
    id: 1, 
    ext_id: 'job1',
    title: 'Software Engineer', 
    description: 'Develop applications using JavaScript and TypeScript'
  },
  { 
    id: 2, 
    ext_id: 'job2',
    title: 'Frontend Developer', 
    description: 'Build UIs with React and modern CSS'
  },
  { 
    id: 3, 
    ext_id: 'job3',
    title: 'Backend Developer', 
    description: 'Develop server-side applications with Node.js'
  }
];

describe('JobSearch Services Integration', () => {
  let jobsSearchService: JobsSearchService;
  let queryBuilder: JobSearchQueryBuilder;
  let vectorService: JobSearchVectorService;
  let resultProcessor: JobSearchResultProcessor;
  let cacheService: JobSearchCacheService;

  beforeEach(async () => {
    // Reset mocks
    jest.clearAllMocks();
    
    mockJobsImportRepository.findJobsWithDetails.mockResolvedValue(mockJobs);

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        JobsSearchService,
        JobSearchQueryBuilder,
        {
          provide: JobSearchVectorService,
          useClass: JobSearchVectorService,
        },
        JobSearchResultProcessor,
        {
          provide: JobSearchCacheService,
          useClass: JobSearchCacheService,
        },
        {
          provide: JobsImportRepository,
          useValue: mockJobsImportRepository,
        },
        {
          provide: ForYouRepository,
          useValue: { getForYouJobs: jest.fn(), saveForYouJobs: jest.fn() },
        },
        {
          provide: UserApiService,
          useValue: { getUserKeywordsData: jest.fn() },
        },
        {
          provide: RecommendationEngineService,
          useValue: { getRecommendations: jest.fn() },
        },
        {
          provide: JobsPineconeService,
          useValue: mockPineconeService,
        },
        {
          provide: PineconeService,
          useValue: mockPineconeClientService,
        },
        {
          provide: CacheService,
          useValue: mockCacheService,
        },
      ],
    }).compile();

    jobsSearchService = module.get<JobsSearchService>(JobsSearchService);
    queryBuilder = module.get<JobSearchQueryBuilder>(JobSearchQueryBuilder);
    vectorService = module.get<JobSearchVectorService>(JobSearchVectorService);
    resultProcessor = module.get<JobSearchResultProcessor>(JobSearchResultProcessor);
    cacheService = module.get<JobSearchCacheService>(JobSearchCacheService);

    // Silence the logger during tests
    jest.spyOn(Logger.prototype, 'log').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'debug').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'error').mockImplementation(() => {});
    jest.spyOn(Logger.prototype, 'warn').mockImplementation(() => {});
  });

  it('should perform a complete search with all components working together', async () => {
    // Arrange
    const searchParams = {
      query: 'JavaScript developer',
      limit: 10,
      page: 1,
    };

    // Act
    const result = await jobsSearchService.searchJobs(searchParams);

    // Assert
    // Check that the query builder was used to build the search context
    expect(mockPineconeService.getDefaultIndex).toHaveBeenCalled();
    expect(mockPineconeService.generateEmbeddingsV2).toHaveBeenCalled();
    expect(mockPineconeIndex.query).toHaveBeenCalled();
    expect(mockJobsImportRepository.findJobsWithDetails).toHaveBeenCalled();
    
    // Check that the result is as expected
    expect(result.jobs).toBeDefined();
    expect(result.meta).toBeDefined();
    expect(result.meta.total).toBeGreaterThan(0);
  });

  it('should use cache when available', async () => {
    // Arrange
    const searchParams = {
      query: 'JavaScript developer',
      limit: 10,
      page: 1,
    };

    const cachedResult = {
      jobs: [{ id: 'cached-job', title: 'Cached Job' }],
      meta: { total: 1, page: 1, limit: 10, has_next: false },
    };

    // Mock the cache service to return a cached result
    jest.spyOn(mockCacheService, 'get').mockResolvedValueOnce(cachedResult);

    // Act
    const result = await jobsSearchService.searchJobs(searchParams);

    // Assert
    expect(mockCacheService.get).toHaveBeenCalled();
    expect(mockPineconeService.getDefaultIndex).not.toHaveBeenCalled(); // Should not perform search
    expect(result).toEqual(cachedResult);
  });

  it('should cache results after searching', async () => {
    // Arrange
    const searchParams = {
      query: 'JavaScript developer',
      limit: 10,
      page: 1,
    };

    // Act
    await jobsSearchService.searchJobs(searchParams);

    // Assert
    expect(mockCacheService.set).toHaveBeenCalled();
  });

  describe('Component Interaction Tests', () => {
    it('JobSearchQueryBuilder should build correct search context', () => {
      // Arrange
      const searchParams = {
        query: 'JavaScript developer',
        limit: 10,
        page: 1,
        location: 'Helsinki',
      };
      
      const keywords = 'JavaScript React';
      const skillContexts = ['Frontend Development', 'Web Development'];
      
      // Act
      const context = queryBuilder.buildSearchContext(searchParams, keywords, skillContexts);
      
      // Assert
      expect(context.searchParams).toEqual(searchParams);
      expect(context.keywords).toEqual(keywords);
      expect(context.skillContexts).toEqual(skillContexts);
      expect(context.pagination).toEqual({ page: 1, limit: 10 });
      expect(context.useVectorSearch).toBe(true);
    });
    
    it('JobSearchVectorService should use Pinecone for vector operations', async () => {
      // This test would need the actual service instances
      // Here we're just testing that the mock was called correctly
      const context: SearchContext = {
        searchParams: { query: 'JavaScript' },
        keywords: 'JavaScript',
        skillContexts: [],
        pagination: { page: 1, limit: 10 },
        sorting: { sortDirection: 'desc' },
        filters: {},
        useVectorSearch: true,
      };
      
      await vectorService.generateQueryVector(context);
      
      expect(mockPineconeService.generateEmbeddingsV2).toHaveBeenCalledWith('JavaScript');
    });
    
    it('JobSearchResultProcessor should format results correctly', () => {
      // Would need the actual service for this test
      // Here's a simple assertion that it runs without errors
      expect(resultProcessor).toBeDefined();
    });
  });
});