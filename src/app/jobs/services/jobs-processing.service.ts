import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { CompletionClient } from 'dify-client';

import { IJob } from '../repository/interfaces/job.interface';
import { JobsImportRepository } from '../repository/jobs-import.repository';
import { IClassification } from '../repository/interfaces/classification.interface';
import { mapResponseToClassification } from './mappers/classification.mapper';

@Injectable()
export class JobsProcessingService {
  private completionClient: CompletionClient;

  constructor(
    private readonly config: ConfigService,
    private readonly jobsRepository: JobsImportRepository,
  ) {
    const [key, link] = [
      this.config.get<string>('JOB_PROCESSING_API_KEY'),
      this.config.get<string>('WORKFLOW_API_URL'),
    ];

    if (!key || !link) throw new Error('JobProcessingService: env required.');

    this.completionClient = new CompletionClient(key, link);
  }

  async processJobs(limit = 100) {
    let offset = 0;
    let hasNextPage = true;

    do {
      console.log(
        `[JOB-PROCESSING]: Starting from offset: ${offset}, limit: ${limit}`,
      );

      const jobs: IJob[] = await this.jobsRepository.find(limit, offset);

      for (const job of jobs) {
        const descriptionEn = job.translation.find(
          (item) => item.language === 'en',
        )?.description;
        if (!descriptionEn) continue;

        const classification = await this.getClassificationData(
          descriptionEn,
          job.employer_name || '',
          job.ext_id,
        );
        console.log(classification);
        await this.jobsRepository.upsertProcessedJobs(job, classification);
      }

      offset += jobs.length;
      if (jobs.length < limit) hasNextPage = false;

      console.log(
        `[JOB-PROCESSING]: Processed: ${jobs.length}, hasNextPage - ${hasNextPage}.`,
      );
    } while (hasNextPage);
  }

  private async getClassificationData(
    job_description: string,
    employer: string,
    ext_id: string,
  ): Promise<IClassification> {
    const response = await this.completionClient.createCompletionMessage(
      { job_description, employer, ext_id },
      'translation-service',
    );

    const data = response.data.answer.replace(/```json|```/g, '').trim();
    const parsedData = JSON.parse(data);

    // Use the mapper to convert the response to IClassification format
    return mapResponseToClassification(parsedData);
  }
}
