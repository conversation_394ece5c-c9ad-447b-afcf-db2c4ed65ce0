import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { JobApplication } from '../repository/job-application.entity';
import {
  JobApplicationDto,
  JobApplicationResponseDto,
} from '../dtos/job-application.dto';
import { plainToInstance } from 'class-transformer';
import { Job } from '../../../entities/job.entity';

@Injectable()
export class JobApplicationService {
  private readonly logger = new Logger(JobApplicationService.name);

  constructor(
    @InjectRepository(JobApplication)
    private readonly jobApplicationRepository: Repository<JobApplication>,
    @InjectRepository(Job)
    private readonly jobRepository: Repository<Job>,
  ) {}

  /**
   * Create a new job application
   * @param applicationData Job application data from CV generation
   * @returns Created job application entity
   */
  async createJobApplication(
    applicationData: JobApplicationDto,
  ): Promise<JobApplicationResponseDto> {
    this.logger.log(
      `Creating job application for user ${applicationData.userId} to job ${applicationData.jobExtId}`,
    );

    // Verify the job exists
    const job = await this.jobRepository.findOne({
      where: { ext_id: applicationData.jobExtId },
    });

    if (!job) {
      throw new NotFoundException(
        `Job with external ID ${applicationData.jobExtId} not found`,
      );
    }

    // Create a new job application entity
    const jobApplication = new JobApplication();
    jobApplication.userId = applicationData.userId;
    jobApplication.cvHtml = applicationData.cvHtml;
    jobApplication.cvUrl = applicationData.cvUrl || ''; // Handle undefined
    jobApplication.jobExtId = applicationData.jobExtId;
    jobApplication.selectedLanguage = applicationData.selectedLanguage;
    jobApplication.language = applicationData.language || ''; // Handle undefined
    jobApplication.documentType = applicationData.documentType;
    jobApplication.selectedTemplate = applicationData.selectedTemplate;
    jobApplication.sections = applicationData.sections;
    jobApplication.generatedAboutMe = applicationData.generatedAboutMe || ''; // Handle undefined
    jobApplication.keyFitPoints = applicationData.keyFitPoints || []; // Handle undefined

    try {
      // Save the job application
      const savedApplication =
        await this.jobApplicationRepository.save(jobApplication);
      this.logger.log(
        `Successfully created job application with ID ${savedApplication.id}`,
      );

      // Return the response DTO
      return this.mapToResponseDto(savedApplication);
    } catch (error) {
      this.logger.error(
        `Failed to create job application: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to create job application');
    }
  }

  /**
   * Get all job applications for a user
   * @param userId User's Supertoken ID
   * @returns List of job applications
   */
  async getJobApplicationsByUserId(
    userId: string,
  ): Promise<JobApplicationResponseDto[]> {
    this.logger.log(`Fetching job applications for user ${userId}`);

    try {
      const applications = await this.jobApplicationRepository.find({
        where: { userId },
        order: { createdAt: 'DESC' },
      });

      return applications.map((app) => this.mapToResponseDto(app));
    } catch (error) {
      this.logger.error(
        `Failed to fetch job applications: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to fetch job applications');
    }
  }

  /**
   * Get a specific job application by ID
   * @param id Job application ID
   * @returns Job application if found
   */
  async getJobApplicationById(id: string): Promise<JobApplicationResponseDto> {
    this.logger.log(`Fetching job application with ID ${id}`);

    try {
      const application = await this.jobApplicationRepository.findOne({
        where: { id },
      });

      if (!application) {
        throw new NotFoundException(`Job application with ID ${id} not found`);
      }

      return this.mapToResponseDto(application);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(
        `Failed to fetch job application: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to fetch job application');
    }
  }

  /**
   * Maps a JobApplication entity to a JobApplicationResponseDto
   * @param application JobApplication entity
   * @returns JobApplicationResponseDto
   */
  private mapToResponseDto(
    application: JobApplication,
  ): JobApplicationResponseDto {
    return plainToInstance(JobApplicationResponseDto, {
      id: application.id,
      userId: application.userId,
      cvUrl: application.cvUrl,
      jobExtId: application.jobExtId,
      createdAt: application.createdAt,
      updatedAt: application.updatedAt,
    });
  }

  /**
   * Check if a user has already applied to a specific job
   * @param userId User's Supertoken ID
   * @param jobExtId External job ID
   * @returns Boolean indicating whether the user has applied to the job
   */
  async hasUserAppliedToJob(
    userId: string,
    jobExtId: string,
  ): Promise<boolean> {
    this.logger.log(
      `Checking if user ${userId} has applied to job ${jobExtId}`,
    );

    try {
      const application = await this.jobApplicationRepository.findOne({
        where: { userId, jobExtId },
      });

      return application !== null;
    } catch (error) {
      this.logger.error(
        `Error checking job application status: ${error.message}`,
        error.stack,
      );
      throw new BadRequestException('Failed to check job application status');
    }
  }
}
