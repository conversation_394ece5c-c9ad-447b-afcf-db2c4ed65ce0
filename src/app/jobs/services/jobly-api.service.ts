import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError } from 'axios';
import { JoblyJob } from './interfaces/jobly-job.interface';

/**
 * Service for interacting with the Jobly API
 */
@Injectable()
export class JoblyApiClient {
  private readonly logger = new Logger(JoblyApiClient.name);
  private readonly apiEndpoint: string;
  private readonly cacheTtl: number;
  private readonly importCacheKey: string;
  private readonly importCompletedCacheKey: string;

  constructor(private readonly configService: ConfigService) {
    this.apiEndpoint = this.configService.getOrThrow<string>('JOBLY_API_ENDPOINT');
    this.cacheTtl = this.configService.get<number>('JOBLY_CACHE_TTL', 3600); // Default to 1 hour
    this.importCacheKey = this.configService.getOrThrow<string>('JOBLY_IMPORT_CACHE_KEY');
    this.importCompletedCacheKey = this.configService.getOrThrow<string>('JOBLY_IMPORT_COMPLETED_CACHE_KEY');
    
    this.logger.log(`Initialized JoblyApiClient with endpoint: ${this.apiEndpoint}`);
  }

  /**
   * Fetches job postings from the Jobly API
   * @returns Array of JoblyJob objects
   */
  async getJobPostings(): Promise<JoblyJob[]> {
    try {
      this.logger.log(`Fetching job postings from Jobly API: ${this.apiEndpoint}`);
      
      const response = await axios.get<JoblyJob[]>(this.apiEndpoint, {
        timeout: 30000, // 30 seconds timeout
        headers: {
          'Accept': 'application/json',
        }
      });
      
      const jobPostings = response.data;
      this.logger.log(`Successfully fetched ${jobPostings.length} job postings from Jobly API`);
      
      return jobPostings;
    } catch (error) {
      this.handleApiError(error as Error);
      throw new Error('Failed to fetch job postings from Jobly API');
    }
  }
  
  /**
   * Handles and logs API errors with appropriate context
   * @param error The error object to handle
   */
  private handleApiError(error: Error): void {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      
      let errorMessage = `Jobly API error: ${axiosError.message}`;
      let errorContext: any = { url: this.apiEndpoint };
      
      if (axiosError.response) {
        // Server responded with an error status code
        errorMessage += ` (Status: ${axiosError.response.status})`;
        errorContext = {
          ...errorContext,
          status: axiosError.response.status,
          data: axiosError.response.data,
        };
      } else if (axiosError.request) {
        // Request was made but no response received (network error)
        errorMessage += ' (No response received)';
        errorContext = {
          ...errorContext,
          request: axiosError.request,
        };
      }
      
      this.logger.error(errorMessage, errorContext);
    } else {
      // Generic error handling
      this.logger.error(`Jobly API error: ${error.message}`, error.stack);
    }
  }
  
  /**
   * Gets the cache key for Jobly import processing
   */
  getCacheKey(): string {
    return this.importCacheKey;
  }
  
  /**
   * Gets the cache key for completed Jobly imports
   */
  getCompletedCacheKey(): string {
    return this.importCompletedCacheKey;
  }
  
  /**
   * Gets the cache TTL for Jobly imports
   */
  getCacheTtl(): number {
    return this.cacheTtl;
  }
}