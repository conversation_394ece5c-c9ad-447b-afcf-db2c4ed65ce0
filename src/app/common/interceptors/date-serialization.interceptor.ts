import { Injectable, NestInterceptor, Execution<PERSON>ontext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

/**
 * Interceptor to ensure consistent Date serialization across all responses
 * regardless of the Accept-Header language.
 */
@Injectable()
export class DateSerializationInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map(data => {
        // Process the response data
        return this.serializeDates(data);
      }),
    );
  }

  /**
   * Recursively serializes Date objects to ISO strings
   */
  private serializeDates(data: any): any {
    if (data === null || data === undefined) {
      return data;
    }

    if (data instanceof Date) {
      return data.toISOString();
    }

    if (Array.isArray(data)) {
      return data.map(item => this.serializeDates(item));
    }

    if (typeof data === 'object') {
      const result = {};
      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          result[key] = this.serializeDates(data[key]);
        }
      }
      return result;
    }

    return data;
  }
}
