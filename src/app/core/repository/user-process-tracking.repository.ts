import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { 
  UserProcessTracking, 
  ProcessEntityType, 
  ProcessStatus 
} from '../../../entities/user-process-tracking.entity';

/**
 * Repository for managing user process tracking
 */
@Injectable()
export class UserProcessTrackingRepository {
  private readonly logger = new Logger(UserProcessTrackingRepository.name);

  constructor(
    @InjectRepository(UserProcessTracking)
    private readonly repository: Repository<UserProcessTracking>,
  ) {}

  /**
   * Create a new process tracking record
   */
  async createProcess(
    userId: string, 
    entityType: ProcessEntityType, 
    metadata?: Record<string, any>
  ): Promise<UserProcessTracking> {
    const process = this.repository.create({
      userId,
      entityType,
      status: ProcessStatus.PENDING,
      metadata,
    });

    return this.repository.save(process);
  }

  /**
   * Update the status of a process
   */
  async updateStatus(
    id: number, 
    status: ProcessStatus, 
    error?: string
  ): Promise<UserProcessTracking> {
    const process = await this.repository.findOneBy({ id });
    
    if (!process) {
      throw new Error(`Process tracking record with ID ${id} not found`);
    }

    process.status = status;
    
    if (status === ProcessStatus.IN_PROGRESS && !process.startedAt) {
      process.startedAt = new Date();
    } else if (status === ProcessStatus.COMPLETED || status === ProcessStatus.FAILED) {
      process.completedAt = new Date();
      
      if (status === ProcessStatus.FAILED && error) {
        process.errorMessage = error;
      }
    }

    return this.repository.save(process);
  }

  /**
   * Find process by user ID and entity type
   */
  async findByUserAndType(
    userId: string, 
    entityType: ProcessEntityType
  ): Promise<UserProcessTracking | null> {
    return this.repository.findOne({
      where: { userId, entityType, isActive: true },
      order: { createdAt: 'DESC' }
    });
  }

  /**
   * Delete a process tracking record
   */
  async deleteProcess(id: number): Promise<void> {
    await this.repository.delete(id);
  }

  /**
   * Mark a process as complete and inactive
   */
  async completeProcess(id: number): Promise<void> {
    await this.updateStatus(id, ProcessStatus.COMPLETED);
    
    // Set isActive to false to remove it from future queries
    const process = await this.repository.findOneBy({ id });
    if (process) {
      process.isActive = false;
      await this.repository.save(process);
    }
  }

  /**
   * Mark a process as failed
   */
  async failProcess(id: number, error: string): Promise<void> {
    await this.updateStatus(id, ProcessStatus.FAILED, error);
  }

  /**
   * Check if a user has a pending or in-progress task of a specific type
   */
  async hasActiveProcess(userId: string, entityType: ProcessEntityType): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        userId,
        entityType,
        isActive: true,
        status: In([ProcessStatus.PENDING, ProcessStatus.IN_PROGRESS]),
      }
    });
    
    return count > 0;
  }
}
