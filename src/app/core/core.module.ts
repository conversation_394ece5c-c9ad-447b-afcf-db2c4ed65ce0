import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserProcessTracking } from '../../entities/user-process-tracking.entity';
import { UserProcessTrackingRepository } from './repository/user-process-tracking.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([UserProcessTracking]),
  ],
  providers: [UserProcessTrackingRepository],
  exports: [UserProcessTrackingRepository],
})
export class CoreModule {}
