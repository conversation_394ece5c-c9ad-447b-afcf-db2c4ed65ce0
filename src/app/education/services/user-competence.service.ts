import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { UserCompetence } from '../../../entities/user-competence.entity';
import { Competence } from '../../../entities/competence.entity';
import { CompetenceType } from '../../../entities/competence-type.enum';
import { SelectUserCompetencesDto } from '../dto/user-competence.dto';
import { CompetenceService } from './competence.service';

@Injectable()
export class UserCompetenceService {
  constructor(
    @InjectRepository(UserCompetence)
    private userCompetenceRepository: Repository<UserCompetence>,
    @InjectRepository(Competence)
    private competenceRepository: Repository<Competence>,
    private competenceService: CompetenceService,
  ) {}

  async findByUserId(supertokensId: string): Promise<UserCompetence[]> {
    return this.userCompetenceRepository.find({
      where: { supertokens_id: supertokensId },
      relations: ['competence', 'competence.program']
    });
  }

  async findByUserAndProgram(supertokensId: string, programId: number): Promise<UserCompetence[]> {
    return this.userCompetenceRepository
      .createQueryBuilder('userCompetence')
      .innerJoinAndSelect('userCompetence.competence', 'competence')
      .innerJoinAndSelect('competence.program', 'program')
      .where('userCompetence.supertokens_id = :supertokensId', { supertokensId })
      .andWhere('competence.programId = :programId', { programId })
      .getMany();
  }

  async selectUserCompetences(selectDto: SelectUserCompetencesDto): Promise<UserCompetence[]> {
    const { supertokens_id, competenceIds } = selectDto;
    
    // Validate that all competenceIds exist
    for (const competenceId of competenceIds) {
      const competence = await this.competenceRepository.findOne({
        where: { id: competenceId }
      });
      
      if (!competence) {
        throw new NotFoundException(`Competence with ID ${competenceId} not found`);
      }
    }
    
    // First, remove existing competence selections for this user that are not in the new list
    await this.userCompetenceRepository
      .createQueryBuilder()
      .delete()
      .from(UserCompetence)
      .where('supertokens_id = :supertokensId', { supertokensId: supertokens_id })
      .andWhere('competenceId NOT IN (:...competenceIds)', { competenceIds: competenceIds.length > 0 ? competenceIds : [0] })
      .execute();
    
    // Now add any new ones that don't already exist
    const userCompetences: UserCompetence[] = [];
    
    for (const competenceId of competenceIds) {
      // Check if this user-competence pair already exists
      const existing = await this.userCompetenceRepository.findOne({
        where: {
          supertokens_id: supertokens_id,
          competenceId: competenceId
        }
      });
      
      // If it doesn't exist, create it
      if (!existing) {
        const userCompetence = this.userCompetenceRepository.create({
          supertokens_id,
          competenceId
        });
        userCompetences.push(await this.userCompetenceRepository.save(userCompetence));
      } else {
        userCompetences.push(existing);
      }
    }
    
    return this.findByUserId(supertokens_id);
  }

  async selectUserProgram(supertokensId: string, programId: number): Promise<UserCompetence[]> {
    // Get all CORE competences for the program
    const coreCompetences = await this.competenceService.findByProgramAndType(
      programId,
      CompetenceType.CORE
    );
    
    if (coreCompetences.length === 0) {
      throw new NotFoundException(`No core competences found for program ID ${programId}`);
    }
    
    // Select all core competences for the user automatically
    const competenceIds = coreCompetences.map(c => c.id);
    
    return this.selectUserCompetences({
      supertokens_id: supertokensId,
      competenceIds
    });
  }
}
