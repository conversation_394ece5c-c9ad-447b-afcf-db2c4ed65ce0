import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Industry } from '../../../entities/industry.entity';
import {
  IndustryRecommendationRequestDto,
  IndustryRecommendationResponseDto,
} from '../dto/industry-recommendation.dto';
import { IndustryViewDto } from '../dto/industry-view.dto';
import * as fs from 'fs';
import * as path from 'path';
import { CompletionClient } from 'dify-client';
import * as process from 'node:process';
import { CacheService } from '../../../cache/cache.service';

@Injectable()
export class IndustryService {
  private completionClient: CompletionClient;

  constructor(
    @InjectRepository(Industry)
    private industryRepository: Repository<Industry>,
    private readonly cacheService: CacheService,
  ) {
    if (
      !process.env.WORKFLOW_EDUCATION_INDUSTRY_LIST_API_KEY ||
      !process.env.WORKFLOW_EDUCATION_INDUSTRY_VIEW_API_KEY
    ) {
      throw new Error(
        'WORKFLOW_EDUCATION_XXX_API_KEY environment variable is not set',
      );
    }

    if (!process.env.WORKFLOW_API_URL) {
      throw new Error('WORKFLOW_API_URL environment variable is not set');
    }

    const [API_KEY, DIFY_SERVER] = [
      process.env.WORKFLOW_EDUCATION_INDUSTRY_LIST_API_KEY,
      process.env.WORKFLOW_API_URL,
    ];
    this.completionClient = new CompletionClient(API_KEY, DIFY_SERVER);
  }

  private readProgramDescription(programCode: string): string {
    try {
      const projectRoot =
        process.env.NODE_ENV === 'production'
          ? '/app' // In Docker production, files are in /app
          : path.join(__dirname, '../../../..'); // In development, src/education/services -> project root

      const filePath = path.join(
        projectRoot,
        'research_programs',
        `${programCode}.json`,
      );
      console.log('Attempting to read program file from:', filePath);

      const fileContent = fs.readFileSync(filePath, 'utf8');
      const programData: any = JSON.parse(fileContent);
      return programData || '';
    } catch (error) {
      console.error(`Error reading program file for ${programCode}:`, error);
      return '';
    }
  }

  async getRecommendedIndustries(
    userId: string,
    dto: IndustryRecommendationRequestDto,
  ): Promise<IndustryRecommendationResponseDto[]> {
    const cacheKey = `recommended-industries:${dto.programCode}:${dto.responseLanguage}`;

    try {
      // Try to get from cache first
      const cachedResult =
        await this.cacheService.get<IndustryRecommendationResponseDto[]>(
          cacheKey,
        );
      if (cachedResult) {
        console.log(`Serving cached result for ${cacheKey}`);
        return cachedResult;
      } else {
        console.log('cachedResult: ', cachedResult);
      }
    } catch (error) {
      console.error('Redis cache error:', error);
      // Continue with LLM call if cache fails
    }

    // If not in cache or cache failed, proceed with LLM call
    this.completionClient.updateApiKey(
      process.env.WORKFLOW_EDUCATION_INDUSTRY_LIST_API_KEY!,
    );

    const user = `random-user-id`;
    const programDescription = this.readProgramDescription(dto.programCode);

    const query = {
      language: dto.responseLanguage,
      languages_spoken: JSON.stringify(dto.languagesSpoken),
      degree_program_description: JSON.stringify(programDescription),
    };

    console.log(
      `Sending request to industry recommendation API with query:`,
      query,
    );

    // Create a completion client
    const completionMessage: { data: { answer: string } } =
      await this.completionClient.createCompletionMessage({ ...query }, user);

    console.log('Received completion message:', completionMessage.data.answer);

    // Clean up the response string by removing json code block markers
    const cleanResponse = completionMessage.data.answer
      .replace(/```json\n?/g, '') // Remove opening json code block
      .replace(/```\n?/g, '') // Remove closing code block
      .trim(); // Remove any extra whitespace

    const response: IndustryRecommendationResponseDto[] =
      JSON.parse(cleanResponse);
    if (!Array.isArray(response) || response.length === 0) {
      throw new Error('Invalid response from recommendation API');
    }

    // Sort by alignmentScore in descending order
    const sortedResponse = response.sort(
      (a, b) => b.alignmentScore - a.alignmentScore,
    );

    // Cache the result
    try {
      await this.cacheService.set(cacheKey, sortedResponse);
      console.log(`Successfully cached recommendation with key ${cacheKey}`);
    } catch (error) {
      console.error('Redis cache set error:', error);
      // Continue even if caching fails
    }

    return sortedResponse;
  }

  async getIndustryOverview(
    userId: string,
    industryName: string,
    responseLanguage: string,
    programCode: string,
  ): Promise<IndustryViewDto> {
    // First check if industry exists
    const industry = await this.industryRepository.findOne({
      where: { code: industryName },
    });

    if (!industry) {
      throw new NotFoundException(
        `Industry with code ${industryName} not found`,
      );
    }

    const cacheKey = `industry-overview:${industryName}:${programCode}:${responseLanguage}`;

    try {
      // Try to get from cache first
      const cachedResult =
        await this.cacheService.get<IndustryViewDto>(cacheKey);
      if (cachedResult) {
        console.log(
          `Serving cached result for ${programCode} in ${industryName}`,
        );
        return cachedResult;
      } else {
        console.log(`No cached result found for ${cacheKey}`);
      }
    } catch (error) {
      console.error('Redis cache error:', error);
      // Continue with LLM call if cache fails
    }

    // If not in cache or cache failed, proceed with LLM call
    this.completionClient.updateApiKey(
      process.env.WORKFLOW_EDUCATION_INDUSTRY_VIEW_API_KEY!,
    );
    const user = `random-user-id`;
    const programDescription = this.readProgramDescription(programCode);

    const query = {
      industry_name: industry.name,
      language: responseLanguage,
      degree_program_description: JSON.stringify(programDescription),
    };

    console.log(`Sending request to industry overview API with query:`, query);

    // Create a completion client
    const completionMessage: { data: { answer: string } } =
      await this.completionClient.createCompletionMessage({ ...query }, user);

    console.log('Received completion message:', completionMessage.data.answer);

    // Clean up the response string by removing json code block markers
    const cleanResponse = completionMessage.data.answer
      .replace(/```json\n?/g, '') // Remove opening json code block
      .replace(/```\n?/g, '') // Remove closing code block
      .trim(); // Remove any extra whitespace

    const response: IndustryViewDto = JSON.parse(cleanResponse);
    if (!response || !response.name) {
      throw new Error('Invalid response from industry overview API');
    }

    // Cache the result
    try {
      await this.cacheService.set(cacheKey, response);
      console.log(
        `Successfully cached result for ${programCode} with key ${cacheKey}`,
      );
    } catch (error) {
      console.error('Redis cache set error:', error);
      // Continue even if caching fails
    }

    return response;
  }
}
