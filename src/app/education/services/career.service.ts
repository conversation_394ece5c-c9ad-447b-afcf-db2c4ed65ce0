import { Injectable, NotFoundException } from '@nestjs/common';
import { CompletionClient } from 'dify-client';
import * as process from 'node:process';
import * as path from 'path';
import * as fs from 'fs';
import { CacheService } from '../../../cache/cache.service';
import {
  CareerPathsRequestDto,
  CareerPathResponseDto,
  CareerViewResponseDto,
} from '../dto/career-paths.dto';

@Injectable()
export class CareerService {
  private completionClient: CompletionClient;

  constructor(private readonly cacheService: CacheService) {
    if (
      !process.env.WORKFLOW_EDUCATION_CAREER_PATHS_API_KEY ||
      !process.env.WORKFLOW_EDUCATION_CAREER_VIEW_API_KEY
    ) {
      throw new Error(
        'WORKFLOW_EDUCATION_CAREER_PATHS_API_KEY environment variable is not set',
      );
    }

    if (!process.env.WORKFLOW_API_URL) {
      throw new Error('WORKFLOW_API_URL environment variable is not set');
    }

    const [API_KEY, DIFY_SERVER] = [
      process.env.WORKFLOW_EDUCATION_CAREER_PATHS_API_KEY,
      process.env.WORKFLOW_API_URL,
    ];
    this.completionClient = new CompletionClient(API_KEY, DIFY_SERVER);
  }

  private readProgramDescription(programCode: string): string {
    try {
      const projectRoot =
        process.env.NODE_ENV === 'production'
          ? '/app'
          : path.join(__dirname, '../../../..');

      const filePath = path.join(
        projectRoot,
        'research_programs',
        `${programCode}.json`,
      );

      console.log('Reading program description from:', filePath);

      if (!fs.existsSync(filePath)) {
        throw new NotFoundException(
          `Program description for ${programCode} not found`,
        );
      }

      const fileContent = fs.readFileSync(filePath, 'utf8');
      const programData: any = JSON.parse(fileContent);
      return programData || '';
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      console.error(
        `Error reading program description for ${programCode}:`,
        error,
      );
      throw new NotFoundException(
        `Unable to read program description for ${programCode}`,
      );
    }
  }

  async getCareerPaths(
    dto: CareerPathsRequestDto,
  ): Promise<CareerPathResponseDto[]> {
    const cacheKey = `career_paths:${dto.programCode}:${dto.responseLanguage}`;
    const cachedResult =
      await this.cacheService.get<CareerPathResponseDto[]>(cacheKey);

    if (cachedResult) {
      return cachedResult;
    }

    const programDescription = this.readProgramDescription(dto.programCode);

    // Call Dify API to get career recommendations
    const user = `random-user-id`;

    const query = {
      language: dto.responseLanguage,
      languages_spoken: JSON.stringify(dto.languagesSpoken),
      degree_program_description: JSON.stringify(programDescription),
    };

    this.completionClient.updateApiKey(
      process.env.WORKFLOW_EDUCATION_CAREER_PATHS_API_KEY!,
    );

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const completionMessage: { data: { answer: string } } =
      await this.completionClient.createCompletionMessage({ ...query }, user);

    console.log('Received completion message:', completionMessage.data.answer);

    // Clean up the response string by removing json code block markers
    const cleanResponse = completionMessage.data.answer
      .replace(/```json\n?/g, '') // Remove opening json code block
      .replace(/```\n?/g, '') // Remove closing code block
      .trim(); // Remove any extra whitespace

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const response: CareerPathResponseDto[] = JSON.parse(cleanResponse);
    if (!Array.isArray(response) || response.length === 0) {
      throw new Error('Invalid response from recommendation API');
    }

    // Example response transformation - actual implementation will depend on Dify API response format
    const careerPaths: CareerPathResponseDto[] = response;

    // Cache the result
    try {
      await this.cacheService.set(cacheKey, careerPaths);
      console.log(`Successfully cached Career paths with key ${cacheKey}`);
    } catch (error) {
      console.error('Redis cache set error:', error);
      // Continue even if caching fails
    }

    return careerPaths;
  }

  async getCareerView(
    programCode: string,
    jobRole: string,
    responseLanguage: string,
    languagesSpoken: string[],
  ): Promise<CareerViewResponseDto> {
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const decodedCareerData: { role: string; salary: string; level: string } =
      JSON.parse(atob(jobRole));

    const cacheKey = `career_view:${programCode}:${decodedCareerData.role.replace(' ', '-')}:${responseLanguage}`;
    const cachedResult =
      await this.cacheService.get<CareerViewResponseDto>(cacheKey);

    if (cachedResult) {
      console.log('Serving cached career view:', cacheKey);
      return cachedResult;
    }

    const programDescription = this.readProgramDescription(programCode);

    // Call Dify API to get career view analysis
    const user = `random-user-id`;

    const query = {
      language: responseLanguage,
      LANGUAGES_SPOKEN: JSON.stringify(languagesSpoken),
      DEGREE_PROGRAM_DESCRIPTION: JSON.stringify(programDescription),
      JOB_ROLE: jobRole,
    };

    this.completionClient.updateApiKey(
      process.env.WORKFLOW_EDUCATION_CAREER_VIEW_API_KEY!,
    );
    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const completionMessage: { data: { answer: string } } =
      await this.completionClient.createCompletionMessage({ ...query }, user);

    console.log(
      'Received career view completion:',
      completionMessage.data.answer,
    );

    // Clean up the response string by removing json code block markers
    const cleanResponse = completionMessage.data.answer
      .replace(/```json\n?/g, '') // Remove opening json code block
      .replace(/```\n?/g, ''); // Remove closing code block

    try {
      const careerView = JSON.parse(cleanResponse) as CareerViewResponseDto;
      await this.cacheService.set(cacheKey, careerView); // Cache for 24 hours
      console.log(`Successfully cached Career view with key ${cacheKey}`);
      return careerView;
    } catch (error) {
      console.error('Error parsing career view response:', error);
      throw new Error('Failed to process career view analysis');
    }
  }

  async getCareerSuggestions(
    dto: CareerPathsRequestDto,
  ): Promise<{role: string}[]> {
    const cacheKey = `career_suggestions:${dto.programCode}:${dto.responseLanguage}`;
    const cachedResult =
      await this.cacheService.get<CareerPathResponseDto[]>(cacheKey);

    if (cachedResult) {
      return cachedResult;
    }

    const programDescription = this.readProgramDescription(dto.programCode);

    // Call Dify API to get career recommendations
    const user = `random-user-id`;

    const query = {
      language: dto.responseLanguage,
      languages_spoken: JSON.stringify(dto.languagesSpoken),
      degree_program_description: JSON.stringify(programDescription),
    };

    this.completionClient.updateApiKey(
      process.env.WORKFLOW_EDUCATOIN_OCCUPATION_SUGGEST_API_KEY!,
    );

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const completionMessage: { data: { answer: string } } =
      await this.completionClient.createCompletionMessage({ ...query }, user);

    console.log('Received completion message:', completionMessage.data.answer);

    // Clean up the response string by removing json code block markers
    const cleanResponse = completionMessage.data.answer
      .replace(/```json\n?/g, '') // Remove opening json code block
      .replace(/```\n?/g, '') // Remove closing code block
      .trim(); // Remove any extra whitespace

    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
    const response: {role:string}[] = JSON.parse(cleanResponse);
    if (!Array.isArray(response) || response.length === 0) {
      throw new Error('Invalid response from recommendation API');
    }

    // Example response transformation - actual implementation will depend on Dify API response format
    const careerSuggestions:  {role:string}[] = response;

    // Cache the result
    try {
      await this.cacheService.set(cacheKey, careerSuggestions);
      console.log(`Successfully cached careerSuggestions with key ${cacheKey}`);
    } catch (error) {
      console.error('Redis cache set error:', error);
      // Continue even if caching fails
    }

    return careerSuggestions;
  }
}
