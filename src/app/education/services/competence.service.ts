import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

import { Competence } from '../../../entities/competence.entity';
import { Program } from '../../../entities/program.entity';
import { CompetenceType } from '../../../entities/competence-type.enum';
import { CreateCompetenceDto } from '../dto/competence.dto';
import { ProgramService } from './program.service';

@Injectable()
export class CompetenceService {
  constructor(
    @InjectRepository(Competence)
    private competenceRepository: Repository<Competence>,
    @InjectRepository(Program)
    private programRepository: Repository<Program>,
    private programService: ProgramService,
  ) {}

  async findAll(): Promise<Competence[]> {
    return this.competenceRepository.find({ relations: ['program'] });
  }

  async findOne(id: number): Promise<Competence> {
    const competence = await this.competenceRepository.findOne({ 
      where: { id },
      relations: ['program']
    });
    
    if (!competence) {
      throw new NotFoundException(`Competence with ID ${id} not found`);
    }
    
    return competence;
  }

  async findByProgram(programId: number): Promise<Competence[]> {
    return this.competenceRepository.find({
      where: { programId },
      relations: ['program']
    });
  }

  async findByProgramAndType(programId: number, type: CompetenceType): Promise<Competence[]> {
    return this.competenceRepository.find({
      where: { programId, type },
      relations: ['program']
    });
  }

  async create(createCompetenceDto: CreateCompetenceDto): Promise<Competence> {
    // Check if program exists
    const program = await this.programRepository.findOne({
      where: { id: createCompetenceDto.programId }
    });
    
    if (!program) {
      throw new NotFoundException(`Program with ID ${createCompetenceDto.programId} not found`);
    }
    
    const competence = this.competenceRepository.create(createCompetenceDto);
    return this.competenceRepository.save(competence);
  }

  async update(id: number, updateCompetenceDto: CreateCompetenceDto): Promise<Competence> {
    const competence = await this.findOne(id);
    
    // Check if program exists if programId is changing
    if (updateCompetenceDto.programId && updateCompetenceDto.programId !== competence.programId) {
      const program = await this.programRepository.findOne({
        where: { id: updateCompetenceDto.programId }
      });
      
      if (!program) {
        throw new NotFoundException(`Program with ID ${updateCompetenceDto.programId} not found`);
      }
    }
    
    this.competenceRepository.merge(competence, updateCompetenceDto);
    return this.competenceRepository.save(competence);
  }

  async remove(id: number): Promise<void> {
    const competence = await this.findOne(id);
    competence.isActive = false;
    await this.competenceRepository.save(competence);
  }

  async loadCompetencesFromJson(): Promise<void> {
    // Make sure all programs are loaded first
    await this.programService.loadProgramsFromJson();
    
    const programsDir = path.join(process.cwd(), 'research_programs');
    const files = fs.readdirSync(programsDir);
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const filePath = path.join(programsDir, file);
        const programData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
        
        // Find the program
        let program: Program;
        try {
          program = await this.programService.findByCode(programData.code);
        } catch (error) {
          console.error(`Program with code ${programData.code} not found, skipping competence loading`);
          continue;
        }

        // Process core competences
        if (programData.modules && programData.modules.length > 0) {
          const coreCompetences = programData.modules.find(m => m.name === 'CORE COMPETENCE');
          
          if (coreCompetences && coreCompetences.modules) {
            for (const module of coreCompetences.modules) {
              // Add core module as a competence
              await this.createIfNotExists({
                name: module.name,
                description: module.objective || null,
                type: CompetenceType.CORE,
                programId: program.id
              });
            }
          }
          
          // Process complementary competences
          const complementaryCompetences = programData.modules.find(m => m.name === 'COMPLEMENTARY COMPETENCE');
          
          if (complementaryCompetences && complementaryCompetences.modules) {
            // Find the field-related modules
            const fieldRelatedModules = complementaryCompetences.modules.find(
              m => m.name === 'COMPLEMENTARY COMPETENCE STUDIES RELATED TO THE FIELD OF STUDY'
            );
            
            if (fieldRelatedModules && fieldRelatedModules.modules) {
              for (const module of fieldRelatedModules.modules) {
                // Add complementary module as a competence
                await this.createIfNotExists({
                  name: module.name,
                  description: module.objective || null,
                  type: CompetenceType.COMPLEMENTARY,
                  programId: program.id
                });
              }
            }
          }
        }
      }
    }
  }

  private async createIfNotExists(competenceData: CreateCompetenceDto): Promise<Competence> {
    // Check if competence already exists
    const existingCompetence = await this.competenceRepository.findOne({
      where: {
        name: competenceData.name,
        programId: competenceData.programId,
        type: competenceData.type
      }
    });
    
    if (!existingCompetence) {
      return this.create(competenceData);
    }
    
    return existingCompetence;
  }
}
