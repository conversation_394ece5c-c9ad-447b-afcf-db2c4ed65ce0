import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';

import { Program } from '../../../entities/program.entity';
import { CreateProgramDto } from '../dto/program.dto';

@Injectable()
export class ProgramService {
  constructor(
    @InjectRepository(Program)
    private programRepository: Repository<Program>,
  ) {}

  async findAll(): Promise<Program[]> {
    return this.programRepository.find();
  }

  async findOne(id: number): Promise<Program> {
    const program = await this.programRepository.findOne({ where: { id } });
    if (!program) {
      throw new NotFoundException(`Program with ID ${id} not found`);
    }
    return program;
  }

  async findByCode(code: string): Promise<Program> {
    const program = await this.programRepository.findOne({ where: { code } });
    if (!program) {
      throw new NotFoundException(`Program with code ${code} not found`);
    }
    return program;
  }

  async create(createProgramDto: CreateProgramDto): Promise<Program> {
    const program = this.programRepository.create(createProgramDto);
    return this.programRepository.save(program);
  }

  async update(id: number, updateProgramDto: CreateProgramDto): Promise<Program> {
    const program = await this.findOne(id);
    this.programRepository.merge(program, updateProgramDto);
    return this.programRepository.save(program);
  }

  async remove(id: number): Promise<void> {
    const program = await this.findOne(id);
    program.isActive = false;
    await this.programRepository.save(program);
  }

  async loadProgramsFromJson(): Promise<void> {
    const programsDir = path.join(process.cwd(), 'research_programs');
    const files = fs.readdirSync(programsDir);
    
    for (const file of files) {
      if (file.endsWith('.json')) {
        const filePath = path.join(programsDir, file);
        const programData = JSON.parse(fs.readFileSync(filePath, 'utf-8'));
        
        // Check if program already exists
        const existingProgram = await this.programRepository.findOne({
          where: { code: programData.code }
        });
        
        if (!existingProgram) {
          await this.create({
            code: programData.code,
            degree_name: programData.degree_name,
            degree_title: programData.degree_title,
          });
        }
      }
    }
  }
}
