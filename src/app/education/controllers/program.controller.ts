import { Controller, Get, Post, Body, Param, Put, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { ProgramService } from '../services/program.service';
import { Program } from '../../../entities/program.entity';
import { CreateProgramDto, ProgramDto } from '../dto/program.dto';

@Controller('v1/education/programs')
export class ProgramController {
  constructor(private readonly programService: ProgramService) {}

  @Get()
  async findAll(): Promise<ProgramDto[]> {
    const programs = await this.programService.findAll();
    return programs.map(program => ({
      id: program.id,
      code: program.code,
      degree_name: program.degree_name,
      degree_title: program.degree_title
    }));
  }

  @Get(':id')
  async findOne(@Param('id') id: string): Promise<ProgramDto> {
    const program = await this.programService.findOne(+id);
    return {
      id: program.id,
      code: program.code,
      degree_name: program.degree_name,
      degree_title: program.degree_title
    };
  }

  @Post()
  async create(@Body() createProgramDto: CreateProgramDto): Promise<ProgramDto> {
    const program = await this.programService.create(createProgramDto);
    return {
      id: program.id,
      code: program.code,
      degree_name: program.degree_name,
      degree_title: program.degree_title
    };
  }

  @Put(':id')
  async update(
    @Param('id') id: string,
    @Body() updateProgramDto: CreateProgramDto,
  ): Promise<ProgramDto> {
    const program = await this.programService.update(+id, updateProgramDto);
    return {
      id: program.id,
      code: program.code,
      degree_name: program.degree_name,
      degree_title: program.degree_title
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id') id: string): Promise<void> {
    await this.programService.remove(+id);
  }

  @Post('load-from-json')
  async loadFromJson(): Promise<{ message: string }> {
    await this.programService.loadProgramsFromJson();
    return { message: 'Programs loaded successfully' };
  }
}
