import { Controller, Get, Post, Body, Param, HttpCode, HttpStatus } from '@nestjs/common';
import { UserCompetenceService } from '../services/user-competence.service';
import { UserCompetence } from '../../../entities/user-competence.entity';
import { SelectUserCompetencesDto, UserCompetenceDto } from '../dto/user-competence.dto';

@Controller('v1/education/user-competences')
export class UserCompetenceController {
  constructor(private readonly userCompetenceService: UserCompetenceService) {}

  @Get('user/:supertokensId')
  async findByUserId(@Param('supertokensId') supertokensId: string): Promise<UserCompetenceDto[]> {
    const userCompetences = await this.userCompetenceService.findByUserId(supertokensId);
    return userCompetences.map(userCompetence => this.mapToDto(userCompetence));
  }

  @Get('user/:supertokensId/program/:programId')
  async findByUserAndProgram(
    @Param('supertokensId') supertokensId: string,
    @Param('programId') programId: string,
  ): Promise<UserCompetenceDto[]> {
    const userCompetences = await this.userCompetenceService.findByUserAndProgram(supertokensId, +programId);
    return userCompetences.map(userCompetence => this.mapToDto(userCompetence));
  }

  @Post('select')
  async selectCompetences(@Body() selectDto: SelectUserCompetencesDto): Promise<UserCompetenceDto[]> {
    const userCompetences = await this.userCompetenceService.selectUserCompetences(selectDto);
    return userCompetences.map(userCompetence => this.mapToDto(userCompetence));
  }

  @Post('user/:supertokensId/program/:programId/select-core')
  async selectUserProgram(
    @Param('supertokensId') supertokensId: string,
    @Param('programId') programId: string,
  ): Promise<UserCompetenceDto[]> {
    const userCompetences = await this.userCompetenceService.selectUserProgram(supertokensId, +programId);
    return userCompetences.map(userCompetence => this.mapToDto(userCompetence));
  }

  private mapToDto(userCompetence: UserCompetence): UserCompetenceDto {
    return {
      id: userCompetence.id,
      supertokens_id: userCompetence.supertokens_id,
      competenceId: userCompetence.competenceId,
      competence: userCompetence.competence ? {
        id: userCompetence.competence.id,
        name: userCompetence.competence.name,
        description: userCompetence.competence.description,
        type: userCompetence.competence.type,
      } : undefined
    };
  }
}
