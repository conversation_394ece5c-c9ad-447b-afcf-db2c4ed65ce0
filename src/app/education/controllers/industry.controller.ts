import { Controller, Post, Body, HttpStatus, Get, Query, NotFoundException, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { IndustryService } from '../services/industry.service';
import {
  IndustryRecommendationRequestDto,
  IndustryRecommendationResponseDto,
} from '../dto/industry-recommendation.dto';
import { IndustryViewDto } from '../dto/industry-view.dto';
import { AuthGuard } from '../../../auth/guards/auth.guard';
import { SessionUser } from '../../../auth/decorators/session.decorator';
import { SessionContainer } from 'supertokens-node/recipe/session';

@ApiTags('Education Industries')
@Controller('education/industries')
@UseGuards(AuthGuard)
export class IndustryController {
  constructor(private readonly industryService: IndustryService) {}

  @Post('recommend')
  @ApiOperation({
    summary: 'Get industry recommendations based on program code',
    description: `
      Returns a list of recommended industries based on the user's educational program.
      The recommendations include:
      - Industry name (translated based on responseLanguage)
      - Industry code
      - Projected growth rate
      - Program alignment score
      - Median salary range
      
      The response is sorted by alignment score in descending order.
    `,
  })
  @ApiBody({
    type: IndustryRecommendationRequestDto,
    description: 'Program code and language preferences',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully retrieved industry recommendations',
    type: [IndustryRecommendationResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  async getRecommendedIndustries(
    @SessionUser() session: SessionContainer,
    @Body() dto: IndustryRecommendationRequestDto,
  ): Promise<IndustryRecommendationResponseDto[]> {
    return this.industryService.getRecommendedIndustries(session.getUserId(), dto);
  }

  @Get('overview')
  @ApiOperation({
    summary: 'Get detailed overview of a specific industry',
    description: `
      Returns a detailed overview of a specific industry including:
      - Industry name and description
      - Required skills and certifications
      - Popular jobs in the industry with:
        - Salary information
        - Experience requirements
        - Top employers
        - Historical salary trends
      
      The response is localized based on the responseLanguage parameter.
    `,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully retrieved industry overview',
    type: IndustryViewDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Industry not found',
  })
  async getIndustryOverview(
    @SessionUser() session: SessionContainer,
    @Query('industryName') industryName: string,
    @Query('responseLanguage') responseLanguage: string,
    @Query('programCode') programCode: string,
  ): Promise<IndustryViewDto> {
    return this.industryService.getIndustryOverview(
      session.getUserId(),
      industryName,
      responseLanguage,
      programCode,
    );
  }
}
