import { Controller, Post, Body, HttpStatus, Get, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { CareerService } from '../services/career.service';
import {
  CareerPathsRequestDto,
  CareerPathResponseDto,
  CareerViewRequestDto,
  CareerViewResponseDto,
} from '../dto/career-paths.dto';
import { AuthGuard } from '../../../auth/guards/auth.guard';

@ApiTags('Education Careers')
@UseGuards(AuthGuard)
@Controller('education/careers')
export class CareerController {
  constructor(private readonly careerService: CareerService) {}

  @Post('paths')
  @ApiOperation({
    summary: 'Get career path recommendations based on program code',
    description: `
      Returns a list of recommended career paths based on the user's educational program.
      The recommendations include:
      - Career title (translated based on responseLanguage)
      - Description
      - Required skills
      - Growth potential score
      - Program alignment score
      - Salary range
      
      The response is sorted by alignment score in descending order.
    `,
  })
  @ApiBody({
    type: CareerPathsRequestDto,
    description: 'Program code and language preferences',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully retrieved career path recommendations',
    type: [CareerPathResponseDto],
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  async getCareerPaths(
    @Body() dto: CareerPathsRequestDto,
  ): Promise<CareerPathResponseDto[]> {
    return this.careerService.getCareerPaths(dto);
  }

  @Post('view')
  @ApiOperation({
    summary: 'Get detailed career view analysis',
    description: `
      Returns a detailed analysis of a specific career path based on the user's educational program.
      The analysis includes:
      - Skills assessment
      - Transferable skills analysis
      - Career progression path
      - Future challenges and opportunities
    `,
  })
  @ApiBody({
    type: CareerViewRequestDto,
    description: 'Program code, job role, and language preferences',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Successfully retrieved career view analysis',
    type: CareerViewResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid request parameters',
  })
  async getCareerView(
    @Body() dto: CareerViewRequestDto,
  ): Promise<CareerViewResponseDto> {
    return this.careerService.getCareerView(
      dto.programCode,
      dto.jobRole,
      dto.responseLanguage,
      dto.languagesSpoken,
    );
  }

  @Post('suggestions')
  async getCareerSuggestions(
    @Body() dto: CareerPathsRequestDto,
  ): Promise<{ role: string }[]> {
    return this.careerService.getCareerSuggestions(dto);
  }
}
