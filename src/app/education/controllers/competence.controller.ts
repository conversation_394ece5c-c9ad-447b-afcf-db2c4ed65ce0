import { Controller, Get, Post, Body, Param, Put, Delete, HttpCode, HttpStatus, Query } from '@nestjs/common';
import { CompetenceService } from '../services/competence.service';
import { Competence } from '../../../entities/competence.entity';
import { CreateCompetenceDto, CompetenceDto, CompetenceWithProgramDto } from '../dto/competence.dto';
import { CompetenceType } from '../../../entities/competence-type.enum';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { ProgramService } from '../services/program.service';


// WIP - need to correctly process the program files
@ApiTags('Education Competences')
@Controller('v1/education/competences')
export class CompetenceController {
  constructor(
    private readonly competenceService: CompetenceService,
    private readonly programService: ProgramService
  ) {}

  @Get()
  @ApiOperation({
    summary: 'Get all competences',
    description: 'Retrieves a list of all competences with their associated program information'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of competences retrieved successfully',
    type: [CompetenceWithProgramDto]
  })
  async findAll(): Promise<CompetenceWithProgramDto[]> {
    const competences = await this.competenceService.findAll();
    return competences.map(competence => this.mapToDto(competence));
  }

  @Get(':id')
  @ApiOperation({
    summary: 'Get competence by ID',
    description: 'Retrieves a specific competence by its unique identifier'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the competence to retrieve',
    type: 'number',
    required: true
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Competence retrieved successfully',
    type: CompetenceWithProgramDto
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Competence not found'
  })
  async findOne(@Param('id') id: string): Promise<CompetenceWithProgramDto> {
    const competence = await this.competenceService.findOne(+id);
    return this.mapToDto(competence);
  }

  @Get('program/:programCode')
  @ApiOperation({
    summary: 'Get competences by program code',
    description: 'Retrieves all competences associated with a specific program code, optionally filtered by competence type'
  })
  @ApiParam({
    name: 'programCode',
    description: 'The code of the program to find competences for',
    type: 'string',
    required: true
  })
  @ApiQuery({
    name: 'type',
    description: 'Optional filter by competence type',
    enum: CompetenceType,
    required: false
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'List of competences for the program retrieved successfully',
    type: [CompetenceWithProgramDto]
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Program not found'
  })
  async findByProgram(
    @Param('programCode') programCode: string,
    @Query('type') type?: CompetenceType,
  ): Promise<CompetenceWithProgramDto[]> {
    let competences: Competence[];
    
    // First find the program by code
    const program = await this.programService.findByCode(programCode);
    
    if (type) {
      competences = await this.competenceService.findByProgramAndType(program.id, type);
    } else {
      competences = await this.competenceService.findByProgram(program.id);
    }
    
    return competences.map(competence => this.mapToDto(competence));
  }

  @Post()
  @ApiOperation({
    summary: 'Create a new competence',
    description: 'Creates a new competence with the provided details'
  })
  @ApiBody({
    type: CreateCompetenceDto,
    description: 'Competence data to create'
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Competence created successfully',
    type: CompetenceDto
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid competence data provided'
  })
  async create(@Body() createCompetenceDto: CreateCompetenceDto): Promise<CompetenceDto> {
    const competence = await this.competenceService.create(createCompetenceDto);
    return {
      id: competence.id,
      name: competence.name,
      description: competence.description,
      type: competence.type,
      programId: competence.programId
    };
  }

  @Put(':id')
  @ApiOperation({
    summary: 'Update a competence',
    description: 'Updates an existing competence with the provided details'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the competence to update',
    type: 'number',
    required: true
  })
  @ApiBody({
    type: CreateCompetenceDto,
    description: 'Updated competence data'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Competence updated successfully',
    type: CompetenceDto
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Competence not found'
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid competence data provided'
  })
  async update(
    @Param('id') id: string,
    @Body() updateCompetenceDto: CreateCompetenceDto,
  ): Promise<CompetenceDto> {
    const competence = await this.competenceService.update(+id, updateCompetenceDto);
    return {
      id: competence.id,
      name: competence.name,
      description: competence.description,
      type: competence.type,
      programId: competence.programId
    };
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Delete a competence',
    description: 'Removes a competence by its ID'
  })
  @ApiParam({
    name: 'id',
    description: 'The ID of the competence to delete',
    type: 'number',
    required: true
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Competence deleted successfully'
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Competence not found'
  })
  async remove(@Param('id') id: string): Promise<void> {
    await this.competenceService.remove(+id);
  }

  @Post('load-from-json')
  @ApiOperation({
    summary: 'Load competences from JSON',
    description: 'Loads competence data from a predefined JSON file into the database'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Competences loaded successfully',
    schema: {
      type: 'object',
      properties: {
        message: {
          type: 'string',
          example: 'Competences loaded successfully'
        }
      }
    }
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Failed to load competences from JSON'
  })
  async loadFromJson(): Promise<{ message: string }> {
    await this.competenceService.loadCompetencesFromJson();
    return { message: 'Competences loaded successfully' };
  }

  private mapToDto(competence: Competence): CompetenceWithProgramDto {
    return {
      id: competence.id,
      name: competence.name,
      description: competence.description,
      type: competence.type,
      programId: competence.programId,
      program: competence.program ? {
        id: competence.program.id,
        code: competence.program.code,
        degree_name: competence.program.degree_name,
        degree_title: competence.program.degree_title
      }: { id: 999, code: "NAVB", degree_name: "null", degree_title: "null" }
    };
  }
}
