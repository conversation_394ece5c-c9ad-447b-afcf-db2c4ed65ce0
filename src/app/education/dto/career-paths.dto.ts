import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, ArrayMaxSize, IsBoolean, IsEnum } from 'class-validator';

export class CareerPathsRequestDto {
  @ApiProperty({
    description: 'Educational program code',
    example: 'CS-101',
  })
  @IsString()
  programCode: string;

  @ApiProperty({
    description: 'Languages spoken by the user',
    example: ['en', 'fr'],
    isArray: true,
  })
  @IsArray()
  @IsString({ each: true })
  languagesSpoken: string[];

  @ApiProperty({
    description: 'Preferred language for the response',
    example: 'en',
  })
  @IsString()
  responseLanguage: string;
}

export enum PopularityLevel {
  HIGH_GROWTH = 'High Growth',
  MODERATE = 'Moderate',
  STEADY = 'Steady',
  DECLINING = 'Declining',
  NICHE = 'Niche',
}

export enum AlignmentLevel {
  HIGHLY = 'Highly aligned',
  MODERATELY = 'Moderately aligned',
  PARTIALLY = 'Partially aligned',
  NOT_ALIGNED = 'Not aligned',
}

export enum SkillType {
  TECHNICAL = 'Technical',
  SOFT = 'Soft',
  DOMAIN = 'Domain',
  PRACTICAL = 'Practical',
}

export class CareerSkill {
  @ApiProperty({
    description: 'Name of the skill',
    example: 'Problem Solving',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Whether the skill is acquired through the degree program',
    example: true,
  })
  @IsBoolean()
  isAcquired: boolean;

  @ApiProperty({
    description: 'Type of skill',
    enum: SkillType,
    example: SkillType.TECHNICAL,
  })
  @IsEnum(SkillType)
  type: SkillType;
}

export class CareerPathResponseDto {
  @ApiProperty({
    description: 'Job Role name',
    example: 'Software Developer',
  })
  @IsString()
  role: string;

  @ApiProperty({
    description: 'Popularity and growth level of the role',
    enum: PopularityLevel,
    example: PopularityLevel.HIGH_GROWTH,
  })
  @IsEnum(PopularityLevel)
  popularityLevel: PopularityLevel;

  @ApiProperty({
    description: 'Salary range for role in Finland',
    example: '€45,000 - €60,000',
  })
  @IsString()
  salaryRange: string;

  @ApiProperty({
    description: 'Alignment of the role with the student\'s profile',
    enum: AlignmentLevel,
    example: AlignmentLevel.HIGHLY,
  })
  @IsEnum(AlignmentLevel)
  alignment: AlignmentLevel;

  @ApiProperty({
    description: 'Skills that will help succeed in this role',
    type: [CareerSkill],
    maxItems: 4,
  })
  @IsArray()
  @ArrayMaxSize(4)
  skills: CareerSkill[];
}

export class CareerViewRequestDto {
  @ApiProperty({
    description: 'Educational program code',
    example: 'CS-101',
  })
  @IsString()
  programCode: string;

  @ApiProperty({
    description: 'Job role to analyze',
    example: 'Software Developer',
  })
  @IsString()
  jobRole: string;

  @ApiProperty({
    description: 'Languages spoken by the user',
    example: ['en', 'fr'],
    isArray: true,
  })
  @IsArray()
  @IsString({ each: true })
  languagesSpoken: string[];

  @ApiProperty({
    description: 'Preferred language for the response',
    example: 'en',
  })
  @IsString()
  responseLanguage: string;
}

export class CareerSkillAssessment {
  @ApiProperty({
    description: 'Name of the skill',
    example: 'Python Programming',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Whether the skill is acquired',
    example: true,
  })
  @IsBoolean()
  isAquired: boolean;

  @ApiProperty({
    description: 'Comment about the skill acquisition',
    example: 'Strong foundation from coursework',
  })
  @IsString()
  comment: string;
}

export class TransferableSkill {
  @ApiProperty({
    description: 'Name of the transferable skill',
    example: 'Problem Solving',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Current context of the skill',
    example: 'Academic projects',
  })
  @IsString()
  existing: string;

  @ApiProperty({
    description: 'How the skill applies to the target role',
    example: 'Production system debugging',
  })
  @IsString()
  destination: string;
}

export class CareerPathStep {
  @ApiProperty({
    description: 'Role title',
    example: 'Junior Developer',
  })
  @IsString()
  role: string;

  @ApiProperty({
    description: 'Years of experience required',
    example: '0-2 years',
  })
  @IsString()
  yearsExperience: string;

  @ApiProperty({
    description: 'Key skills for this step',
    example: ['JavaScript', 'Git', 'Agile'],
  })
  @IsArray()
  @IsString({ each: true })
  skills: string[];

  @ApiProperty({
    description: 'Expected salary range',
    example: '€35,000 - €45,000',
  })
  @IsString()
  salary: string;

  @ApiProperty({
    description: 'Icon identifier for the role',
    example: 'developer',
  })
  @IsString()
  icon: string;
}

export class CareerProgressionPath {
  @ApiProperty({
    description: 'Career progression steps',
    type: [CareerPathStep],
  })
  @IsArray()
  steps: CareerPathStep[];

  @ApiProperty({
    description: 'Potential future challenges',
    example: 'Rapid technology changes requiring continuous learning',
  })
  @IsString()
  futureChallenges: string;

  @ApiProperty({
    description: 'Optimistic career scenario',
    example: 'Leading a development team within 5 years',
  })
  @IsString()
  optimisticScenario: string;

  @ApiProperty({
    description: 'Expected career progression',
    example: 'Steady growth with focus on technical expertise',
  })
  @IsString()
  expectedPath: string;
}

export class CareerViewResponseDto {
  @ApiProperty({
    description: 'Job role being analyzed',
    example: 'Software Developer',
  })
  @IsString()
  role: string;

  @ApiProperty({
    description: 'Skills assessment',
    type: [CareerSkillAssessment],
  })
  @IsArray()
  skills: CareerSkillAssessment[];

  @ApiProperty({
    description: 'Transferable skills analysis',
    type: [TransferableSkill],
  })
  @IsArray()
  transferableSkills: TransferableSkill[];

  @ApiProperty({
    description: 'Career progression path',
    type: CareerProgressionPath,
  })
  careerPath: CareerProgressionPath;
}
