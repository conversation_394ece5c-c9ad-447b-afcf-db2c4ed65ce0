import { IsString, IsArray } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class IndustryRecommendationRequestDto {
  @ApiProperty({
    description: 'The program code of the user',
    example: 'CS2021',
  })
  @IsString()
  programCode: string;

  @ApiProperty({
    description: 'The preferred response language',
    example: 'en',
  })
  @IsString()
  responseLanguage: string;

  @ApiProperty({
    description: 'Languages spoken by the user',
    example: ['en', 'fi'],
  })
  @IsArray()
  @IsString({ each: true })
  languagesSpoken: string[];
}

export class IndustryRecommendationResponseDto {
  @ApiProperty({
    description: 'Name of the industry',
    example: 'Software Development',
  })
  name: string;

  @ApiProperty({
    description: 'Industry code',
    example: 'IT001',
  })
  code: string;

  @ApiProperty({
    description: 'Projected industry growth percentage',
    example: 12.5,
  })
  projectedIndustryGrowth: number;

  @ApiProperty({
    description: 'Alignment score between program and industry',
    example: 85.5,
  })
  alignmentScore: number;

  @ApiProperty({
    description: 'Median salary in the industry',
    example: '€45,000 - €65,000',
  })
  medianSalary: string;
}
