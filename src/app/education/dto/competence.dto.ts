import { Is<PERSON>tring, Is<PERSON>num, <PERSON><PERSON><PERSON>ber, <PERSON><PERSON><PERSON>al, IsNotEmpty } from 'class-validator';
import { CompetenceType } from '../../../entities/competence-type.enum';

export class CreateCompetenceDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(CompetenceType)
  type: CompetenceType;

  @IsNumber()
  programId: number;
}

export class CompetenceDto {
  id: number;
  name: string;
  description?: string;
  type: CompetenceType;
  programId: number;
}

export class CompetenceWithProgramDto extends CompetenceDto {
  program: {
    id: number;
    code: string;
    degree_name: string;
    degree_title: string;
  };
}
