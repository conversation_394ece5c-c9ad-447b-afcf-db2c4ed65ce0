import { IsS<PERSON>, <PERSON><PERSON><PERSON>y, <PERSON><PERSON><PERSON>ber, IsNotEmpty } from 'class-validator';

export class SelectUserCompetencesDto {
  @IsString()
  @IsNotEmpty()
  supertokens_id: string;

  @IsArray()
  @IsNumber({}, { each: true })
  competenceIds: number[];
}

export class UserCompetenceDto {
  id: number;
  supertokens_id: string;
  competenceId: number;
  competence?: {
    id: number;
    name: string;
    description?: string;
    type: string;
  };
}
