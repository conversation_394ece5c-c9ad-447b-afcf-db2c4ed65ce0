import { ApiProperty } from '@nestjs/swagger';

class TopEmployerDto {
  @ApiProperty({ description: 'Name of the employer' })
  name: string;

  @ApiProperty({ description: 'Location of the employer' })
  location: string;

  @ApiProperty({ description: 'Website URL of the employer' })
  website: string;

  @ApiProperty({ description: 'Salary range offered by the employer' })
  payRange: string;
}

class SalaryByYearDto {
  @ApiProperty({ description: 'Year of the salary data' })
  year: string;

  @ApiProperty({ description: 'Average salary for the year', type: Number })
  salary: number;
}

class SalaryByExperienceDto {
  @ApiProperty({ description: 'Experience level' })
  experience: string;

  @ApiProperty({
    description: 'Average salary for the experience level',
    type: Number,
  })
  salary: number;
}

class PopularJobDto {
  @ApiProperty({ description: 'Job title' })
  name: string;

  @ApiProperty({ description: 'Average salary range' })
  salary: string;

  @ApiProperty({ description: 'Required experience level' })
  experience: string;

  @ApiProperty({ description: 'Job popularity level' })
  popularityLevel: string;

  @ApiProperty({ description: 'List of top employers', type: [TopEmployerDto] })
  topEmployers: TopEmployerDto[];

  @ApiProperty({
    description: 'Historical salary data by year',
    type: [SalaryByYearDto],
  })
  salaryByYear: SalaryByYearDto[];

  @ApiProperty({
    description: 'Salary data by experience level',
    type: [SalaryByExperienceDto],
  })
  salaryByExperience: SalaryByExperienceDto[];
}

class SkillDto {
  @ApiProperty({ description: 'Name of the skill' })
  name: string;

  @ApiProperty({ description: 'Whether the skill is acquired' })
  isAquired: boolean;

  @ApiProperty({ description: 'Type of skill' })
  type: string;
}

class CertificationDto {
  @ApiProperty({ description: 'Name of the certification' })
  name: string;

  @ApiProperty({ description: 'Level of certification' })
  level: string;
}

export class IndustryViewDto {
  @ApiProperty({ description: 'Name of the industry' })
  name: string;

  @ApiProperty({ description: 'Description of the industry' })
  description: string;

  @ApiProperty({ description: 'List of relevant skills', type: [SkillDto] })
  skills: SkillDto[];

  @ApiProperty({
    description: 'List of relevant certifications',
    type: [CertificationDto],
  })
  certifications: CertificationDto[];

  @ApiProperty({
    description: 'List of popular jobs in the industry',
    type: [PopularJobDto],
  })
  popularJobs: PopularJobDto[];
}
