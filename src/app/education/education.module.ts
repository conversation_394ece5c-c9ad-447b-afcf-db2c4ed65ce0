import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Industry } from '../../entities/industry.entity';
import { Program } from '../../entities/program.entity';
import { Competence } from '../../entities/competence.entity';
import { UserCompetence } from '../../entities/user-competence.entity';
import { IndustryController } from './controllers/industry.controller';
import { IndustryService } from './services/industry.service';
import { CareerController } from './controllers/career.controller';
import { CareerService } from './services/career.service';
import { ProgramController } from './controllers/program.controller';
import { CompetenceController } from './controllers/competence.controller';
import { UserCompetenceController } from './controllers/user-competence.controller';
import { ProgramService } from './services/program.service';
import { CompetenceService } from './services/competence.service';
import { UserCompetenceService } from './services/user-competence.service';
import { CacheModule } from '../../cache/cache.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Industry,
      Program,
      Competence,
      UserCompetence
    ]),
    CacheModule
  ],
  controllers: [
    IndustryController,
    CareerController,
    ProgramController,
    CompetenceController,
    UserCompetenceController
  ],
  providers: [
    IndustryService,
    CareerService,
    ProgramService,
    CompetenceService,
    UserCompetenceService
  ],
})
export class EducationModule {}
