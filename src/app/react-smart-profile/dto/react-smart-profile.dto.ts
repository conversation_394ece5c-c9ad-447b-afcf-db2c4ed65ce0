import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>al,
  IsString,
  Val<PERSON>teNested,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

class CertificationDto {
  @ApiProperty()
  @IsString()
  title: string;

  @ApiProperty()
  @IsString()
  organization: string;

  @ApiProperty()
  @IsString()
  issue_date: string;

  @ApiProperty()
  @IsString()
  _key: string;
}

class EducationDto {
  @ApiProperty()
  @IsString()
  degree: string;

  @ApiProperty()
  @IsString()
  field_of_study: string;

  @ApiProperty()
  @IsString()
  institution: string;

  @ApiProperty()
  @IsString()
  start_date: string;

  @ApiProperty()
  @IsString()
  end_date: string;

  @ApiProperty()
  @IsString()
  _key: string;
}

class ExperienceDto {
  @ApiProperty()
  @IsString()
  position: string;

  @ApiProperty()
  @IsString()
  company: string;

  @ApiProperty()
  @IsString()
  start_date: string;

  @ApiProperty()
  @IsString()
  end_date: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  responsibilities?: string[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  skills?: string[];

  @ApiProperty()
  @IsString()
  _key: string;
}

class LanguageDto {
  @ApiProperty()
  @IsString()
  language: string;

  @ApiProperty()
  @IsString()
  proficiency: string;

  @ApiProperty()
  @IsString()
  _key: string;
}

class SkillDto {
  @ApiProperty()
  @IsString()
  skill: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  _key: string;
}

export class ReactSmartProfileDto {
  @ApiProperty()
  @IsString()
  @IsOptional()
  mongoId?: string;

  @ApiProperty()
  @IsNumber()
  profileId: number;

  @ApiProperty({ type: [CertificationDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CertificationDto)
  certifications?: CertificationDto[];

  @ApiProperty({ type: [EducationDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => EducationDto)
  education?: EducationDto[];

  @ApiProperty({ type: [ExperienceDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExperienceDto)
  experience?: ExperienceDto[];

  @ApiProperty({ type: [LanguageDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => LanguageDto)
  languages?: LanguageDto[];

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ type: [SkillDto], required: false })
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SkillDto)
  skills?: SkillDto[];
}
