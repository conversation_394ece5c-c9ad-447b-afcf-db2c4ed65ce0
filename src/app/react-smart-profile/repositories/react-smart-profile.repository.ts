import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ReactSmartProfile } from '../entities/react-smart-profile.entity';

@Injectable()
export class ReactSmartProfileRepository {
  constructor(
    @InjectRepository(ReactSmartProfile)
    private readonly repository: Repository<ReactSmartProfile>,
  ) {}

  async findOne(id: string): Promise<ReactSmartProfile> {
    const profile = await this.repository.findOne({ where: { id } });
    if (!profile) {
      throw new NotFoundException(`Profile with ID ${id} not found`);
    }
    return profile;
  }

  async findByMongoId(mongoId: string): Promise<ReactSmartProfile> {
    const profile = await this.repository.findOne({ where: { mongoId } });
    if (!profile) {
      throw new NotFoundException(
        `Profile with MongoDB ID ${mongoId} not found`,
      );
    }
    return profile;
  }

  async findByProfileId(profileId: number): Promise<ReactSmartProfile> {
    const profile = await this.repository.findOne({ where: { profileId } });
    if (!profile) {
      throw new NotFoundException(
        `Profile with profile ID ${profileId} not found`,
      );
    }
    return profile;
  }

  async create(
    profile: Partial<ReactSmartProfile>,
  ): Promise<ReactSmartProfile> {
    const newProfile = this.repository.create(profile);
    return this.repository.save(newProfile);
  }

  async update(
    id: string,
    profile: Partial<ReactSmartProfile>,
  ): Promise<ReactSmartProfile> {
    await this.findOne(id);
    await this.repository.update(id, profile);
    return this.findOne(id);
  }

  async upsertByProfileId(
    profileId: number,
    profile: Partial<ReactSmartProfile>,
  ): Promise<ReactSmartProfile> {
    try {
      const existingProfile = await this.findByProfileId(profileId);
      return this.update(existingProfile.id, profile);
    } catch (error) {
      if (error instanceof NotFoundException) {
        return this.create({ ...profile, profileId });
      }
      throw error;
    }
  }

  async remove(id: string): Promise<void> {
    const profile = await this.findOne(id);
    await this.repository.remove(profile);
  }
}
