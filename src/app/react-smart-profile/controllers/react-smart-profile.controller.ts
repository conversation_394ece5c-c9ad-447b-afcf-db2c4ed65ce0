import {
  <PERSON>,
  Get,
  Logger,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ReactSmartProfileService } from '../services/react-smart-profile.service';
import { ReactSmartProfile } from '../entities/react-smart-profile.entity';

@ApiTags('react-smart-profile')
@Controller('react-smart-profile')
export class ReactSmartProfileController {
  private readonly logger = new Logger(ReactSmartProfileController.name);

  constructor(
    private readonly reactSmartProfileService: ReactSmartProfileService,
  ) {}


  @Get('profile/:id')
  @ApiOperation({ summary: 'Find a profile by profile ID' })
  @ApiParam({ name: 'id', description: 'Profile ID' })
  @ApiResponse({
    status: 200,
    description: 'The profile has been successfully retrieved.',
  })
  async findByProfileId(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ReactSmartProfile> {
    this.logger.log(`Finding profile by profile ID: ${id}`);
    return this.reactSmartProfileService.findByProfileId(id);
  }

}
