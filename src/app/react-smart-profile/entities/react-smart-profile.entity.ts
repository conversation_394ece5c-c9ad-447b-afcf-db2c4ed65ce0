import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

// Interface for certification
interface Certification {
  title: string;
  organization: string;
  issue_date: string;
  _key: string;
}

// Interface for education
interface Education {
  degree: string;
  field_of_study: string;
  institution: string;
  start_date: string;
  end_date: string;
  _key: string;
}

// Interface for experience
interface Experience {
  position: string;
  company: string;
  start_date: string;
  end_date: string;
  responsibilities?: string[];
  skills?: string[];
  _key: string;
}

// Interface for language
interface Language {
  language: string;
  proficiency: string;
  _key: string;
}

// Interface for skill
interface Skill {
  skill: string;
  type: string;
  _key: string;
}

@Entity('react_smart_profile')
export class ReactSmartProfile {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ nullable: true })
  mongoId: string;

  @Column({ unique: true })
  @Index()
  profileId: number;

  @Column({ type: 'jsonb', nullable: true })
  certifications: Certification[];

  @Column({ type: 'jsonb', nullable: true })
  education: Education[];

  @Column({ type: 'jsonb', nullable: true })
  experience: Experience[];

  @Column({ type: 'jsonb', nullable: true })
  languages: Language[];

  @Column({ nullable: true })
  name: string;

  @Column({ type: 'jsonb', nullable: true })
  skills: Skill[];
}
