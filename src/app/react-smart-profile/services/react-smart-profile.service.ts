import { Injectable, Logger } from '@nestjs/common';
import { ReactSmartProfileRepository } from '../repositories/react-smart-profile.repository';
import { ReactSmartProfile } from '../entities/react-smart-profile.entity';

@Injectable()
export class ReactSmartProfileService {
  private readonly logger = new Logger(ReactSmartProfileService.name);

  constructor(
    private readonly reactSmartProfileRepository: ReactSmartProfileRepository,
  ) {}

  /**
   * Fetch a profile by MongoDB ID
   */
  async findByMongoId(id: string): Promise<ReactSmartProfile> {
    try {
      return await this.reactSmartProfileRepository.findByMongoId(id);
    } catch (error) {
      this.logger.error(
        `Error fetching profile by MongoDB ID: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Fetch a profile by profileId
   */
  async findByProfileId(profileId: number): Promise<ReactSmartProfile> {
    try {
      return await this.reactSmartProfileRepository.findByProfileId(
        profileId,
      );
    } catch (error) {
      this.logger.error(
        `Error fetching profile by profileId: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

}
