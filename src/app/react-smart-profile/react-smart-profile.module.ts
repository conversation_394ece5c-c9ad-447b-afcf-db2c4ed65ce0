import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ReactSmartProfile } from './entities/react-smart-profile.entity';
import { ReactSmartProfileRepository } from './repositories/react-smart-profile.repository';
import { ReactSmartProfileService } from './services/react-smart-profile.service';
import { ReactSmartProfileController } from './controllers/react-smart-profile.controller';

@Module({
  imports: [
    TypeOrmModule.forFeature([ReactSmartProfile]),
  ],
  providers: [ReactSmartProfileRepository, ReactSmartProfileService],
  controllers: [ReactSmartProfileController],
  exports: [ReactSmartProfileRepository, ReactSmartProfileService],
})
export class ReactSmartProfileModule {}
