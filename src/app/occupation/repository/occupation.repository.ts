import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Occupation } from '../../../entities/occupation.entity';

@Injectable()
export class OccupationRepository {
  constructor(
    @InjectRepository(Occupation)
    private readonly occupationRepository: Repository<Occupation>,
  ) {}

  async findAll(): Promise<Occupation[]> {
    return this.occupationRepository.find();
  }

  async findByCode(code: string): Promise<Occupation | null> {
    return this.occupationRepository.findOne({
      where: { code },
    });
  }

  async findByConceptUri(conceptUri: string): Promise<Occupation | null> {
    return this.occupationRepository.findOne({
      where: { conceptUri },
    });
  }

  async findById(id: number): Promise<Occupation | null> {
    return this.occupationRepository.findOne({
      where: { id },
    });
  }

  async create(occupationData: Partial<Occupation>): Promise<Occupation> {
    const occupation = this.occupationRepository.create(occupationData);
    return this.occupationRepository.save(occupation);
  }

  async update(
    id: number,
    occupationData: Partial<Occupation>,
  ): Promise<Occupation> {
    await this.occupationRepository.update(id, occupationData);
    const updated = await this.findById(id);
    if (!updated) {
      throw new NotFoundException(
        `Occupation with ID ${id} not found after update`,
      );
    }
    return updated;
  }
}
