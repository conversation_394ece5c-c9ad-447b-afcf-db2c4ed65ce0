import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Municipality } from '../../entities/municipality.entity';
import { MunicipalityRepository } from './repository/municipality.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Municipality])],
  providers: [MunicipalityRepository],
  exports: [MunicipalityRepository],
})
export class MunicipalityModule {}
