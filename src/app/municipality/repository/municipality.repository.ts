import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { Municipality } from '../../../entities/municipality.entity';

@Injectable()
export class MunicipalityRepository {
  private readonly logger = new Logger(MunicipalityRepository.name);

  constructor(
    @InjectRepository(Municipality)
    private readonly municipalityRepository: Repository<Municipality>,
  ) {}

  async findAll(): Promise<Municipality[]> {
    return this.municipalityRepository.find();
  }

  async findByCode(code: string): Promise<Municipality | null> {
    return this.municipalityRepository.findOne({
      where: { code },
    });
  }

  async findById(id: number): Promise<Municipality | null> {
    return this.municipalityRepository.findOne({
      where: { id },
    });
  }

  async create(municipalityData: Partial<Municipality>): Promise<Municipality> {
    const municipality = this.municipalityRepository.create(municipalityData);
    return this.municipalityRepository.save(municipality);
  }

  async update(
    id: number,
    municipalityData: Partial<Municipality>,
  ): Promise<Municipality> {
    await this.municipalityRepository.update(id, municipalityData);
    const updated = await this.findById(id);
    if (!updated) {
      throw new NotFoundException(
        `Municipality with ID ${id} not found after update`,
      );
    }
    return updated;
  }

  /**
   * Find a municipality by its name
   * @param name The name of the municipality
   * @returns The municipality if found, null otherwise
   */
  async findByName(name: string): Promise<Municipality | null> {
    if (!name) {
      return null;
    }

    try {
      // Try exact match first
      let municipality = await this.municipalityRepository.findOne({
        where: { classificationName: name },
      });

      // If not found, try case-insensitive match
      if (!municipality) {
        municipality = await this.municipalityRepository.findOne({
          where: { classificationName: ILike(name) },
        });
      }

      return municipality;
    } catch (error) {
      this.logger.error(
        `Error finding municipality by name "${name}": ${error.message}`,
      );
      return null;
    }
  }
}
