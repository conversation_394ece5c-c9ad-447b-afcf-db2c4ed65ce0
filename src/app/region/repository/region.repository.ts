import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { ILike, Repository } from 'typeorm';
import { Region } from '../../../entities/region.entity';

@Injectable()
export class RegionRepository {
  private readonly logger = new Logger(RegionRepository.name);

  constructor(
    @InjectRepository(Region)
    private readonly regionRepository: Repository<Region>,
  ) {}

  async findAll(): Promise<Region[]> {
    return this.regionRepository.find();
  }

  async findByCode(code: string): Promise<Region | null> {
    return this.regionRepository.findOne({
      where: { code },
    });
  }

  async findById(id: number): Promise<Region | null> {
    return this.regionRepository.findOne({
      where: { id },
    });
  }

  async create(regionData: Partial<Region>): Promise<Region> {
    const region = this.regionRepository.create(regionData);
    return this.regionRepository.save(region);
  }

  async update(id: number, regionData: Partial<Region>): Promise<Region> {
    await this.regionRepository.update(id, regionData);
    const updated = await this.findById(id);
    if (!updated) {
      throw new NotFoundException(
        `Region with ID ${id} not found after update`,
      );
    }
    return updated;
  }

  /**
   * Find a region by its name
   * @param name The name of the region
   * @returns The region if found, null otherwise
   */
  async findByName(name: string): Promise<Region | null> {
    if (!name) {
      return null;
    }

    try {
      // Try exact match first
      let region = await this.regionRepository.findOne({
        where: { classificationName: name },
      });

      // If not found, try case-insensitive match
      if (!region) {
        region = await this.regionRepository.findOne({
          where: { classificationName: ILike(name) },
        });
      }

      return region;
    } catch (error) {
      this.logger.error(
        `Error finding region by name "${name}": ${error.message}`,
      );
      return null;
    }
  }
}
