import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { LocationController } from './controllers/location.controller';
import { Municipality } from '../../entities/municipality.entity';
import { Region } from '../../entities/region.entity';
import { MunicipalityRepository } from '../municipality/repository/municipality.repository';
import { RegionRepository } from '../region/repository/region.repository';

@Module({
  imports: [TypeOrmModule.forFeature([Municipality, Region])],
  controllers: [LocationController],
  providers: [MunicipalityRepository, RegionRepository],
  exports: [MunicipalityRepository, RegionRepository],
})
export class LocationModule {}
