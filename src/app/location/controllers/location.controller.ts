import { Controller, Get, Logger, NotFoundException, Param } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { MunicipalityRepository } from '../../municipality/repository/municipality.repository';
import { RegionRepository } from '../../region/repository/region.repository';
import { Municipality } from '../../../entities/municipality.entity';
import { Region } from '../../../entities/region.entity';

/**
 * Location API Controller
 *
 * Handles location-related operations including fetching municipalities and regions
 */
@ApiTags('Locations')
@Controller('/locations')
export class LocationController {
  private readonly logger = new Logger(LocationController.name);

  constructor(
    private readonly municipalityRepository: MunicipalityRepository,
    private readonly regionRepository: RegionRepository,
  ) {}

  /**
   * Simple health check endpoint for the Location API
   */
  @Get()
  @ApiOperation({
    summary: 'Health check endpoint',
    description: 'Returns "ok" if the Location API is running properly',
  })
  @ApiResponse({
    status: 200,
    description: 'API is healthy',
    schema: {
      type: 'string',
      example: 'ok',
    },
  })
  async status() {
    return 'ok';
  }

  /**
   * Get all municipalities
   */
  @Get('/municipalities')
  @ApiOperation({
    summary: 'Get all municipalities',
    description: 'Returns a list of all municipalities',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved municipalities',
    type: [Municipality],
  })
  async getAllMunicipalities(): Promise<Municipality[]> {
    try {
      return await this.municipalityRepository.findAll();
    } catch (error) {
      this.logger.error(`Error fetching municipalities: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get municipality by code
   */
  @Get('/municipalities/:code')
  @ApiOperation({
    summary: 'Get municipality by code',
    description: 'Returns a municipality by its code',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved municipality',
    type: Municipality,
  })
  @ApiResponse({
    status: 404,
    description: 'Municipality not found',
  })
  async getMunicipalityByCode(@Param('code') code: string): Promise<Municipality> {
    const municipality = await this.municipalityRepository.findByCode(code);
    
    if (!municipality) {
      throw new NotFoundException(`Municipality with code ${code} not found`);
    }
    
    return municipality;
  }

  /**
   * Get all regions
   */
  @Get('/regions')
  @ApiOperation({
    summary: 'Get all regions',
    description: 'Returns a list of all regions',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved regions',
    type: [Region],
  })
  async getAllRegions(): Promise<Region[]> {
    try {
      return await this.regionRepository.findAll();
    } catch (error) {
      this.logger.error(`Error fetching regions: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get region by code
   */
  @Get('/regions/:code')
  @ApiOperation({
    summary: 'Get region by code',
    description: 'Returns a region by its code',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved region',
    type: Region,
  })
  @ApiResponse({
    status: 404,
    description: 'Region not found',
  })
  async getRegionByCode(@Param('code') code: string): Promise<Region> {
    const region = await this.regionRepository.findByCode(code);
    
    if (!region) {
      throw new NotFoundException(`Region with code ${code} not found`);
    }
    
    return region;
  }
}
