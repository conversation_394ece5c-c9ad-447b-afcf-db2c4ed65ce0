import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  HttpException,
  Injectable,
  InternalServerErrorException,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { from, Observable } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';
import { LocalTranslationService } from '../application/local-translation.service';
import { TRANSLATABLE_KEY } from '../decorators/translatable.decorator';
import { TRANSLATABLE_RESPONSE_KEY } from '../decorators/translate-response.decorator';
import { Language } from '../../shared/enums/languages.enum';
import { TRANSLATABLE_FIELD_PATTERNS, TRANSLATABLE_OBJECT_PATTERNS } from '../translation-registry';

/**
 * TranslationInterceptor
 * 
 * Intercepts HTTP responses and translates fields that are marked with @Translatable()
 * Only activates when the controller method is marked with @TranslateResponse()
 * Uses the Accept-Language header to determine the target language
 */
@Injectable()
export class TranslationInterceptor implements NestInterceptor {
  private readonly logger = new Logger(TranslationInterceptor.name);

  constructor(private readonly translationService: LocalTranslationService) {
    this.logger.log('TranslationInterceptor initialized');
  }

  /**
   * Intercept method called for each HTTP request
   */
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();

    // Check if @TranslateResponse is present on the handler or controller
    const handler = context.getHandler();
    const controller = context.getClass();
    const methodName = handler.name;

    // Look for @TranslateResponse on method or controller
    const shouldTranslateResponse =
      Reflect.getMetadata(TRANSLATABLE_RESPONSE_KEY, controller, methodName) ||
      Reflect.getMetadata(TRANSLATABLE_RESPONSE_KEY, controller);

    // Skip translation if decorator not found
    if (!shouldTranslateResponse) {
      this.logger.debug('[TranslationInterceptor] Skipping translation - @TranslateResponse not present');
      return next.handle();
    }

    // Get language from Accept-Language header
    const acceptLanguage = request.headers['accept-language'];
    if (!acceptLanguage) {
      this.logger.debug('[TranslationInterceptor] Skipping translation - no Accept-Language header');
      return next.handle();
    }

    // Determine target language from Accept-Language header
    let targetLanguage: Language;
    if (acceptLanguage.startsWith('fi')) {
      targetLanguage = Language.FI;
    } else if (acceptLanguage.startsWith('sv')) {
      targetLanguage = Language.SV;
    } else if (acceptLanguage.startsWith('en')) {
      this.logger.debug('[TranslationInterceptor] Skipping translation - language is already English');
      return next.handle();
    } else {
      this.logger.debug(`[TranslationInterceptor] Skipping translation - unsupported language: ${acceptLanguage}`);
      return next.handle();
    }

    this.logger.log(`Target language for translation: ${targetLanguage} (from Accept-Language: ${acceptLanguage})`);

    // Process the response using the handler
    return next.handle().pipe(
      switchMap((data) => {
        if (!data) return from([data]);
        
        const startTime = Date.now();
        // Translate the data
        if (process.env.NODE_ENV !== 'production') {
          this.logger.debug('[TranslationInterceptor] Starting translation of response data');
          if (process.env.ENABLE_DETAILED_LOGS === 'true') {
            this.debugObject(data, 0, 2);
          }
        }
        
        return from(this.translateData(data, targetLanguage)
          .then(translatedData => {
            const endTime = Date.now();
            if (process.env.NODE_ENV !== 'production' || process.env.LOG_TRANSLATION_TIMING === 'true') {
              this.logger.log(`Translation completed in ${endTime - startTime}ms`);
            }
            return translatedData;
          })
          .catch(translationError => { // Catch errors specifically from translateData
            this.logger.error(`[TranslationInterceptor] Error during data translation: ${translationError.message}`, translationError.stack);
            // Return the original data instead of throwing an error after response is sent
            // This prevents "Cannot set headers after they are sent" errors
            return data;
          }));
      }),
      catchError(err => { // Catch errors from the controller or the switchMap/translateData above
        this.logger.error(`[TranslationInterceptor] Error caught in pipe: ${err.message}`, err.stack);
        // Only re-throw if this is an error from the controller (before response is sent)
        // If it's an error from translation after the response, we've already handled it above
        if (err instanceof HttpException) {
          throw err; // Re-throw if already an HttpException
        }
        // Wrap other errors in a standard NestJS exception
        throw new InternalServerErrorException(err.message || 'An unexpected error occurred in translation interceptor');
      })
    );
  }

  /**
   * Debug helper to show object structure
   */
  private debugObject(data: any, depth: number = 0, maxDepth: number = 2): void {
    if (process.env.NODE_ENV === 'production') return;
    
    if (depth > maxDepth || !data || typeof data !== 'object') return;
    
    const prefix = '  '.repeat(depth);
    const keys = Object.keys(data);
    
    this.logger.debug(`${prefix}Object: ${keys.join(', ')}`);
    
    if (depth < maxDepth) {
      for (const key of keys) {
        if (data[key] && typeof data[key] === 'object') {
          this.logger.debug(`${prefix}→ ${key}:`);
          this.debugObject(data[key], depth + 1, maxDepth);
        }
      }
    }
  }

  /**
   * Main translation method that discovers and translates all translatable fields in an object
   * Uses a combination of:
   * 1. Direct annotation scanning (via reflection)
   * 2. Support for __translatable_fields property on serialized objects
   * 3. Field scanning when class is annotated with TranslatableClass
   */
  private async translateData(data: any, targetLanguage: Language): Promise<any> {
    // Skip translation altogether for English
    if (targetLanguage === Language.EN) {
      return data;
    }

    // Handle direct translation of arrays
    if (Array.isArray(data)) {
      // Process arrays in smaller batches to avoid overwhelming the system
      const batchSize = 5;
      const results: any[] = []; // Explicitly typed array to avoid TS error
      
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        const batchResults = await Promise.all(
          batch.map(item => this.translateData(item, targetLanguage))
        );
        results.push(...batchResults);
      }
      
      return results;
    }

    // Skip null or non-objects
    if (!data || typeof data !== 'object') {
      return data;
    }

    // Create a copy to avoid modifying the original
    const result = { ...data };
    
    // For each property in the data object - type assertion to handle TS error
    for (const key of Object.keys(result)) {
      const value = result[key as keyof typeof result];
      // Skip null values
      if (value === null || value === undefined) {
        continue;
      }

      // Fast path for common non-translatable fields
      const nonTranslatableFields = ['success', 'timestamp', 'message', 'meta', 'total', 'count', 'page', 'limit', 'has_next'];
      if (nonTranslatableFields.includes(key)) {
        continue;
      }
      
      // Find translatable fields from various sources
      const isTranslatable = await this.isFieldTranslatable(data, key);
      
      if (isTranslatable && typeof value === 'string') {
        // Translate the string
        try {
          result[key] = await this.translationService.translateText(value, targetLanguage);
          if (process.env.NODE_ENV !== 'production' && process.env.ENABLE_DETAILED_LOGS === 'true') {
            const snippet = value.length > 30 ? value.substring(0, 30) + '...' : value;
            const translatedSnippet = result[key].length > 30 ? result[key].substring(0, 30) + '...' : result[key];
            this.logger.debug(`[Translated] ${key}: "${snippet}" → "${translatedSnippet}"`);
          }
        } catch (err) {
          if (!err.message.includes('timeout') && !err.message.includes('network')) {
            this.logger.warn(`Translation failed for ${key}: ${err.message}`);
          }
        }
      } else if (isTranslatable && Array.isArray(value)) {
        // Handle arrays of strings marked as translatable
        result[key] = await Promise.all(
          value.map(async (item) => {
            if (typeof item === 'string') {
              try {
                return await this.translationService.translateText(item, targetLanguage);
              } catch (err) {
                if (!err.message.includes('timeout') && !err.message.includes('network')) {
                  this.logger.warn(`Translation failed for array item in ${key}: ${err.message}`);
                }
                return item;
              }
            }
            return this.translateData(item, targetLanguage);
          })
        );
      } else if (typeof value === 'object') {
        // Recursively process objects and arrays
        result[key] = await this.translateData(value, targetLanguage);
      }
    }
    
    return result;
  }
  
  /**
   * Determines if a field should be translated by checking:
   * 1. Class metadata for @Translatable decorator
   * 2. Direct __translatable_fields property on object
   * 3. Field scanning when class is annotated with TranslatableClass
   */
  private async isFieldTranslatable(obj: any, key: string): Promise<boolean> {
    // Fast path for common non-translatable fields
    const nonTranslatableFields = ['success', 'timestamp', 'message', 'meta', 'total', 'count', 'page', 'limit', 'has_next'];
    if (nonTranslatableFields.includes(key)) {
      return false;
    }
    
    // Case 1: Check class metadata via reflection (most reliable)
    try {
      const constructor = Object.getPrototypeOf(obj)?.constructor;
      if (constructor) {
        const metadata = Reflect.getMetadata(TRANSLATABLE_KEY, constructor) || [];
        if (Array.isArray(metadata) && metadata.includes(key)) {
          return true;
        }
      }
    } catch (err) {
      // Safely ignore reflection errors (can happen with serialized objects)
    }
    
    // Case 2: Check for embedded __translatable_fields property
    if (obj.__translatable_fields && 
        Array.isArray(obj.__translatable_fields) && 
        obj.__translatable_fields.includes(key)) {
      return true;
    }
    
    // Case 3: Check object patterns from registry
    for (const pattern of TRANSLATABLE_OBJECT_PATTERNS) {
      if (pattern.pattern(obj) && pattern.fields.includes(key)) {
        return true;
      }

      // Handle nested objects (special case for summary, evolution, market_salary)
      const nestedObjectKeys = ['summary', 'evolution', 'market_salary'];
      if (nestedObjectKeys.includes(key) && obj[key] && typeof obj[key] === 'object') {
        // Instead of just marking the container as translatable,
        // we actually need to check the individual fields inside
        // to correctly identify what to translate
        const nestedObj = obj[key];
        if (key === 'evolution' && nestedObj.evolution_summary) {
          // Don't return true - we want to recurse into the object itself
        }
        else if (key === 'summary' && (nestedObj.elevator_pitch || nestedObj.primary_tasks)) {
          // Don't return true - we want to recurse into the object itself
        }
        else if (key === 'market_salary' && nestedObj.job_role) {
          // Don't return true - we want to recurse into the object itself
        }
      }
    }
    
    // Case 4: Check field name patterns
    for (const pattern of TRANSLATABLE_FIELD_PATTERNS) {
      if (pattern.test(key)) {
        return true;
      }
    }
    
    // Special handling for nested objects with translatable fields
    if (key === 'evolution' || key === 'summary' || key === 'market_salary') {
      return true; // Mark these objects as requiring recursive processing
    }
    
    return false;
  }
}
