/**
 * Global registry for translation patterns and rules
 * 
 * This centralizes all translatable field patterns in one place
 * to avoid hardcoding field names throughout the codebase.
 */

/**
 * Registry of common translatable field patterns
 * Any field matching these patterns will be considered for translation
 */
export const TRANSLATABLE_FIELD_PATTERNS = [
  // Text content fields
  /description$/i,
  /summary$/i,
  /content$/i,
  /text$/i,
  /note$/i,
  /header$/i,
  
  // Label fields
  /title$/i,
  /name$/i,
  /label$/i,
  
  // Job-related fields
  /occupation$/i,
  /industry$/i,
  /job_role$/i,
  /pitch$/i,
  /tasks$/i,
  
  // Classification fields
  /classification_data/i,
  /reliability$/i,
  
  // Skills fields
  /skills$/i,
  /context/i,
  
  // Analysis fields
  /impact$/i,
  /prediction$/i,
  
  // Reason fields
  /why/i,
  /reason/i,
];

/**
 * Known object types that might need translation even when serialized
 * Each entry describes an object pattern and its translatable keys
 */
export const TRANSLATABLE_OBJECT_PATTERNS = [
  // ForYou objects
  {
    pattern: (obj: any) => 
      obj && 
      (obj.classification_data !== undefined || obj.why !== undefined) && 
      obj.recommended_at !== undefined,
    fields: ['classification_data', 'why']
  },
  
  // Job objects
  {
    pattern: (obj: any) => 
      obj && 
      obj.title !== undefined && 
      obj.occupation !== undefined,
    fields: ['title', 'occupation', 'industry', 'skill_contexts']
  },
  
  // JobSummary objects
  {
    pattern: (obj: any) => 
      obj && 
      obj.elevator_pitch !== undefined && 
      obj.primary_tasks !== undefined,
    fields: ['elevator_pitch', 'primary_tasks']
  },
  
  // JobEvolution objects
  {
    pattern: (obj: any) => 
      obj && 
      obj.evolution_summary !== undefined,
    fields: ['evolution_summary', 'automation_impact', 'relevance_prediction']
  },
  
  // MarketSalary objects
  {
    pattern: (obj: any) => 
      obj && 
      obj.job_role !== undefined && 
      obj.median_salary !== undefined,
    fields: ['job_role', 'data_reliability']
  },
  
  // ClassifyJob objects
  {
    pattern: (obj: any) => 
      obj && 
      obj.mySkills !== undefined && 
      obj.missingSkills !== undefined,
    fields: ['mySkills', 'missingSkills', 'header', 'note']
  },
  
  // SkillSuggestions objects
  {
    pattern: (obj: any) => 
      obj && 
      Array.isArray(obj.skills),
    fields: ['skills']
  }
];
