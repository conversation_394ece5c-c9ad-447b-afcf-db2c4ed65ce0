import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { APP_INTERCEPTOR } from '@nestjs/core';

import { VertexTranslationsService } from './application/vertex-translation.service';
import { LocalTranslationService } from './application/local-translation.service';
import { TranslationController } from './controllers/translation.controller';
import { LocalTranslation } from '../../entities/local-translation.entity';
import { TranslationInterceptor } from './interceptors/translation.interceptor';
import { GeminiModule } from '../../gemini/gemini.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([LocalTranslation]),
    GeminiModule,
  ],
  controllers: [TranslationController],
  providers: [
    VertexTranslationsService,
    LocalTranslationService,
    {
      provide: APP_INTERCEPTOR,
      useFactory: (translationService: LocalTranslationService) => {
        return new TranslationInterceptor(translationService);
      },
      inject: [LocalTranslationService]
    },
  ],
  exports: [VertexTranslationsService, LocalTranslationService],
})
export class TranslationModule {}
