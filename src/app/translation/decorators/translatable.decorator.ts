import 'reflect-metadata';

export const TRANSLATABLE_KEY = 'translatable';

/**
 * Decorator to mark fields that need translation
 * Properties decorated with @Translatable() will be automatically translated
 * based on the Accept-Language header in the request
 */
export function Translatable(): PropertyDecorator {
  return (target: any, propertyKey: string | symbol) => {
    // Collect translatable fields in metadata array on the class
    const ctor = target.constructor;
    const existing: (string | symbol)[] =
      Reflect.getMetadata(TRANSLATABLE_KEY, ctor) || [];
    if (!existing.includes(propertyKey)) {
      existing.push(propertyKey);
      Reflect.defineMetadata(TRANSLATABLE_KEY, existing, ctor);
    }
  };
}
