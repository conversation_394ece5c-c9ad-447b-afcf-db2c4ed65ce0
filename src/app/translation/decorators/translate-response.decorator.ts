import 'reflect-metadata';

export const TRANSLATABLE_RESPONSE_KEY = 'translatable_response';

/**
 * Decorator to mark controller classes or methods whose responses should be translated
 * based on the Accept-Language header.
 */
export function TranslateResponse(): MethodDecorator & ClassDecorator {
  return (target: object, propertyKey?: string | symbol) => {
    if (propertyKey) {
      Reflect.defineMetadata(
        TRANSLATABLE_RESPONSE_KEY,
        true,
        target.constructor,
        propertyKey,
      );
    } else {
      Reflect.defineMetadata(TRANSLATABLE_RESPONSE_KEY, true, target);
    }
  };
}
