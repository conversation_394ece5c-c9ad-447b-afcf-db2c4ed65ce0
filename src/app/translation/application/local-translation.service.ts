import { Injectable, Logger, Optional } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as deepl from 'deepl-node';
import * as crypto from 'crypto';
import { LocalTranslation } from '../../../entities/local-translation.entity';
import { Language } from '../../shared/enums/languages.enum';
import { GeminiService } from '../../../gemini/gemini.service';

@Injectable()
export class LocalTranslationService {
  private readonly translator: deepl.Translator | null;
  private readonly logger = new Logger(LocalTranslationService.name);

  constructor(
    @InjectRepository(LocalTranslation)
    private localTranslationRepository: Repository<LocalTranslation>,
    @Optional() private readonly geminiService: GeminiService,
  ) {
    const authKey = process.env.DEEPL_AUTH_KEY;
    if (!authKey) {
      this.logger.warn(
        'DEEPL_AUTH_KEY is not defined in environment variables. DeepL will not be available as fallback.',
      );
      this.translator = null;
    } else {
      this.translator = new deepl.Translator(authKey);
    }
    
    if (this.geminiService) {
      this.logger.log('LocalTranslationService initialized with Gemini as primary and DeepL as fallback');
    } else if (this.translator) {
      this.logger.log('LocalTranslationService initialized with DeepL only (Gemini service not available)');
    } else {
      this.logger.error('No translation service available!');
    }
  }

  /**
   * Generates a hash for a text and target language
   * @param text - Original text to translate
   * @param targetLanguage - Target language code
   * @returns A SHA-256 hash of the text and target language
   */
  private generateHash(text: string, targetLanguage: Language): string {
    const hash = crypto
      .createHash('sha256')
      .update(`${text}:${targetLanguage}`)
      .digest('hex');
    
    this.logger.debug(`[Hash] Generated hash: ${hash.substring(0, 8)}... for text: "${text.substring(0, 20)}..." to ${targetLanguage}`);
    return hash;
  }

  /**
   * Translates text from English to a target language (Finnish or Swedish)
   * Uses cache if available, otherwise calls DeepL API
   *
   * @param text - The text to translate (in English)
   * @param targetLanguage - The target language
   * @returns Translated text
   */
  async translateText(text: string, targetLanguage: Language): Promise<string> {
    // Skip translation for empty strings
    if (!text || !text.trim()) {
      this.logger.debug('[Translate] Skipping translation for empty string');
      return text;
    }
    
    // Log input text for debugging
    const textPreview = text.length > 30 ? `${text.substring(0, 30)}...` : text;
    this.logger.debug(`[Translate] Request to translate: "${textPreview}" to ${targetLanguage}`);
    
    // Generate hash for the text + target language
    const textHash = this.generateHash(text, targetLanguage);

    try {
      // Check if we have a cached translation
      this.logger.debug(`[Cache] Looking for cached translation with hash: ${textHash.substring(0, 8)}...`);
      const cachedTranslation = await this.localTranslationRepository.findOne({
        where: { textHash, targetLanguage },
      });

      // If we have a cached translation, return it
      if (cachedTranslation) {
        const cachedTextPreview = cachedTranslation.translatedText.length > 30 
          ? `${cachedTranslation.translatedText.substring(0, 30)}...` 
          : cachedTranslation.translatedText;
        
        this.logger.debug(`[Cache] HIT for hash: ${textHash.substring(0, 8)}..., returning: "${cachedTextPreview}"`);
        return cachedTranslation.translatedText;
      }

      // No cached translation, try Gemini first, then DeepL as fallback
      this.logger.debug(`[Cache] MISS for hash: ${textHash.substring(0, 8)}..., using translation API`);

      let translatedText: string;
      
      // Try Gemini first if available
      if (this.geminiService) {
        try {
          this.logger.debug(`[Gemini] Attempting translation with target language: ${targetLanguage}`);
          translatedText = await this.geminiService.translate(text, targetLanguage.toLowerCase(), 'en');
          this.logger.debug(`[Gemini] Successfully translated text`);
        } catch (geminiError) {
          this.logger.warn(`[Gemini] Translation failed: ${geminiError.message}`);
          
          // Fall back to DeepL if available
          if (this.translator) {
            this.logger.debug(`[DeepL] Falling back to DeepL translation`);
            const deeplTargetLang = targetLanguage === Language.FI ? 'FI' : 'SV';
            const result = await this.translator.translateText(
              text,
              null,
              deeplTargetLang as deepl.TargetLanguageCode,
            );
            translatedText = result.text;
            this.logger.debug(`[DeepL] Successfully translated text`);
          } else {
            throw new Error('No fallback translation service available');
          }
        }
      } else if (this.translator) {
        // No Gemini, use DeepL directly
        this.logger.debug(`[DeepL] Using DeepL as primary (Gemini not available)`);
        const deeplTargetLang = targetLanguage === Language.FI ? 'FI' : 'SV';
        const result = await this.translator.translateText(
          text,
          null,
          deeplTargetLang as deepl.TargetLanguageCode,
        );
        translatedText = result.text;
      } else {
        throw new Error('No translation service available');
      }

      const translatedTextPreview = translatedText.length > 30 
        ? `${translatedText.substring(0, 30)}...` 
        : translatedText;
        
      this.logger.debug(`[Translation] Received translation: "${translatedTextPreview}"`);

      // Save the translation to cache
      try {
        this.logger.debug(`[Cache] Saving translation to database for hash: ${textHash.substring(0, 8)}...`);
        
        await this.localTranslationRepository.save({
          textHash,
          originalText: text,
          targetLanguage,
          translatedText,
        });
        
        this.logger.debug(`[Cache] Successfully saved translation to database`);
      } catch (dbError) {
        // If there's a duplicate key error, it means another process has already saved this translation
        // We can just fetch and return that translation instead
        if (dbError.message && dbError.message.includes('duplicate key value violates unique constraint')) {
          this.logger.warn(
            `[Cache] Race condition detected for hash: ${textHash.substring(0, 8)}... Error: ${dbError.message}`,
          );
          
          this.logger.debug(`[Cache] Attempting to fetch existing translation after race condition`);
          const existingTranslation = await this.localTranslationRepository.findOne({
            where: { textHash, targetLanguage },
          });
          
          if (existingTranslation) {
            const existingTextPreview = existingTranslation.translatedText.length > 30 
              ? `${existingTranslation.translatedText.substring(0, 30)}...` 
              : existingTranslation.translatedText;
            
            this.logger.debug(`[Cache] Found existing translation after race condition: "${existingTextPreview}"`);
            return existingTranslation.translatedText;
          } else {
            this.logger.warn(`[Cache] Could not find existing translation after race condition, using new translation`);
          }
          // If for some reason we still can't find it, continue with our translation
        } else {
          // For other database errors, log and rethrow
          this.logger.error(
            `[Cache] Failed to save translation: ${dbError.message}`,
            dbError.stack,
          );
          throw dbError;
        }
      }

      return translatedText;
    } catch (error) {
      this.logger.error(
        `[Translate] Failed to translate text: ${error.message}`,
        error.stack,
      );
      throw new Error(`Translation failed: ${error.message}`);
    }
  }

  /**
   * Batch translate multiple texts from English to a target language
   *
   * @param texts - Array of texts to translate
   * @param targetLanguage - The target language
   * @returns Array of translated texts in the same order
   */
  async batchTranslate(
    texts: string[],
    targetLanguage: Language,
  ): Promise<string[]> {
    this.logger.debug(`[Batch] Translating ${texts.length} texts to ${targetLanguage}`);
    
    // Process texts in parallel using Promise.all
    const results = await Promise.all(
      texts.map(async (text, index) => {
        try {
          const translated = await this.translateText(text, targetLanguage);
          this.logger.debug(`[Batch] Successfully translated item ${index}`);
          return translated;
        } catch (error) {
          this.logger.error(`[Batch] Failed to translate item ${index}: ${error.message}`);
          return text; // Return original text on error
        }
      }),
    );
    
    this.logger.debug(`[Batch] Completed batch translation of ${texts.length} texts`);
    return results;
  }
}
