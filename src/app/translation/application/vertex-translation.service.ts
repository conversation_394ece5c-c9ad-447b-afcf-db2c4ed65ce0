/* eslint-disable @typescript-eslint/no-unsafe-argument */
// TAKEN FROM MAIN APP

/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-return */

import { Injectable } from '@nestjs/common';
import {
  GenerativeModelPreview,
  SafetySetting,
  VertexAI,
} from '@google-cloud/vertexai';

import { Language as Lang } from '../../shared/enums/languages.enum';
import { translatePrompt } from './prompts/translation.prompt';

type Language = Lang | string;

@Injectable()
export class VertexTranslationsService {
  private model: string;
  private projectId: string;
  private location: string;

  private client: GenerativeModelPreview;

  constructor() {
    this.projectId = process.env.GCP_PROJECT_ID!;
    this.location = process.env.GCP_LOCATION!;
    this.model = process.env.VERTEX_AI_MODEL!;

    this.client = this.initVertex();
  }

  async translate(
    text: string,
    language: { source: Language; target: Language },
  ): Promise<string> {
    const { source, target } = language;

    const question = translatePrompt
      .replace('#SOURCE', source)
      .replace('#TARGET', target)
      .replace('#TEXT', text);

    const request = {
      contents: [{ role: 'user', parts: [{ text: question }] }],
    };

    const completion = await this.client.generateContent(request)
    const translatedText = completion.response.candidates![0].content.parts[0].text

    return translatedText ?? ''
  }

  private initVertex() {
    const safetySettings: SafetySetting[] = [];

    const config: Record<string, string> = {
      client_id: process.env.GCP_CLIENT_ID!,
      client_secret: process.env.GCP_CLIENT_SECRET!,
      refresh_token: process.env.GCP_REFRESH_TOKEN!,
      account: '',
      type: 'authorized_user',
      quota_project_id: this.projectId,
      universe_domain: 'googleapis.com',
    };

    const vertexAi = new VertexAI({
      project: this.projectId,
      location: this.location,
      googleAuthOptions: { credentials: config },
    });

    const isContentFilterEnabled =
      !!process.env.VERTEX_AI_MODEL_CONTENT_FILTER_ENABLED;
    if (isContentFilterEnabled)
      safetySettings.push(...this.getContentFilters());

    return vertexAi.preview.getGenerativeModel({
      model: this.model,
      generationConfig: {
        topK: 1,
        candidateCount: 1,
        maxOutputTokens: +process.env.VERTEX_AI_MODEL_MAX_TOKEN_OUTPUT!, // 1 TOKEN = 1 WORD
        temperature: +process.env.VERTEX_AI_MODEL_TEMPERATURE!,
      },
      safetySettings,
    });
  }

  private getContentFilters() {
    return [
      {
        category: 'HARM_CATEGORY_HATE_SPEECH',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_DANGEROUS_CONTENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
      {
        category: 'HARM_CATEGORY_HARASSMENT',
        threshold: 'BLOCK_MEDIUM_AND_ABOVE',
      },
    ] as SafetySetting[];
  }
}
