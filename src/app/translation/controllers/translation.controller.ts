import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Post,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { LocalTranslationService } from '../application/local-translation.service';
import {
  ApiBody,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { IsArray, IsEnum, IsNotEmpty, IsString } from 'class-validator';
import { Language } from '../../shared/enums/languages.enum';

export class TranslateTextDto {
  @IsString()
  @IsNotEmpty()
  text: string;

  @IsEnum(Language)
  @IsNotEmpty()
  targetLanguage: Language;
}

export class BatchTranslateDto {
  @IsArray()
  @IsString({ each: true })
  @IsNotEmpty({ each: true })
  texts: string[];

  @IsEnum(Language)
  @IsNotEmpty()
  targetLanguage: Language;
}

export class TranslationResponseDto {
  translatedText: string;
}

export class BatchTranslationResponseDto {
  translatedTexts: string[];
}

@ApiTags('translation')
@Controller('api/translation')
export class TranslationController {
  constructor(
    private readonly localTranslationService: LocalTranslationService,
  ) {}

  @Post('translate')
  @ApiOperation({
    summary: 'Translate a single text from English to Finnish or Swedish',
  })
  @ApiBody({ type: TranslateTextDto })
  @ApiResponse({
    status: 200,
    description: 'Translation successful',
    type: TranslationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async translateText(
    @Body() translateTextDto: TranslateTextDto,
  ): Promise<TranslationResponseDto> {
    try {
      const translatedText = await this.localTranslationService.translateText(
        translateTextDto.text,
        translateTextDto.targetLanguage,
      );
      return { translatedText };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Post('batch')
  @ApiOperation({
    summary: 'Translate multiple texts from English to Finnish or Swedish',
  })
  @ApiBody({ type: BatchTranslateDto })
  @ApiResponse({
    status: 200,
    description: 'Translations successful',
    type: BatchTranslationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  @UsePipes(new ValidationPipe({ transform: true }))
  async batchTranslate(
    @Body() batchTranslateDto: BatchTranslateDto,
  ): Promise<BatchTranslationResponseDto> {
    try {
      const translatedTexts = await this.localTranslationService.batchTranslate(
        batchTranslateDto.texts,
        batchTranslateDto.targetLanguage,
      );
      return { translatedTexts };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @Get('translate')
  @ApiOperation({ summary: 'Translate a text using GET method' })
  @ApiQuery({
    name: 'text',
    required: true,
    description: 'Text to translate (English)',
  })
  @ApiQuery({
    name: 'targetLanguage',
    required: true,
    enum: Language,
    description: 'Target language code (fi or sv)',
  })
  @ApiResponse({
    status: 200,
    description: 'Translation successful',
    type: TranslationResponseDto,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async translateTextGet(
    @Query('text') text: string,
    @Query('targetLanguage') targetLanguage: string,
  ): Promise<TranslationResponseDto> {
    if (!text) {
      throw new BadRequestException('Text is required');
    }

    let langEnum: Language;
    try {
      // Convert string to enum
      if (Object.values(Language).includes(targetLanguage as Language)) {
        langEnum = targetLanguage as Language;
      } else {
        throw new Error();
      }
    } catch {
      throw new BadRequestException(
        'Target language must be a valid language code (fi or sv)',
      );
    }

    try {
      const translatedText = await this.localTranslationService.translateText(
        text,
        langEnum,
      );
      return { translatedText };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
