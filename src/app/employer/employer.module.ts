import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EmployerController } from './controller/employer.controller';
import { EmployerRepository } from './repository/employer.repository';
import { Employer } from '../../entities/employer.entity';
import { EmployerService } from './services/employer.service';

@Module({
  imports: [TypeOrmModule.forFeature([Employer])],
  controllers: [EmployerController],
  providers: [EmployerRepository, EmployerService],
  exports: [EmployerRepository, EmployerService],
})
export class EmployerModule {}
