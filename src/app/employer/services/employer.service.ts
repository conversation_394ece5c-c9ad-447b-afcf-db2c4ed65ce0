import { Injectable, Logger } from '@nestjs/common';
import { EmployerRepository } from '../repository/employer.repository';
import { JobPostingEN } from '../../jobs/services/interfaces/job-market-response-En.interface';
import { EmployerData } from '../repository/interfaces/employer-data.interface';
import { jobMarketConstants } from '../../jobs/services/constants/job-market.constants';
import { Employer } from '../../../entities/employer.entity';
import { JoblyJob } from '../../jobs/services/interfaces/jobly-job.interface';

@Injectable()
export class EmployerService {
  private readonly logger = new Logger(EmployerService.name);

  constructor(private readonly employerRepository: EmployerRepository) {}

  /**
   * Extract employer information from a job posting
   * @param jobPosting The job posting to extract employer info from
   * @returns EmployerData object with extracted information
   */
  extractEmployerData(jobPosting: JobPostingEN): EmployerData {
    // Extract the employer name in the first available language
    const employerName = jobPosting.employerName?.[0]?.value || '';
    const languageCode = jobPosting.employerName?.[0]?.languageCode || 'fi';

    // Create the employer data object
    const employerData: EmployerData = {
      business_id: jobPosting.employerBusinessID || null,
      type: jobPosting.employer as (typeof jobMarketConstants.EmployerTypes)[keyof typeof jobMarketConstants.EmployerTypes],
      name: employerName,
      language_code: languageCode,
    };

    return employerData;
  }

  /**
   * Create or update an employer from job posting data
   * @param jobPosting The job posting containing employer information
   * @returns The created or updated employer
   */
  async createOrUpdateEmployerFromJobPosting(jobPosting: JobPostingEN) {
    const employerData = this.extractEmployerData(jobPosting);

    if (employerData.business_id) {
      this.logger.debug(
        `Upserting employer by business_id: ${employerData.business_id} for name: ${employerData.name}`,
      );
      return this.employerRepository.upsertByBusinessId(employerData);
    } else {
      this.logger.warn(
        `Employer '${employerData.name}' from job posting (Source Job ID: ${jobPosting.jobID || 'N/A'}) is missing a business ID. Attempting to create new employer record.`,
      );
      // Consider adding logic here to find employer by name to prevent duplicates if desired in the future.
      // For now, create a new one if business_id is missing.
      return this.employerRepository.create(employerData);
    }
  }

  /**
   * Create or update an employer from Jobly job data
   * @param joblyJob The Jobly job containing employer information
   * @returns The created or updated employer
   */
  async createOrUpdateEmployerFromJobly(joblyJob: JoblyJob): Promise<Employer> {
    // Extract employer information from Jobly job
    const employerData: EmployerData = {
      name: joblyJob.company,
      language_code: 'fi', // Default to Finnish for Jobly
      type: jobMarketConstants.EmployerTypes.COMPANY, // Default type
      logo_url: joblyJob.company_logo || undefined,
    };

    // Use the new upsertEmployer method with 'Jobly' as the source
    return this.employerRepository.upsertEmployer(employerData, 'Jobly');
  }
}
