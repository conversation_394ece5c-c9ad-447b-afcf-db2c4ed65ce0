import { Controller, Get, NotFoundException, Param } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { EmployerRepository } from '../repository/employer.repository';
import { Employer } from '../../../entities/employer.entity';

@ApiTags('employers')
@Controller('v1/employers')
export class EmployerController {
  constructor(private readonly employerRepository: EmployerRepository) {}

  @Get()
  @ApiOperation({ summary: 'Get all employers' })
  @ApiResponse({
    status: 200,
    description: 'Return all employers',
    type: [Employer],
  })
  async findAll(): Promise<Employer[]> {
    return this.employerRepository.findAll();
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get employer by ID' })
  @ApiParam({ name: 'id', type: 'number', description: 'Employer ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the employer',
    type: Employer,
  })
  @ApiResponse({ status: 404, description: 'Employer not found' })
  async findOne(@Param('id') id: number): Promise<Employer> {
    const employer = await this.employerRepository.findById(id);
    if (!employer) {
      throw new NotFoundException(`Employer with ID ${id} not found`);
    }
    return employer;
  }

  @Get('business/:businessId')
  @ApiOperation({ summary: 'Get employer by business ID' })
  @ApiParam({ name: 'businessId', type: 'string', description: 'Business ID' })
  @ApiResponse({
    status: 200,
    description: 'Return the employer',
    type: Employer,
  })
  @ApiResponse({ status: 404, description: 'Employer not found' })
  async findByBusinessId(
    @Param('businessId') businessId: string,
  ): Promise<Employer> {
    const employer = await this.employerRepository.findByBusinessId(businessId);
    if (!employer) {
      throw new NotFoundException(
        `Employer with business ID ${businessId} not found`,
      );
    }
    return employer;
  }
}
