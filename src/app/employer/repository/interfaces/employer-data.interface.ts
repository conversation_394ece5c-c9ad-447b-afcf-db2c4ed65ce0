import { CompanySize, CompanyType } from '../../../../entities/employer.entity';
import { jobMarketConstants } from '../../../jobs/services/constants/job-market.constants';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  IsUrl,
  Length,
  Matches,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class EmployerData {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  @Matches(/^\d{7}-\d$/, {
    message: 'Business ID must be in format: 1234567-8',
  })
  business_id?: string | null;

  @ApiProperty({ enum: jobMarketConstants.EmployerTypes })
  @IsEnum(jobMarketConstants.EmployerTypes)
  type: (typeof jobMarketConstants.EmployerTypes)[keyof typeof jobMarketConstants.EmployerTypes];

  @ApiProperty()
  @IsString()
  @Length(1, 255)
  name: string;

  @ApiProperty()
  @IsString()
  @Length(2, 3)
  language_code: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  businessInfoUrl?: string;

  @ApiProperty({ enum: CompanyType, required: false })
  @IsOptional()
  @IsEnum(CompanyType)
  company_type?: CompanyType;

  @ApiProperty({ enum: CompanySize, required: false })
  @IsOptional()
  @IsEnum(CompanySize)
  company_size?: CompanySize;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsUrl()
  logo_url?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  social_media?: Record<string, string>;
}
