import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Employer } from '../../../entities/employer.entity';
import { EmployerData } from './interfaces/employer-data.interface';

@Injectable()
export class EmployerRepository {
  constructor(
    @InjectRepository(Employer)
    private readonly employerRepository: Repository<Employer>,
  ) {}

  async findAll(): Promise<Employer[]> {
    return this.employerRepository.find();
  }

  async findByBusinessId(businessId: string): Promise<Employer | null> {
    return this.employerRepository.findOne({
      where: { business_id: businessId },
    });
  }

  async findById(id: number): Promise<Employer | null> {
    return this.employerRepository.findOne({
      where: { id },
    });
  }

  async create(employerData: EmployerData): Promise<Employer> {
    const employer = this.employerRepository.create(employerData);
    return this.employerRepository.save(employer);
  }

  async update(
    id: number,
    employerData: Partial<EmployerData>,
  ): Promise<Employer> {
    await this.employerRepository.update(id, employerData);
    const updated = await this.findById(id);
    if (!updated) {
      throw new NotFoundException(
        `Employer with ID ${id} not found after update`,
      );
    }
    return updated;
  }

  async upsertByBusinessId(employerData: EmployerData): Promise<Employer> {
    if (!employerData.business_id) {
      throw new Error('Business ID is required for upsertByBusinessId');
    }

    const existingEmployer = await this.findByBusinessId(
      employerData.business_id,
    );

    if (existingEmployer) {
      // Update existing employer
      return this.update(existingEmployer.id, employerData);
    } else {
      // Create new employer
      return this.create(employerData);
    }
  }

  /**
   * Find an employer by name and source
   * @param name Employer name
   * @param source Source of the employer data (e.g., 'Jobly')
   * @returns The employer if found, null otherwise
   */
  async findByNameAndSource(
    name: string,
    source: string,
  ): Promise<Employer | null> {
    return this.employerRepository.findOne({
      where: {
        name,
        // For source, we'll store it in the description field for now
        description: `source:${source}`,
      },
    });
  }

  /**
   * Upsert an employer using name and source as the primary key
   * This is used for sources like Jobly where business_id is not available
   * @param employerData The employer data to create or update
   * @param source The source of the employer data (e.g., 'Jobly')
   * @returns The created or updated employer
   */
  async upsertEmployer(
    employerData: EmployerData,
    source: string,
  ): Promise<Employer> {
    // If business_id is provided, use the standard upsertByBusinessId method
    if (employerData.business_id) {
      return this.upsertByBusinessId(employerData);
    }

    // Store the source in the description field
    if (!employerData.description) {
      employerData.description = `source:${source}`;
    } else if (!employerData.description.includes(`source:${source}`)) {
      employerData.description = `${employerData.description} | source:${source}`;
    }

    // Try to find by name and source
    const existingEmployer = await this.findByNameAndSource(
      employerData.name,
      source,
    );

    if (existingEmployer) {
      // Update existing employer
      return this.update(existingEmployer.id, employerData);
    } else {
      // Create new employer
      return this.create(employerData);
    }
  }
}
