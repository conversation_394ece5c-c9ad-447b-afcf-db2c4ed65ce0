import { Injectable, NestMiddleware, BadRequestException } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { ZodSchema } from 'zod';

@Injectable()
export class ValidationMiddleware implements NestMiddleware {
  constructor(private schema: ZodSchema) {}

  use(req: Request, res: Response, next: NextFunction) {
    try {
      const validatedBody = this.schema.parse(req.body);
      req.body = validatedBody;
      next();
    } catch (error) {
      throw new BadRequestException({
        message: 'Validation failed',
        errors: error.errors?.map((e: any) => ({
          field: e.path.join('.'),
          message: e.message,
        })),
      });
    }
  }
}

// Helper function to create validation middleware
export const createValidationMiddleware = (schema: ZodSchema) => {
  return new ValidationMiddleware(schema);
};
