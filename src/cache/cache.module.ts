import { Lo<PERSON>, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { createKeyv } from '@keyv/redis';
import { ConfigService } from '@nestjs/config';

import { Cacheable } from 'cacheable';
import { CacheService } from './cache.service';

@Module({
  providers: [
    {
      provide: 'CACHE_INSTANCE',
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const logger = new Logger('CacheModule');

        try {
          // Get the Redis URL or use a fallback
          const uri = configService.get('REDIS_URL');

          if (!uri) {
            logger.warn(
              'REDIS_URL not configured, using in-memory cache instead',
            );
            return new Cacheable({ ttl: '24h' });
          }

          logger.log(`Connecting to Redis: ${uri.split('@').pop()}`); // Don't log full URL with credentials

          // Create the Redis client with simple configuration
          // Note: @keyv/redis doesn't support detailed connection options at this level
          const secondary = createKeyv(uri);

          // Add event listeners for connection issues
          secondary.on('error', (err) => {
            logger.error(`Redis connection error: ${err.message}`);
          });

          secondary.on('connect', () => {
            logger.log('Connected to Redis');
          });

          return new Cacheable({ secondary, ttl: '24h' });
        } catch (error) {
          logger.error(
            `Failed to initialize Redis cache: ${error.message}`,
            error.stack,
          );
          logger.warn('Falling back to in-memory cache');
          return new Cacheable({ ttl: '24h' }); // Fallback to memory-only cache
        }
      },
    },
    CacheService,
  ],
  exports: ['CACHE_INSTANCE', CacheService],
})
export class CacheModule {}
