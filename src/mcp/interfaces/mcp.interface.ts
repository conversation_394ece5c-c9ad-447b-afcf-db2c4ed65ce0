import { z } from 'zod';

export interface MCPContext {
  userId: string;
  requestId: string;
  source: 'dify_agent' | 'jobly_agent' | 'user_service' | 'admin_portal';
  reason: string;
  sessionInfo?: {
    accessToken: string;
    sessionHandle: string;
  };
}

export const MCPContextSchema = z.object({
  userId: z.string().min(1, 'userId is required'),
  requestId: z.string().min(1, 'requestId is required'),
  source: z.enum(['dify_agent', 'jobly_agent', 'user_service', 'admin_portal']),
  reason: z.string().min(1, 'reason is required'),
  sessionInfo: z.object({
    accessToken: z.string(),
    sessionHandle: z.string(),
  }).optional(),
});

export interface MCPTool<TInput = any, TOutput = any> {
  name: string;
  description: string;
  inputSchema: z.ZodSchema<any>;
  handler: (args: TInput, context: MCPContext) => Promise<TOutput>;
  requiresAuth?: boolean;
  rateLimit?: {
    maxRequests: number;
    windowMs: number;
  };
}

export interface MCPRequest {
  method: string;
  params?: any;
  id?: string | number;
}

export interface MCPResponse {
  result?: any;
  error?: MCPError;
  id?: string | number;
}

export interface MCPError {
  code: number;
  message: string;
  data?: any;
}

export const MCPErrorCodes = {
  PARSE_ERROR: -32700,
  INVALID_REQUEST: -32600,
  METHOD_NOT_FOUND: -32601,
  INVALID_PARAMS: -32602,
  INTERNAL_ERROR: -32603,
  AUTHENTICATION_ERROR: -32001,
  AUTHORIZATION_ERROR: -32002,
  RATE_LIMIT_ERROR: -32003,
} as const;

export interface MCPToolMetadata {
  name: string;
  description: string;
  version: string;
  category: 'search' | 'user_data' | 'analytics' | 'cv' | 'education';
  lastUpdated: Date;
}