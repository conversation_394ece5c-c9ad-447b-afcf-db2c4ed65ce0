import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import { AuthService } from '../../auth/services/auth.service';

@Injectable()
export class MCPAuthGuard implements CanActivate {
  private readonly logger = new Logger(MCPAuthGuard.name);
  private readonly validServiceTokens: Set<string>;

  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    // Initialize valid service tokens from environment
    const tokens = this.configService.get<string>('MCP_SERVICE_TOKENS', '');
    this.validServiceTokens = new Set(
      tokens.split(',').map(t => t.trim()).filter(Boolean)
    );
    
    if (this.validServiceTokens.size === 0) {
      this.logger.warn('No MCP service tokens configured');
    }
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const serviceToken = this.extractServiceToken(request);
    const correlationId = request.headers['x-correlation-id'] as string;

    if (!serviceToken) {
      this.logger.warn({
        message: 'Missing service token',
        correlationId,
        ip: request.ip,
      });
      throw new UnauthorizedException('Missing service token');
    }

    try {
      // Validate service token
      if (!this.isValidServiceToken(serviceToken)) {
        this.logger.warn({
          message: 'Invalid service token',
          correlationId,
          ip: request.ip,
        });
        throw new UnauthorizedException('Invalid service token');
      }

      // For impersonation requests, validate the session
      const body = request.body as any;
      if (body?.params?.context?.sessionInfo) {
        const { accessToken, sessionHandle } = body.params.context.sessionInfo;
        
        // Verify the session is valid and belongs to the claimed user
        const sessionValid = await this.authService.verifySession(
          accessToken,
          sessionHandle,
        );

        if (!sessionValid) {
          this.logger.warn({
            message: 'Invalid session for impersonation',
            correlationId,
            userId: body.params.context.userId,
          });
          throw new UnauthorizedException('Invalid session for impersonation');
        }

        // Verify the service has permission to impersonate
        if (!this.canImpersonateUser(serviceToken)) {
          this.logger.warn({
            message: 'Service not authorized for impersonation',
            correlationId,
            serviceToken: this.maskToken(serviceToken),
          });
          throw new UnauthorizedException('Service not authorized for impersonation');
        }
      }

      // Attach service info to request for downstream use
      request['mcpService'] = {
        token: serviceToken,
        canImpersonate: this.canImpersonateUser(serviceToken),
      };

      this.logger.debug({
        message: 'MCP authentication successful',
        correlationId,
        hasImpersonation: !!body?.params?.context?.sessionInfo,
      });

      return true;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }

      this.logger.error({
        message: 'MCP authentication error',
        correlationId,
        error: error.message,
      });

      throw new UnauthorizedException('Authentication failed');
    }
  }

  private extractServiceToken(request: Request): string | null {
    // Check header first
    const headerToken = request.headers['x-service-token'] as string;
    if (headerToken) {
      return headerToken;
    }

    // Check Authorization header
    const authHeader = request.headers.authorization;
    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    return null;
  }

  private isValidServiceToken(token: string): boolean {
    return this.validServiceTokens.has(token);
  }

  private canImpersonateUser(serviceToken: string): boolean {
    // In production, implement more sophisticated permission checking
    // For now, all valid service tokens can impersonate
    return this.validServiceTokens.has(serviceToken);
  }

  private maskToken(token: string): string {
    if (token.length <= 8) {
      return '***';
    }
    return `${token.substring(0, 4)}...${token.substring(token.length - 4)}`;
  }
}