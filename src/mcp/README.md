# MCP (Model Context Protocol) Module

This module implements the MCP server for the NeduAI Job Service, enabling AI agents to interact with job service functionality through a standardized protocol.

## Architecture

### Core Components

1. **MCPController** (`controllers/mcp.controller.ts`)
   - Main HTTP endpoint at `/mcp`
   - Handles MCP protocol requests
   - Supports single and batch requests
   - Implements request/response streaming (future)

2. **MCPService** (`services/mcp.service.ts`)
   - Core request handler
   - Routes requests to appropriate handlers
   - Manages protocol-level operations

3. **ToolRegistryService** (`services/tool-registry.service.ts`)
   - Manages tool registration and discovery
   - Provides tool metadata
   - Handles tool categorization

4. **ContextService** (`services/context.service.ts`)
   - Validates user context
   - Handles impersonation logic
   - Ensures proper authorization

5. **AuditService** (`services/audit.service.ts`)
   - Comprehensive logging of all operations
   - User action tracking integration
   - Security event logging

### Security

1. **MCPAuthGuard** (`guards/mcp-auth.guard.ts`)
   - Service token validation
   - Session verification for impersonation
   - Rate limiting (configurable per tool)

### Tools

Tools are located in the `tools/` directory. Each tool implements:
- Name and description
- Input schema (using Zod)
- Handler function
- Optional auth requirements

Example tool structure:
```typescript
export const myTool: MCPTool = {
  name: 'tool_name',
  description: 'Clear description of what the tool does',
  inputSchema: z.object({
    context: MCPContextSchema,
    // tool-specific parameters
  }),
  requiresAuth: true,
  handler: async (args, context) => {
    // Implementation
  }
};
```

## Configuration

Required environment variables:
```bash
# Comma-separated list of valid service tokens
MCP_SERVICE_TOKENS=token1,token2

# Other configuration
NODE_ENV=production
```

## Testing

Test the MCP endpoint:
```bash
# Health check
curl -X POST http://localhost:3051/mcp \
  -H "Content-Type: application/json" \
  -H "x-service-token: your-token" \
  -d '{
    "method": "tools/call",
    "params": {
      "name": "health_check",
      "arguments": {
        "context": {
          "userId": "test-user",
          "requestId": "req-123",
          "source": "dify_agent",
          "reason": "Testing MCP endpoint"
        },
        "includeDetails": true
      }
    },
    "id": 1
  }'
```

## Adding New Tools

1. Create a new file in `tools/` directory
2. Define the tool following the MCPTool interface
3. Export it in `tools/index.ts`
4. The tool will be automatically registered on module initialization

## Security Considerations

1. All requests require valid service tokens
2. User impersonation requires valid session tokens
3. All operations are logged for audit purposes
4. Rate limiting can be configured per tool
5. Input validation is enforced through Zod schemas