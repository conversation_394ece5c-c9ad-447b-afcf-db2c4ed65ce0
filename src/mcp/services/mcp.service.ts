import { Injectable, Logger } from '@nestjs/common';
import { MCPRequest, MCPErrorCodes } from '../interfaces/mcp.interface';
import { ToolRegistryService } from './tool-registry.service';
import { ContextService } from './context.service';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { ListToolsRequestSchema, CallToolRequestSchema } from '@modelcontextprotocol/sdk/types.js';

@Injectable()
export class MCPService {
  private readonly logger = new Logger(MCPService.name);
  private server: Server;

  constructor(
    private readonly toolRegistry: ToolRegistryService,
    private readonly contextService: ContextService,
  ) {
    this.initializeMCPServer();
  }

  private initializeMCPServer() {
    this.server = new Server(
      {
        name: 'neduai-job-service',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      },
    );

    // Set up tool listing handler
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      const tools = this.toolRegistry.getAllTools();
      return {
        tools: tools.map(tool => ({
          name: tool.name,
          description: tool.description,
          inputSchema: tool.inputSchema,
        })),
      };
    });

    // Set up tool execution handler
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;
      
      // Extract context from arguments
      const context = await this.contextService.validateContext(args?.context);
      
      // Execute the tool
      const tool = this.toolRegistry.getTool(name);
      if (!tool) {
        throw new Error(`Tool ${name} not found`);
      }

      const result = await tool.handler(args, context);
      return { content: [{ type: 'text', text: JSON.stringify(result) }] };
    });
  }

  async handleRequest(
    request: MCPRequest,
    requestId: string,
    serviceToken: string,
  ): Promise<any> {
    this.logger.debug({
      message: 'Processing MCP request',
      method: request.method,
      requestId,
    });

    try {
      switch (request.method) {
        case 'initialize':
          return this.handleInitialize(request.params);
        
        case 'tools/list':
          return this.handleListTools();
        
        case 'tools/call':
          return this.handleCallTool(request.params, requestId);
        
        case 'ping':
          return { pong: true, timestamp: new Date().toISOString() };
        
        default:
          throw {
            code: MCPErrorCodes.METHOD_NOT_FOUND,
            message: `Method ${request.method} not found`,
          };
      }
    } catch (error) {
      this.logger.error({
        message: 'MCP request error',
        method: request.method,
        requestId,
        error: error.message,
        stack: error.stack,
      });

      // Re-throw with proper error structure
      if (error.code) {
        throw error;
      }

      throw {
        code: MCPErrorCodes.INTERNAL_ERROR,
        message: error.message || 'Internal server error',
        data: { requestId },
      };
    }
  }

  private handleInitialize(params: any) {
    return {
      protocolVersion: '2024-11-05',
      capabilities: {
        tools: {},
      },
      serverInfo: {
        name: 'neduai-job-service',
        version: '1.0.0',
      },
    };
  }

  private handleListTools() {
    const tools = this.toolRegistry.getAllTools();
    
    return {
      tools: tools.map(tool => ({
        name: tool.name,
        description: tool.description,
        inputSchema: this.schemaToJson(tool.inputSchema),
      })),
    };
  }

  private async handleCallTool(params: any, requestId: string) {
    const { name, arguments: args } = params;

    if (!name) {
      throw {
        code: MCPErrorCodes.INVALID_PARAMS,
        message: 'Tool name is required',
      };
    }

    const tool = this.toolRegistry.getTool(name);
    if (!tool) {
      throw {
        code: MCPErrorCodes.METHOD_NOT_FOUND,
        message: `Tool ${name} not found`,
      };
    }

    try {
      // Validate context
      const context = await this.contextService.validateContext(args.context);
      
      // Add requestId to context
      context.requestId = requestId;

      // Validate tool input
      const validatedArgs = tool.inputSchema.parse(args);

      // Execute tool handler
      const result = await tool.handler(validatedArgs, context);

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify(result, null, 2),
          },
        ],
      };
    } catch (error) {
      if (error.name === 'ZodError') {
        throw {
          code: MCPErrorCodes.INVALID_PARAMS,
          message: 'Invalid tool parameters',
          data: error.errors,
        };
      }

      throw error;
    }
  }

  private schemaToJson(schema: any): any {
    // Convert Zod schema to JSON Schema
    // This is a simplified version - in production, use a proper converter
    if (schema._def) {
      return {
        type: 'object',
        properties: schema._def.shape() || {},
        required: Object.keys(schema._def.shape() || {}),
      };
    }
    return schema;
  }
}