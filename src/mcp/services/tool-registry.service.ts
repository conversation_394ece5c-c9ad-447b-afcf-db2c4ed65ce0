import { Injectable, Logger } from '@nestjs/common';
import { MCPTool } from '../interfaces/mcp.interface';

@Injectable()
export class ToolRegistryService {
  private readonly logger = new Logger(ToolRegistryService.name);
  private readonly tools = new Map<string, MCPTool>();

  constructor() {
    this.logger.log('Initializing MCP Tool Registry');
  }

  registerTool(tool: MCPTool): void {
    if (this.tools.has(tool.name)) {
      this.logger.warn(`Tool ${tool.name} is already registered, overwriting`);
    }

    this.tools.set(tool.name, tool);
    
    this.logger.log({
      message: 'Tool registered',
      name: tool.name,
      description: tool.description,
      requiresAuth: tool.requiresAuth,
    });
  }

  registerTools(tools: MCPTool[]): void {
    tools.forEach(tool => this.registerTool(tool));
  }

  getTool(name: string): MCPTool | undefined {
    return this.tools.get(name);
  }

  getAllTools(): MCPTool[] {
    return Array.from(this.tools.values());
  }

  getToolsByCategory(category: string): MCPTool[] {
    // This can be extended to support categorization
    return this.getAllTools();
  }

  hasRequiredServices(): boolean {
    // Check if all required services are available for tools
    // This can be extended based on tool dependencies
    return true;
  }

  getToolMetrics(): Record<string, any> {
    return {
      totalTools: this.tools.size,
      toolNames: Array.from(this.tools.keys()),
      requiresAuth: Array.from(this.tools.values()).filter(t => t.requiresAuth).length,
    };
  }
}