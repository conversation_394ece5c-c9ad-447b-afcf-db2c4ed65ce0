import { Injectable, Logger, ForbiddenException } from '@nestjs/common';
import { MCPContext, MCPContextSchema } from '../interfaces/mcp.interface';
import { z } from 'zod';
import { AuthService } from '../../auth/services/auth.service';

@Injectable()
export class ContextService {
  private readonly logger = new Logger(ContextService.name);

  constructor(private readonly authService: AuthService) {}

  async validateContext(rawContext: any): Promise<MCPContext> {
    try {
      // Validate the context structure
      const context = MCPContextSchema.parse(rawContext);

      // If session info is provided, validate it
      if (context.sessionInfo) {
        const sessionValid = await this.authService.verifySession(
          context.sessionInfo.accessToken,
          context.sessionInfo.sessionHandle,
        );

        if (!sessionValid) {
          throw new ForbiddenException('Invalid session for user context');
        }

        // Verify the session belongs to the claimed user
        const sessionUserId = await this.authService.getUserIdFromSession(
          context.sessionInfo.accessToken,
        );

        if (!sessionUserId || sessionUserId !== context.userId) {
          this.logger.warn({
            message: 'User ID mismatch in context',
            claimedUserId: context.userId,
            sessionUserId,
            requestId: context.requestId,
          });
          throw new ForbiddenException('User ID does not match session');
        }
      }

      return context;
    } catch (error) {
      if (error instanceof z.ZodError) {
        const issues = error.errors.map(e => `${e.path.join('.')}: ${e.message}`);
        throw new ForbiddenException(`Invalid context: ${issues.join(', ')}`);
      }

      if (error instanceof ForbiddenException) {
        throw error;
      }

      this.logger.error({
        message: 'Context validation error',
        error: error.message,
        stack: error.stack,
      });

      throw new ForbiddenException('Context validation failed');
    }
  }

  getUserIdFromContext(context: MCPContext): string {
    return context.userId;
  }

  getRequestIdFromContext(context: MCPContext): string {
    return context.requestId;
  }

  isSystemContext(context: MCPContext): boolean {
    // Check if this is a system-level operation (no user impersonation)
    return !context.sessionInfo && this.isSystemSource(context.source);
  }

  private isSystemSource(source: string): boolean {
    return ['admin_portal', 'user_service'].includes(source);
  }

  createAuditMetadata(context: MCPContext): Record<string, any> {
    return {
      userId: context.userId,
      requestId: context.requestId,
      source: context.source,
      reason: context.reason,
      timestamp: new Date().toISOString(),
      isImpersonation: !!context.sessionInfo,
    };
  }
}