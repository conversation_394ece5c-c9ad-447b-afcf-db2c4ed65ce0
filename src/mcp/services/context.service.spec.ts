import { Test, TestingModule } from '@nestjs/testing';
import { ContextService } from './context.service';
import { AuthService } from '../../auth/services/auth.service';
import { ForbiddenException } from '@nestjs/common';
import { MCPContext } from '../interfaces/mcp.interface';

describe('ContextService', () => {
  let service: ContextService;
  let authService: jest.Mocked<AuthService>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ContextService,
        {
          provide: AuthService,
          useValue: {
            verifySession: jest.fn(),
            getUserIdFromSession: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ContextService>(ContextService);
    authService = module.get(AuthService);
  });

  describe('validateContext', () => {
    it('should validate a valid context without session', async () => {
      const rawContext = {
        userId: 'user123',
        requestId: 'req123',
        source: 'dify_agent',
        reason: 'Testing',
      };

      const result = await service.validateContext(rawContext);

      expect(result).toEqual(rawContext);
      expect(authService.verifySession).not.toHaveBeenCalled();
    });

    it('should validate a valid context with session', async () => {
      const rawContext = {
        userId: 'user123',
        requestId: 'req123',
        source: 'dify_agent',
        reason: 'Testing',
        sessionInfo: {
          accessToken: 'token123',
          sessionHandle: 'session123',
        },
      };

      authService.verifySession.mockResolvedValue(true);
      authService.getUserIdFromSession.mockResolvedValue('user123');

      const result = await service.validateContext(rawContext);

      expect(result).toEqual(rawContext);
      expect(authService.verifySession).toHaveBeenCalledWith('token123', 'session123');
      expect(authService.getUserIdFromSession).toHaveBeenCalledWith('token123');
    });

    it('should throw error for invalid session', async () => {
      const rawContext = {
        userId: 'user123',
        requestId: 'req123',
        source: 'dify_agent',
        reason: 'Testing',
        sessionInfo: {
          accessToken: 'invalid',
          sessionHandle: 'invalid',
        },
      };

      authService.verifySession.mockResolvedValue(false);

      await expect(service.validateContext(rawContext)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error for user ID mismatch', async () => {
      const rawContext = {
        userId: 'user123',
        requestId: 'req123',
        source: 'dify_agent',
        reason: 'Testing',
        sessionInfo: {
          accessToken: 'token123',
          sessionHandle: 'session123',
        },
      };

      authService.verifySession.mockResolvedValue(true);
      authService.getUserIdFromSession.mockResolvedValue('different-user');

      await expect(service.validateContext(rawContext)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error for invalid context structure', async () => {
      const rawContext = {
        // Missing required fields
        source: 'dify_agent',
      };

      await expect(service.validateContext(rawContext)).rejects.toThrow(
        ForbiddenException,
      );
    });
  });

  describe('isSystemContext', () => {
    it('should identify system context', () => {
      const context: MCPContext = {
        userId: 'system',
        requestId: 'req123',
        source: 'admin_portal',
        reason: 'System operation',
      };

      expect(service.isSystemContext(context)).toBe(true);
    });

    it('should identify non-system context', () => {
      const context: MCPContext = {
        userId: 'user123',
        requestId: 'req123',
        source: 'dify_agent',
        reason: 'User operation',
        sessionInfo: {
          accessToken: 'token',
          sessionHandle: 'session',
        },
      };

      expect(service.isSystemContext(context)).toBe(false);
    });
  });

  describe('createAuditMetadata', () => {
    it('should create audit metadata', () => {
      const context: MCPContext = {
        userId: 'user123',
        requestId: 'req123',
        source: 'dify_agent',
        reason: 'Testing',
        sessionInfo: {
          accessToken: 'token',
          sessionHandle: 'session',
        },
      };

      const metadata = service.createAuditMetadata(context);

      expect(metadata).toMatchObject({
        userId: 'user123',
        requestId: 'req123',
        source: 'dify_agent',
        reason: 'Testing',
        isImpersonation: true,
      });
      expect(metadata.timestamp).toBeDefined();
    });
  });
});