import { Test, TestingModule } from '@nestjs/testing';
import { ToolRegistryService } from './tool-registry.service';
import { MCPTool } from '../interfaces/mcp.interface';
import { z } from 'zod';

describe('ToolRegistryService', () => {
  let service: ToolRegistryService;

  const mockTool: MCPTool = {
    name: 'test_tool',
    description: 'A test tool',
    inputSchema: z.object({
      input: z.string(),
    }),
    handler: async (args, context) => {
      return { result: 'test' };
    },
    requiresAuth: true,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ToolRegistryService],
    }).compile();

    service = module.get<ToolRegistryService>(ToolRegistryService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('registerTool', () => {
    it('should register a tool', () => {
      service.registerTool(mockTool);
      
      const tool = service.getTool('test_tool');
      expect(tool).toEqual(mockTool);
    });

    it('should overwrite existing tool with same name', () => {
      service.registerTool(mockTool);
      
      const updatedTool = { ...mockTool, description: 'Updated description' };
      service.registerTool(updatedTool);
      
      const tool = service.getTool('test_tool');
      expect(tool?.description).toBe('Updated description');
    });
  });

  describe('registerTools', () => {
    it('should register multiple tools', () => {
      const tools = [
        mockTool,
        { ...mockTool, name: 'test_tool_2' },
        { ...mockTool, name: 'test_tool_3' },
      ];

      service.registerTools(tools);
      
      expect(service.getAllTools()).toHaveLength(3);
    });
  });

  describe('getTool', () => {
    it('should return registered tool', () => {
      service.registerTool(mockTool);
      
      const tool = service.getTool('test_tool');
      expect(tool).toEqual(mockTool);
    });

    it('should return undefined for non-existent tool', () => {
      const tool = service.getTool('non_existent');
      expect(tool).toBeUndefined();
    });
  });

  describe('getAllTools', () => {
    it('should return all registered tools', () => {
      const tools = [
        mockTool,
        { ...mockTool, name: 'test_tool_2' },
      ];

      service.registerTools(tools);
      
      const allTools = service.getAllTools();
      expect(allTools).toHaveLength(2);
      expect(allTools.map(t => t.name)).toEqual(['test_tool', 'test_tool_2']);
    });

    it('should return empty array when no tools registered', () => {
      const allTools = service.getAllTools();
      expect(allTools).toEqual([]);
    });
  });

  describe('getToolMetrics', () => {
    it('should return tool metrics', () => {
      const tools = [
        mockTool,
        { ...mockTool, name: 'test_tool_2', requiresAuth: false },
        { ...mockTool, name: 'test_tool_3' },
      ];

      service.registerTools(tools);
      
      const metrics = service.getToolMetrics();
      
      expect(metrics).toEqual({
        totalTools: 3,
        toolNames: ['test_tool', 'test_tool_2', 'test_tool_3'],
        requiresAuth: 2,
      });
    });
  });
});