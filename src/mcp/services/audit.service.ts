import { Injectable, Logger } from '@nestjs/common';
import { UserActionTrackerService } from '../../app/user-actions/services/user-action-tracker.service';
import { MCPContext } from '../interfaces/mcp.interface';

interface AuditLogEntry {
  requestId: string;
  method: string;
  params?: any;
  response?: any;
  error?: string;
  duration: number;
  serviceToken: string;
  userId?: string;
  timestamp?: Date;
}

@Injectable()
export class AuditService {
  private readonly logger = new Logger(AuditService.name);

  constructor(
    private readonly userActionTracker: UserActionTrackerService,
  ) {}

  async logRequest(entry: AuditLogEntry): Promise<void> {
    try {
      // Log to application logger
      this.logger.log({
        message: 'MCP request audit',
        ...entry,
        timestamp: new Date().toISOString(),
      });

      // Track in user actions if userId is present
      if (entry.params?.context?.userId) {
        await this.trackUserAction(
          entry.params.context.userId,
          'mcp_tool_call',
          {
            tool: entry.method,
            requestId: entry.requestId,
            duration: entry.duration,
            success: true,
          },
        );
      }
    } catch (error) {
      this.logger.error({
        message: 'Failed to log audit entry',
        error: error.message,
        requestId: entry.requestId,
      });
    }
  }

  async logError(entry: Omit<AuditLogEntry, 'response'>): Promise<void> {
    try {
      // Log to application logger
      this.logger.error({
        message: 'MCP request error audit',
        ...entry,
        timestamp: new Date().toISOString(),
      });

      // Track error in user actions if userId is present
      if (entry.params?.context?.userId) {
        await this.trackUserAction(
          entry.params.context.userId,
          'mcp_tool_error',
          {
            tool: entry.method,
            requestId: entry.requestId,
            error: entry.error,
            duration: entry.duration,
          },
        );
      }
    } catch (error) {
      this.logger.error({
        message: 'Failed to log error audit entry',
        error: error.message,
        requestId: entry.requestId,
      });
    }
  }

  async logToolExecution(
    context: MCPContext,
    toolName: string,
    input: any,
    output: any,
    duration: number,
  ): Promise<void> {
    try {
      const auditData = {
        requestId: context.requestId,
        userId: context.userId,
        source: context.source,
        reason: context.reason,
        tool: toolName,
        duration,
        timestamp: new Date().toISOString(),
        isImpersonation: !!context.sessionInfo,
      };

      // Log detailed execution
      this.logger.log({
        message: 'MCP tool execution',
        ...auditData,
        inputSize: JSON.stringify(input).length,
        outputSize: JSON.stringify(output).length,
      });

      // Track in user actions
      await this.trackUserAction(
        context.userId,
        'mcp_tool_execution',
        {
          tool: toolName,
          requestId: context.requestId,
          source: context.source,
          duration,
          success: true,
        },
      );
    } catch (error) {
      this.logger.error({
        message: 'Failed to log tool execution',
        error: error.message,
        context,
        toolName,
      });
    }
  }

  async logSecurityEvent(
    event: 'auth_failed' | 'invalid_token' | 'rate_limit' | 'suspicious_activity',
    details: Record<string, any>,
  ): Promise<void> {
    this.logger.warn({
      message: 'MCP security event',
      event,
      ...details,
      timestamp: new Date().toISOString(),
    });
  }

  private async trackUserAction(
    userId: string,
    action: string,
    metadata: Record<string, any>,
  ): Promise<void> {
    try {
      // For now, we'll log the action until we implement a generic trackAction method
      this.logger.log({
        message: 'MCP user action',
        userId,
        action,
        category: 'mcp',
        metadata,
      });
    } catch (error) {
      this.logger.error({
        message: 'Failed to track user action',
        error: error.message,
        userId,
        action,
      });
    }
  }
}