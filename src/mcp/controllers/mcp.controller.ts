import {
  Controller,
  Post,
  Body,
  UseGuards,
  Headers,
  HttpCode,
  HttpStatus,
  <PERSON>gger,
  Req,
  <PERSON><PERSON>,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { Request, Response } from 'express';
import { MCPAuthGuard } from '../guards/mcp-auth.guard';
import { MCPService } from '../services/mcp.service';
import { MCPRequest, MCPResponse } from '../interfaces/mcp.interface';
import { AuditService } from '../services/audit.service';

@ApiTags('MCP')
@Controller('mcp')
@UseGuards(MCPAuthGuard)
export class MCPController {
  private readonly logger = new Logger(MCPController.name);

  constructor(
    private readonly mcpService: MCPService,
    private readonly auditService: AuditService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Handle MCP protocol requests' })
  @ApiBody({ 
    description: 'MCP Request',
    schema: {
      type: 'object',
      properties: {
        method: { type: 'string' },
        params: { type: 'object' },
        id: { oneOf: [{ type: 'string' }, { type: 'number' }] },
      },
      required: ['method'],
    },
  })
  @ApiResponse({ status: 200, description: 'MCP Response' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 429, description: 'Rate limit exceeded' })
  @ApiResponse({ status: 500, description: 'Internal server error' })
  async handleMCPRequest(
    @Body() request: MCPRequest | MCPRequest[],
    @Headers('x-correlation-id') correlationId: string,
    @Headers('x-service-token') serviceToken: string,
    @Req() req: Request,
    @Res() res: Response,
  ): Promise<void> {
    const requestId = correlationId || this.generateRequestId();
    const startTime = Date.now();

    try {
      this.logger.log({
        message: 'MCP request received',
        requestId,
        method: Array.isArray(request) ? 'batch' : request.method,
        isBatch: Array.isArray(request),
      });

      // For streaming support in the future
      const acceptsStream = req.headers.accept?.includes('text/event-stream');
      
      // Handle batch requests
      if (Array.isArray(request)) {
        const responses = await Promise.all(
          request.map(r => this.processSingleRequest(r, requestId, serviceToken))
        );
        res.json(responses);
        return;
      }

      // Handle single request
      const response = await this.processSingleRequest(request, requestId, serviceToken);
      
      // Audit successful request
      await this.auditService.logRequest({
        requestId,
        method: request.method,
        params: request.params,
        response: response.result,
        duration: Date.now() - startTime,
        serviceToken,
      });

      res.json(response);
    } catch (error) {
      this.logger.error({
        message: 'MCP request failed',
        requestId,
        error: error.message,
        stack: error.stack,
      });

      // Audit failed request
      await this.auditService.logError({
        requestId,
        method: Array.isArray(request) ? 'batch' : request.method,
        error: error.message,
        duration: Date.now() - startTime,
        serviceToken,
      });

      const errorResponse: MCPResponse = {
        error: {
          code: -32603,
          message: 'Internal server error',
          data: { requestId },
        },
        id: Array.isArray(request) ? undefined : request.id,
      };

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json(errorResponse);
    }
  }

  private async processSingleRequest(
    request: MCPRequest,
    requestId: string,
    serviceToken: string,
  ): Promise<MCPResponse> {
    try {
      const result = await this.mcpService.handleRequest(request, requestId, serviceToken);
      return {
        result,
        id: request.id,
      };
    } catch (error) {
      return {
        error: {
          code: error.code || -32603,
          message: error.message || 'Internal error',
          data: error.data,
        },
        id: request.id,
      };
    }
  }

  private generateRequestId(): string {
    return `mcp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}