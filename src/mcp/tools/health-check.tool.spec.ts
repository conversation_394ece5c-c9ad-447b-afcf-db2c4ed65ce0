import { healthCheckTool } from './health-check.tool';
import { MCPContext } from '../interfaces/mcp.interface';

describe('HealthCheckTool', () => {
  const mockContext: MCPContext = {
    userId: 'test-user',
    requestId: 'test-request-123',
    source: 'dify_agent',
    reason: 'Testing health check tool',
  };

  it('should return basic health status', async () => {
    const result = await healthCheckTool.handler(
      {
        context: mockContext,
        includeDetails: false,
      },
      mockContext,
    );

    expect(result).toMatchObject({
      status: 'healthy',
      service: 'neduai-job-service-mcp',
      requestId: 'test-request-123',
    });
    expect(result.timestamp).toBeDefined();
  });

  it('should return detailed health status when includeDetails is true', async () => {
    const result = await healthCheckTool.handler(
      {
        context: mockContext,
        includeDetails: true,
      },
      mockContext,
    );

    expect(result).toMatchObject({
      status: 'healthy',
      service: 'neduai-job-service-mcp',
      requestId: 'test-request-123',
      details: {
        version: '1.0.0',
        userId: 'test-user',
        source: 'dify_agent',
        environment: expect.any(String),
      },
    });
    expect(result.timestamp).toBeDefined();
  });

  it('should have correct metadata', () => {
    expect(healthCheckTool.name).toBe('health_check');
    expect(healthCheckTool.description).toBe('Check the health status of the MCP service');
    expect(healthCheckTool.requiresAuth).toBe(false);
  });

  it('should validate input schema', () => {
    const validInput = {
      context: mockContext,
      includeDetails: true,
    };

    const result = healthCheckTool.inputSchema.safeParse(validInput);
    expect(result.success).toBe(true);
  });

  it('should provide default value for includeDetails', () => {
    const input = {
      context: mockContext,
    };

    const result = healthCheckTool.inputSchema.parse(input);
    expect(result.includeDetails).toBe(false);
  });
});