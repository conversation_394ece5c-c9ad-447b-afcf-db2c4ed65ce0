import { z } from 'zod';
import { MCPTool, MCPContext } from '../interfaces/mcp.interface';

export const HealthCheckInputSchema = z.object({
  context: z.any(),
  includeDetails: z.boolean().default(false),
});

export type HealthCheckInput = z.infer<typeof HealthCheckInputSchema>;

export const healthCheckTool: MCPTool<HealthCheckInput, any> = {
  name: 'health_check',
  description: 'Check the health status of the MCP service',
  inputSchema: HealthCheckInputSchema,
  requiresAuth: false,
  handler: async (args: HealthCheckInput, context: MCPContext) => {
    const response = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      service: 'neduai-job-service-mcp',
      requestId: context.requestId,
    };

    if (args.includeDetails) {
      return {
        ...response,
        details: {
          version: '1.0.0',
          userId: context.userId,
          source: context.source,
          environment: process.env.NODE_ENV || 'development',
        },
      };
    }

    return response;
  },
};