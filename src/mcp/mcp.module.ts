import { <PERSON><PERSON><PERSON>, OnModuleInit } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { MCPController } from './controllers/mcp.controller';
import { MCPService } from './services/mcp.service';
import { ContextService } from './services/context.service';
import { ToolRegistryService } from './services/tool-registry.service';
import { AuditService } from './services/audit.service';
import { AuthModule } from '../auth/auth.module';
import { UserActionsModule } from '../app/user-actions/user-actions.module';
import { CacheModule } from '../cache/cache.module';
import { mcpTools } from './tools';

@Module({
  imports: [
    ConfigModule,
    AuthModule,
    UserActionsModule,
    CacheModule,
  ],
  controllers: [MCPController],
  providers: [
    MCPService,
    ContextService,
    ToolRegistryService,
    AuditService,
  ],
  exports: [MCPService, ToolRegistryService],
})
export class MCPModule implements OnModuleInit {
  constructor(private readonly toolRegistry: ToolRegistryService) {}

  onModuleInit() {
    // Register all MCP tools on module initialization
    this.toolRegistry.registerTools(mcpTools);
  }
}