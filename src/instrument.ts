// Import with `const Sentry = require("@sentry/nestjs");` if you are using CJS
import * as Sentry from '@sentry/nestjs';
import { nodeProfilingIntegration } from '@sentry/profiling-node';
import { readFileSync } from 'fs';
import { join } from 'path';

// Try to get package version for release tracking
let version = '0.0.1';
try {
  const packageJson = JSON.parse(
    readFileSync(join(__dirname, '..', '..', 'package.json'), 'utf8')
  );
  version = packageJson.version;
} catch (e) {
  console.error('Could not read package.json for version information');
}

// Get environment from NODE_ENV, defaulting to development
const environment = process.env.NODE_ENV || 'development';

// Disable Sentry initialization - removed due to issues with translation interceptor
console.log('Sen<PERSON> disabled - removed due to issues with translation interceptor');

// Keep a no-op implementation to avoid breaking code that expects <PERSON><PERSON> to be available
const disabledSentry = {
  captureException: () => {},
  captureMessage: () => {},
  captureEvent: () => {},
  startTransaction: () => ({ finish: () => {} }),
  configureScope: () => {},
};

// Export the disabled version so imports don't break
export { disabledSentry as Sentry };