import { HttpAdapterHost, NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { AppModule } from './app.module';
import { setupSwagger } from './swagger.config';
import { GlobalExceptionFilter } from './filters/global-exception.filter';
import { DateSerializationInterceptor } from './app/common/interceptors/date-serialization.interceptor';
import { EnhancedLoggingInterceptor } from './logging/enhanced-logging.interceptor';
import { GcpLoggerService } from './logging/gcp-logger.service';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Configure CORS
  app.enableCors({
    origin: [
      /^(([a-z0-9-]+\.)+)?nedu\.solutions$/,
      /^(([a-z0-9-]+\.)+)?nedu\.ai$/,
      'https://app.nedu.solutions',
      process.env.WEBSITE_DOMAIN ?? '*',
    ],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Content-Type',
      'Authorization',
      'fdi-version',
      'st-auth-mode',
      'rid',
    ],
  });

  // Validation pipe for DTOs
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: false,
      forbidNonWhitelisted: false,
    }),
  );

  // Register global interceptors
  const logger = app.get(GcpLoggerService);
  app.useGlobalInterceptors(
    new EnhancedLoggingInterceptor(logger),
    new DateSerializationInterceptor(),
  );

  // Register global exception filter
  const httpAdapterHost = app.get(HttpAdapterHost);
  app.useGlobalFilters(new GlobalExceptionFilter(httpAdapterHost, logger));

  // Setup Swagger using our configuration
  setupSwagger(app);

  await app.listen(process.env.PORT ?? 3000);
}

bootstrap();
