import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

import { DataSource, DataSourceOptions } from 'typeorm';

import { EducationModule } from './app/education/education.module';
import { CacheModule } from './cache/cache.module';
import { JobsModule } from './app/jobs/jobs.module';
import { AuthModule } from './auth/auth.module';
import { EmployerModule } from './app/employer/employer.module';
import { MunicipalityModule } from './app/municipality/municipality.module';
import { RegionModule } from './app/region/region.module';
import { OccupationModule } from './app/occupation/occupation.module';
import { PineconeModule } from './pinecone/pinecone.module';
import { UserModule } from './app/user/user.module';
import { ReactAppModule } from './app/react-app/react-app.module';
import { OpenAIModule } from './openai/openai.module';
import { ReactSmartProfileModule } from './app/react-smart-profile/react-smart-profile.module';
import { LocationModule } from './app/location/location.module';
import { CvWorkspaceModule } from './app/cv-workspace/cv-workspace.module';
import { GeminiModule } from './gemini/gemini.module';
import { CoreModule } from './app/core/core.module';
import { UserActionsModule } from './app/user-actions/user-actions.module';
import { LoggingModule } from './logging/logging.module';
import { MCPModule } from './mcp/mcp.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    ScheduleModule.forRoot(),
    LoggingModule,
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        type: 'postgres',
        url: configService.get('DATABASE_URL'),
        schema: configService.get('DATABASE_SCHEMA'),
        entities: [__dirname + '/**/*.entity{.ts,.js}'],
        synchronize: false, // Be careful with this in production
        logging: false, //process.env.NODE_ENV !== 'production',
      }),
      dataSourceFactory: async (options: DataSourceOptions) => {
        const schema: string = options['schema'];
        const dataSource = await new DataSource(options).initialize();

        if (schema)
          await dataSource.query(`CREATE SCHEMA IF NOT EXISTS "${schema}"`);
        await dataSource.synchronize();

        return dataSource;
      },
    }),
    EducationModule,
    CacheModule,
    CoreModule,
    JobsModule,
    EmployerModule,
    MunicipalityModule,
    RegionModule,
    OccupationModule,
    PineconeModule,
    OpenAIModule,
    GeminiModule,
    UserModule,
    ReactAppModule,
    ReactSmartProfileModule,
    LocationModule,
    CvWorkspaceModule,
    UserActionsModule,
    MCPModule,
    AuthModule.forRoot({
      connectionURI:
        process.env.SUPERTOKENS_CONNECTION_URI || 'http://localhost:3567',
      apiKey: process.env.SUPERTOKENS_API_KEY,
      appInfo: {
        appName: 'NeduAI Job Service',
        apiDomain: process.env.API_DOMAIN || 'http://localhost:3000',
        websiteDomain: process.env.WEBSITE_DOMAIN || 'http://localhost:3001',
        apiBasePath: '/auth',
        websiteBasePath: '/auth',
      },
    }),
  ],
})
export class AppModule {}
