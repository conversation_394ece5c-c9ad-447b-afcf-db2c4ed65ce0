import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';

import * as fs from 'fs';
import * as path from 'path';

export function setupSwagger(app: INestApplication) {
  const config = new DocumentBuilder()
    .setTitle('NEDU Job Service API')
    .setDescription('API documentation for the NEDU Job Service')
    .setVersion('1.0')
    .addTag(
      'Education Industries',
      'Endpoints related to education and industry recommendations',
    )
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Write swagger json to file
  const outputPath = path.resolve(
    process.cwd(),
    'job-service-swagger-spec.json',
  );
  fs.writeFileSync(outputPath, JSON.stringify(document, null, 2), {
    encoding: 'utf8',
  });
  console.log(`Swagger JSON file written to: ${outputPath}`);

  SwaggerModule.setup('api', app, document);
}
