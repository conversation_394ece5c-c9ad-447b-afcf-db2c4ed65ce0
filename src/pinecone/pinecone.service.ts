import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Pinecone } from '@pinecone-database/pinecone';

@Injectable()
export class PineconeService implements OnModuleInit {
  private readonly logger = new Logger(PineconeService.name);
  private pineconeClient: Pinecone;

  readonly dimension = 1024;
  readonly indexName = 'jobs';
  readonly indexNameV2 = 'jobv2';

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    await this.initPinecone();
  }

  private async initPinecone() {
    try {
      const apiKey = this.configService.get<string>('PINECONE_API_KEY');
      const apiUrl = this.configService.get<string>('PINECONE_API_URL');

      if (!apiKey) {
        this.logger.error(
          'Pinecone API key is not defined in environment variables',
        );
        throw new Error('PINECONE_API_KEY is required');
      }

      // Initialize the Pinecone client
      // For MVP, we're using a simple configuration
      this.pineconeClient = new Pinecone({
        apiKey,
      });

      // Log Pinecone configuration
      this.logger.log('Pinecone client initialized successfully');
      if (apiUrl) {
        this.logger.log(`Using Pinecone API URL: ${apiUrl}`);
        // Note: For a production environment, we would use the Pinecone SDK's
        // proper configuration options for API URL instead of this MVP approach
      }

      // List indexes to verify connection
      const indexes = await this.pineconeClient.listIndexes();
      const indexNames = indexes.indexes?.map((idx) => idx.name) || [];
      this.logger.log(
        `Available Pinecone indexes: ${indexNames.join(', ') || 'No indexes found'}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to initialize Pinecone client: ${error.message}`,
      );
      // For MVP, we're allowing the application to continue even if Pinecone fails
      // This should be addressed in production with proper error handling
      // SECURITY CONCERN: Failing silently could lead to unexpected behavior
    }
  }

  /**
   * Get Pinecone client instance
   */
  public getPineconeClient(): Pinecone {
    if (!this.pineconeClient) {
      this.logger.warn(
        'Attempting to use Pinecone client before initialization',
      );
    }
    return this.pineconeClient;
  }

  /**
   * Get an index from Pinecone by name
   * @param indexName The name of the index to retrieve
   */
  async getIndex(indexName: string) {
    try {
      return this.pineconeClient.index(indexName);
    } catch (error) {
      this.logger.error(
        `Error getting Pinecone index ${indexName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Get the default index (jobs)
   */
  async getDefaultIndex() {
    return this.getIndex(this.indexName);
  }
  async getV2Index() {
    return this.getIndex(this.indexNameV2);
  }

  /**
   * Create a new index in Pinecone
   * @param indexName Name of the index to create
   * @param dimension Dimension of the vectors
   * @param metric Distance metric to use (cosine, dotproduct, euclidean)
   */
  async createIndex(
    indexName: string,
    dimension: number,
    metric: 'cosine' | 'dotproduct' | 'euclidean' = 'cosine',
  ) {
    try {
      const apiRegion =
        this.configService.get<string>('PINECONE_API_REGION') || 'us-west-2';

      await this.pineconeClient.createIndex({
        name: indexName,
        dimension,
        metric,
        spec: {
          serverless: {
            cloud: 'aws',
            region: apiRegion,
          },
        },
      });
      this.logger.log(`Created Pinecone index: ${indexName}`);
      return this.pineconeClient.index(indexName);
    } catch (error) {
      this.logger.error(
        `Error creating Pinecone index ${indexName}: ${error.message}`,
      );
      throw error;
    }
  }

  /**
   * Check if an index exists and create it if it doesn't
   * @param indexName Name of the index to check/create
   * @param dimension Dimension of the vectors (only used if creating)
   * @param metric Distance metric to use (only used if creating)
   */
  async ensureIndex(
    indexName: string,
    dimension: number,
    metric: 'cosine' | 'dotproduct' | 'euclidean' = 'cosine',
  ) {
    try {
      const indexes = await this.pineconeClient.listIndexes();
      const indexExists =
        indexes.indexes?.some((idx) => idx.name === indexName) || false;

      if (!indexExists) {
        this.logger.log(`Index ${indexName} does not exist, creating...`);
        return await this.createIndex(indexName, dimension, metric);
      }

      this.logger.log(`Index ${indexName} exists, using existing index`);
      return this.pineconeClient.index(indexName);
    } catch (error) {
      this.logger.error(
        `Error ensuring Pinecone index ${indexName}: ${error.message}`,
      );
      throw error;
    }
  }
}
