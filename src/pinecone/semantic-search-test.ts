import { Pinecone } from '@pinecone-database/pinecone';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

async function testSemanticSearch() {
  console.log('Starting Pinecone semantic search test...');

  try {
    const apiKey = process.env.PINECONE_API_KEY;
    const apiUrl = process.env.PINECONE_API_URL;

    if (!apiKey) {
      throw new Error(
        'PINECONE_API_KEY is not defined in environment variables',
      );
    }

    if (!apiUrl) {
      throw new Error(
        'PINECONE_API_URL is not defined in environment variables',
      );
    }

    console.log(`Using Pinecone configuration:
- API Key: ${apiKey ? '****' + apiKey.substring(apiKey.length - 4) : 'not set'}
- URL: ${apiUrl || 'not set'}`);

    // Initialize Pinecone client
    console.log('Initializing Pinecone client...');
    const pinecone = new Pinecone({ apiKey });

    // Use the existing "jobs" index
    const indexName = 'jobs';
    console.log(`Accessing existing index: ${indexName}...`);

    // Get the index instance
    const index = pinecone.index(indexName);

    // Get index stats
    const stats = await index.describeIndexStats();
    console.log(`Index stats: ${JSON.stringify(stats, null, 2)}`);

    // Insert a test job with summary field
    const testId = `test-job-${Date.now()}`;
    const dimension = 1024; // Match the index dimension

    // Create a random vector (in a real scenario, this would be an embedding from the summary)
    const testVector = Array(dimension)
      .fill(0)
      .map(() => Math.random() - 0.5);

    // Example job data with summary field
    const jobData = {
      id: testId,
      values: testVector,
      metadata: {
        title: 'Senior Software Engineer',
        company: 'ACME Tech',
        occupation: 'Software Developer',
        description:
          'Backend developer role with focus on Node.js and TypeScript',
        // This is the summary field that would be used for semantic search
        summary:
          'Senior Software Engineer ACME Tech Software Developer Backend developer role with focus on Node.js and TypeScript',
      },
    };

    // Upsert the test job
    console.log('Upserting test job with summary field...');
    await index.upsert([jobData]);
    console.log(`Successfully upserted test job with ID: ${testId}`);

    // Example search query
    const searchQuery = 'TypeScript developer';
    console.log(`Performing semantic search for: "${searchQuery}"`);

    // In a real implementation, you would:
    // 1. Generate an embedding for the search query
    // 2. Use that embedding to query Pinecone

    // For this test, we'll use our random vector (simulating an embedding)
    const queryResult = await index.query({
      vector: testVector,
      topK: 5,
      includeMetadata: true,
    });

    console.log(`Query returned ${queryResult.matches?.length || 0} results`);
    if (queryResult.matches?.length) {
      console.log('Top matches:');
      queryResult.matches.forEach((match, i) => {
        console.log(`${i + 1}. Score: ${match.score}, ID: ${match.id}`);
        console.log(`   Title: ${match.metadata?.title}`);
        console.log(`   Summary: ${match.metadata?.summary}`);
        console.log('---');
      });
    }

    console.log('Semantic search test completed successfully!');
  } catch (error) {
    console.error(`Semantic search test failed: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
testSemanticSearch();
