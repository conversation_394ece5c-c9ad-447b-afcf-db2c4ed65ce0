import { Pinecone } from '@pinecone-database/pinecone';
import * as dotenv from 'dotenv';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

async function testPineconeConnection() {
  console.log('Starting Pinecone connection test...');

  try {
    const apiKey = process.env.PINECONE_API_KEY;
    const apiUrl = process.env.PINECONE_API_URL;
    const apiRegion = process.env.PINECONE_API_REGION;

    if (!apiKey) {
      throw new Error(
        'PINECONE_API_KEY is not defined in environment variables',
      );
    }

    if (!apiUrl) {
      throw new Error(
        'PINECONE_API_URL is not defined in environment variables',
      );
    }

    console.log(`Using Pinecone configuration:
- API Key: ${apiKey ? '****' + apiKey.substring(apiKey.length - 4) : 'not set'}
- Region: ${apiRegion || 'not set'}
- URL: ${apiUrl || 'not set'}`);

    // Initialize Pinecone client with API URL
    console.log('Initializing Pinecone client with API URL...');
    // For the MVP, we'll create a standard configuration and modify it
    // This is not ideal but ensures backward compatibility with the Pinecone SDK
    const pineconeConfig = {
      apiKey,
    };

    const pinecone = new Pinecone(pineconeConfig);

    // List indexes to verify connection
    console.log('Testing listIndexes...');
    const indexes = await pinecone.listIndexes();
    console.log(
      `Found ${indexes.indexes?.length || 0} indexes: ${indexes.indexes?.map((idx) => idx.name).join(', ') || 'None'}`,
    );

    // Use the existing "jobs" index instead of creating a new one
    const indexName = 'jobs';
    console.log(`Accessing existing index: ${indexName}...`);

    // Get the index instance directly using the API URL
    const index = pinecone.index(indexName);

    // Get index stats to verify connection
    console.log('Fetching index statistics...');
    const stats = await index.describeIndexStats();
    console.log(`Index stats: ${JSON.stringify(stats, null, 2)}`);

    // Create a test vector using the correct dimension (1024)
    const dimension = 1024; // Use the dimension from the index stats
    console.log(
      `Testing vector upsert operation with dimension ${dimension}...`,
    );
    const testVector = Array(dimension)
      .fill(0)
      .map(() => Math.random() - 0.5); // Random test vector
    const testId = `test-vector-${Date.now()}`;

    // Upsert the test vector
    await index.upsert([
      {
        id: testId,
        values: testVector,
        metadata: {
          title: 'Test Job',
          description: 'This is a test job posting',
          timestamp: new Date().toISOString(),
        },
      },
    ]);
    console.log(`Successfully upserted test vector with ID: ${testId}`);

    // Query the index
    console.log('Testing vector query operation...');
    const queryResult = await index.query({
      vector: testVector,
      topK: 1,
      includeMetadata: true,
    });

    console.log(`Query returned ${queryResult.matches?.length || 0} results`);
    if (queryResult.matches?.length) {
      console.log(
        `Top match: ${JSON.stringify(queryResult.matches[0].metadata)}`,
      );
    }

    console.log('Pinecone connection test completed successfully!');
  } catch (error) {
    console.error(`Pinecone test failed: ${error.message}`);
    console.error(error.stack);
    process.exit(1);
  }
}

// Run the test
testPineconeConnection();
