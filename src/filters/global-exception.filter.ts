import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Inject,
  Logger,
  Optional,
} from '@nestjs/common';
import { HttpAdapterHost } from '@nestjs/core';
import { GcpLoggerService } from '../logging/gcp-logger.service';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger: GcpLoggerService | Logger;

  constructor(
    private readonly httpAdapterHost: HttpAdapterHost,
    @Optional() @Inject('LOGGER') gcpLogger?: GcpLoggerService,
  ) {
    this.logger = gcpLogger || new Logger(GlobalExceptionFilter.name);
  }

  catch(exception: unknown, host: ArgumentsHost): void {
    // Get the HTTP adapter from the host
    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();

    // Determine HTTP status code
    const httpStatus =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    // Get response message
    let responseMessage: string;
    if (exception instanceof HttpException) {
      const response = exception.getResponse();
      responseMessage =
        typeof response === 'string'
          ? response
          : typeof response === 'object' && 'message' in response
            ? Array.isArray(response['message'])
              ? response['message'][0]
              : response['message']
            : 'Internal server error';
    } else {
      responseMessage =
        exception instanceof Error
          ? exception.message
          : 'Internal server error';
    }

    // Log the error
    this.logger.error(
      `Status ${httpStatus}: ${responseMessage}`,
      exception instanceof Error ? exception.stack : undefined,
    );

    // Prepare response body
    const responseBody = {
      statusCode: httpStatus,
      timestamp: new Date().toISOString(),
      path: httpAdapter.getRequestUrl(ctx.getRequest()),
      message: responseMessage,
    };

    // Send the response
    httpAdapter.reply(ctx.getResponse(), responseBody, httpStatus);
  }
}
