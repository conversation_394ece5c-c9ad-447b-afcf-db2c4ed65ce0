import {
  CallHandler,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { v4 as uuidv4 } from 'uuid';
import { GcpLoggerService } from './gcp-logger.service';

@Injectable()
export class EnhancedLoggingInterceptor implements NestInterceptor {
  private readonly logger: GcpLoggerService | Logger;

  // Add fields that should be redacted
  private readonly sensitiveFields = [
    'password',
    'token',
    'authorization',
    'secret',
    'creditCard',
    'ssn',
    'access_token',
    'refresh_token',
    'jwt',
    'api_key',
    'apikey',
    'key',
    'auth',
    'credentials',
    'private',
    'session',
  ];

  constructor(logger?: GcpLoggerService) {
    this.logger = logger || new Logger('HTTP');
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    if (context.getType() !== 'http') {
      return next.handle();
    }

    const request = context.switchToHttp().getRequest();
    const { method, url, body, query, params, headers } = request;

    // Generate a unique ID for this request
    const requestId = uuidv4();
    request.requestId = requestId;

    // Skip logging for health check endpoints
    if (url === '/health' || url === '/api/health') {
      return next.handle();
    }

    // Get client IP
    const ip = this.getClientIp(request);

    // Skip detailed logging in production unless explicitly enabled
    const isVerboseLoggingEnabled =
      process.env.NODE_ENV !== 'production' ||
      process.env.VERBOSE_REQUEST_LOGGING === 'true';

    // Log basic request info
    const requestLog = {
      message: `Request received: ${method} ${url}`,
      requestId,
      method,
      url,
      ip,
      timestamp: new Date().toISOString(),
    };

    // Add detailed request data if verbose logging is enabled
    if (isVerboseLoggingEnabled) {
      // Sanitize request data
      Object.assign(requestLog, {
        body: this.sanitizeData(body),
        query: this.sanitizeData(query),
        params: this.sanitizeData(params),
        headers: this.sanitizeData(headers),
      });
    }

    this.logger.log(requestLog, 'HTTP');

    const startTime = Date.now();
    return next.handle().pipe(
      tap({
        next: (responseData) => {
          const responseTime = Date.now() - startTime;
          const response = context.switchToHttp().getResponse();

          // Log response
          const responseLog = {
            message: `Request completed: ${method} ${url}`,
            requestId,
            method,
            url,
            statusCode: response.statusCode,
            responseTime: `${responseTime}ms`,
            timestamp: new Date().toISOString(),
          };

          // Log response data in development or if explicitly enabled
          if (isVerboseLoggingEnabled && responseData) {
            responseLog['response'] = this.sanitizeData(responseData);
          }

          this.logger.log(responseLog, 'HTTP');
        },
        error: (error) => {
          const responseTime = Date.now() - startTime;

          // Log error response
          this.logger.error(
            {
              message: `Request failed: ${method} ${url}`,
              requestId,
              method,
              url,
              error: error.message,
              statusCode: error.status || 500,
              responseTime: `${responseTime}ms`,
              timestamp: new Date().toISOString(),
            },
            error.stack,
            'HTTP',
          );
        },
      }),
    );
  }

  // Get client IP address from request
  private getClientIp(request: any): string {
    return (
      request.headers['x-forwarded-for']?.split(',').shift() ||
      request.connection?.remoteAddress ||
      request.socket?.remoteAddress ||
      request.connection?.socket?.remoteAddress ||
      'unknown'
    );
  }

  // Sanitize sensitive data
  private sanitizeData(data: any): any {
    if (!data) return data;

    if (typeof data === 'object' && data !== null) {
      const sanitized = { ...data };

      for (const key of Object.keys(sanitized)) {
        // Check if the key contains any sensitive field names
        if (
          this.sensitiveFields.some((field) =>
            key.toLowerCase().includes(field.toLowerCase()),
          )
        ) {
          sanitized[key] = '[REDACTED]';
        } else if (
          typeof sanitized[key] === 'object' &&
          sanitized[key] !== null
        ) {
          sanitized[key] = this.sanitizeData(sanitized[key]);
        }
      }

      return sanitized;
    }

    return data;
  }
}