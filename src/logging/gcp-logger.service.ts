import { Injectable, LoggerService } from '@nestjs/common';
import { Logging } from '@google-cloud/logging';

@Injectable()
export class GcpLoggerService implements LoggerService {
  private static hasLoggedConnectionStatus = false;
  private static hasConfirmedLogging = false;
  private static hasWarnedMissingProjectId = false;
  private logging: Logging;
  private logName: string;

  constructor() {
    // If running in GCP, credentials are auto-detected
    this.logging = new Logging();
    this.logName = process.env.GCP_LOG_NAME || 'job-service-logs';
  }

  log(message: any, context?: string) {
    this.writeLog('INFO', message, context);
  }

  error(message: any, trace?: string, context?: string) {
    this.writeLog('ERROR', message, context, trace);
  }

  warn(message: any, context?: string) {
    this.writeLog('WARNING', message, context);
  }

  debug(message: any, context?: string) {
    if (process.env.NODE_ENV === 'production' && process.env.DEBUG !== 'true') {
      return; // Skip debug logs in production unless explicitly enabled
    }
    this.writeLog('DEBUG', message, context);
  }

  verbose(message: any, context?: string) {
    if (
      process.env.NODE_ENV === 'production' &&
      process.env.VERBOSE !== 'true'
    ) {
      return; // Skip verbose logs in production unless explicitly enabled
    }
    this.writeLog('DEBUG', message, context);
  }

  private writeLog(
    severity: string,
    message: any,
    context?: string,
    stack?: string,
  ) {
    // Fallback to console logging when not in production or GCP logging is not available
    if (
      process.env.NODE_ENV !== 'production' ||
      process.env.USE_CONSOLE_LOGGER === 'true'
    ) {
      const consoleMethod =
        severity === 'ERROR'
          ? console.error
          : severity === 'WARNING'
            ? console.warn
            : severity === 'DEBUG'
              ? console.debug
              : console.log;

      const logPrefix = `[${severity}]${context ? ` [${context}]` : ''}`;

      if (typeof message === 'object') {
        consoleMethod(logPrefix, message);
      } else {
        consoleMethod(`${logPrefix} ${message}`);
      }

      if (stack) {
        console.error(stack);
      }

      // Skip cloud logging if explicitly disabled
      if (process.env.USE_CLOUD_LOGGER === 'false') {
        return;
      }
    }

    // Skip cloud logging if we've already warned about a missing Project ID
    if (GcpLoggerService.hasWarnedMissingProjectId) {
      return;
    }

    try {
      const log = this.logging.log(this.logName);
      const projectId = this.logging.projectId;

      // Add additional diagnostics for the first log entry
      if (!GcpLoggerService.hasLoggedConnectionStatus) {
        console.log(
          `GCP Cloud Logging attempting to connect with project ID: ${projectId || 'auto-detect'}`,
        );
        GcpLoggerService.hasLoggedConnectionStatus = true;
      }

      const metadata = {
        severity: severity,
        resource: {
          type: 'global',
        },
      };

      // Add labels if context is provided
      if (context) {
        Object.assign(metadata, {
          labels: {
            context,
          },
        });
      }

      // Prepare the log entry data
      let data;
      if (typeof message === 'object') {
        data = message;
        // Add stack trace if available
        if (stack) {
          data.stack = stack;
        }
      } else {
        data = {
          message,
          ...(stack && { stack }),
          ...(context && { context }),
        };
      }

      const entry = log.entry(metadata, data);

      // Fire and forget in production
      log
        .write(entry)
        .then(() => {
          if (!GcpLoggerService.hasConfirmedLogging) {
            console.log('✅ Successfully connected to GCP Cloud Logging');
            GcpLoggerService.hasConfirmedLogging = true;
          }
        })
        .catch((err) => {
          if (
            err &&
            typeof err.message === 'string' &&
            err.message.includes('Unable to detect a Project Id')
          ) {
            if (!GcpLoggerService.hasWarnedMissingProjectId) {
              console.warn(
                '[GcpLoggerService] GCP Cloud Logging setup failed: Unable to detect a Project ID. Ensure GOOGLE_CLOUD_PROJECT env var is set or gcloud CLI is configured. GCP logging will be disabled.',
              );
              GcpLoggerService.hasWarnedMissingProjectId = true;
            }
          } else {
            // For other errors during log.write
            console.error('Failed to write to Cloud Logging:', err);
          }
        });
    } catch (error) {
      // Handle synchronous errors during GCP logging setup (e.g., new Logging())
      if (
        error &&
        typeof error.message === 'string' &&
        error.message.includes('Unable to detect a Project Id')
      ) {
        if (!GcpLoggerService.hasWarnedMissingProjectId) {
          console.warn(
            '[GcpLoggerService] GCP Cloud Logging init failed: Unable to detect a Project ID. GCP logging will be disabled.',
          );
          GcpLoggerService.hasWarnedMissingProjectId = true;
        }
      } else {
        console.error('Error initializing or writing to Cloud Logging:', error);
      }
      // The console fallback for the original message is handled by the block above if USE_CONSOLE_LOGGER is true or not in prod.
    }
  }
}