import { Global, Module } from '@nestjs/common';
import { GcpLoggerService } from './gcp-logger.service';
import { EnhancedLoggingInterceptor } from './enhanced-logging.interceptor';
import { LoggingConfigService } from './logging-config.service';

@Global()
@Module({
  providers: [
    GcpLoggerService,
    LoggingConfigService,
    {
      provide: 'LOGGER',
      useFactory: () => new GcpLoggerService(),
    },
    {
      provide: 'LOGGING_INTERCEPTOR',
      useFactory: (logger: GcpLoggerService) =>
        new EnhancedLoggingInterceptor(logger),
      inject: [GcpLoggerService],
    },
  ],
  exports: [GcpLoggerService, 'LOGGER', 'LOGGING_INTERCEPTOR'],
})
export class LoggingModule {}