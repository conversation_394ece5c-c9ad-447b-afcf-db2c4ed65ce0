import { Injectable, OnModuleInit } from '@nestjs/common';
import { GcpLoggerService } from './gcp-logger.service';

@Injectable()
export class LoggingConfigService implements OnModuleInit {
  constructor(private readonly logger: GcpLoggerService) {}

  onModuleInit() {
    // Determine logging destination
    const isProduction = process.env.NODE_ENV === 'production';
    const useConsoleLogger =
      process.env.USE_CONSOLE_LOGGER === 'true' || !isProduction;
    const useCloudLogger =
      process.env.USE_CLOUD_LOGGER !== 'false' && isProduction;

    // Determine verbose logging
    const verboseLogging =
      process.env.VERBOSE_REQUEST_LOGGING === 'true' || !isProduction;
    const debugLogging = process.env.DEBUG === 'true' || !isProduction;

    // Log the current configuration
    this.logger.log(
      {
        message: 'Logging configuration initialized',
        configuration: {
          environment: isProduction ? 'production' : 'development',
          loggingDestination: useCloudLogger
            ? useConsoleLogger
              ? 'GCP Cloud Logging + Console'
              : 'GCP Cloud Logging only'
            : 'Console only',
          verboseRequestLogging: verboseLogging ? 'enabled' : 'disabled',
          debugLogging: debugLogging ? 'enabled' : 'disabled',
          logName: process.env.GCP_LOG_NAME || 'job-service-logs',
        },
      },
      'LoggingConfig',
    );
  }
}