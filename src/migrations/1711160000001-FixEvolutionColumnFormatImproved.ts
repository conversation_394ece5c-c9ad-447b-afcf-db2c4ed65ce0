import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixEvolutionColumnFormatImproved1711160000001
  implements MigrationInterface
{
  name = 'FixEvolutionColumnFormatImproved1711160000001';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Get all processed_job records where evolution column contains ```json pattern
    const records = await queryRunner.query(
      `SELECT id, evolution FROM processed_job WHERE evolution::text LIKE '%\`\`\`json%'`,
    );

    console.log(
      `Found ${records.length} records with incorrectly formatted evolution data`,
    );

    let successCount = 0;
    let errorCount = 0;

    // Process each record and update with the correct JSON
    for (const record of records) {
      try {
        const rawValue = record.evolution;

        if (typeof rawValue === 'string') {
          // The whole column is a string instead of a JSON object
          let jsonStr = rawValue;

          // Extract JSON content between ```json markers
          const match = rawValue.match(/```json\s*\n([\s\S]*?)```/);
          if (match && match[1]) {
            jsonStr = match[1].trim();

            // Advanced JSON cleaning
            // 1. Try to clean up common issues (missing commas, unclosed brackets)
            let cleanedJson = jsonStr;

            try {
              // First attempt - direct parsing
              const parsedJson = JSON.parse(cleanedJson);

              // If we get here, the JSON was valid
              await queryRunner.query(
                `UPDATE processed_job SET evolution = $1 WHERE id = $2`,
                [JSON.stringify(parsedJson), record.id],
              );

              console.log(`Successfully fixed record ID: ${record.id}`);
              successCount++;
            } catch (parseError) {
              // Second attempt - create a valid JSON structure for a known pattern
              if (
                cleanedJson.includes('evolution_summary') &&
                cleanedJson.includes('automation_impact') &&
                cleanedJson.includes('relevance_prediction')
              ) {
                // Extract the key parts using regex patterns
                const evolutionSummary = extractValue(
                  cleanedJson,
                  'evolution_summary',
                );
                const automationImpact = extractValue(
                  cleanedJson,
                  'automation_impact',
                );
                const relevancePrediction = extractValue(
                  cleanedJson,
                  'relevance_prediction',
                );

                // Create a well-formed JSON object
                const fixedJson = {
                  evolution_summary: evolutionSummary || '',
                  automation_impact: automationImpact || '',
                  relevance_prediction: relevancePrediction || '',
                };

                await queryRunner.query(
                  `UPDATE processed_job SET evolution = $1 WHERE id = $2`,
                  [JSON.stringify(fixedJson), record.id],
                );

                console.log(
                  `Successfully fixed record ID: ${record.id} (using structured approach)`,
                );
                successCount++;
              } else {
                // If all attempts fail, log the error
                console.error(
                  `Could not parse JSON for record ID ${record.id}: ${parseError.message}`,
                );
                errorCount++;
              }
            }
          } else {
            console.error(`No JSON content found in record ID ${record.id}`);
            errorCount++;
          }
        } else if (rawValue && typeof rawValue === 'object') {
          // The column is an object but might have string properties with incorrect JSON
          const fixedObject = { ...rawValue };
          let hasChanges = false;

          // Check each property for the ```json pattern
          for (const [key, value] of Object.entries(fixedObject)) {
            if (typeof value === 'string' && value.includes('```json')) {
              const match = value.match(/```json\s*\n([\s\S]*?)```/);
              if (match && match[1]) {
                try {
                  // Try to extract clean text
                  const evolutionValue = extractValue(match[1], key);
                  if (evolutionValue) {
                    fixedObject[key] = evolutionValue;
                    hasChanges = true;
                  }
                } catch (e) {
                  console.error(
                    `Error parsing JSON in property ${key} of record ${record.id}: ${e.message}`,
                  );
                  errorCount++;
                }
              }
            }
          }

          if (hasChanges) {
            await queryRunner.query(
              `UPDATE processed_job SET evolution = $1 WHERE id = $2`,
              [JSON.stringify(fixedObject), record.id],
            );
            console.log(
              `Successfully fixed record ID: ${record.id} (object properties)`,
            );
            successCount++;
          }
        }
      } catch (error) {
        console.error(
          `Error processing record ID ${record.id}: ${error.message}`,
        );
        errorCount++;
      }
    }

    console.log(
      `Migration complete: ${successCount} records fixed, ${errorCount} errors`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration cannot be reversed as it would be impossible to determine
    // which records had the original formatting issue
    console.log('This migration cannot be reversed');
  }
}

// Helper function to extract values from malformed JSON
function extractValue(text: string, key: string): string | null {
  // Look for patterns like "key": "value" or "key":"value"
  const regex = new RegExp(`"${key}"\\s*:\\s*"([^"]*)"`, 'i');
  const match = text.match(regex);

  if (match && match[1]) {
    return match[1].trim();
  }

  return null;
}
