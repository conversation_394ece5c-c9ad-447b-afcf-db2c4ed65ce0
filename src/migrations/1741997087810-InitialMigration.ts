import { MigrationInterface, QueryRunner } from 'typeorm';

export class InitialMigration1741997087810 implements MigrationInterface {
  name = 'InitialMigration1741997087810';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE "programs" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "code" character varying NOT NULL, "degree_name" character varying NOT NULL, "degree_title" character varying NOT NULL, CONSTRAINT "UQ_11924ca2e0cb47a8d9400bada03" UNIQUE ("code"), CONSTRAINT "PK_d43c664bcaafc0e8a06dfd34e05" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."competences_type_enum" AS ENUM('CORE', 'COMPLEMENTARY')`,
    );
    await queryRunner.query(
      `CREATE TABLE "competences" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "name" character varying NOT NULL, "description" text, "type" "public"."competences_type_enum" NOT NULL, "program_id" integer NOT NULL, CONSTRAINT "PK_1e02a716ddbcbed830e60d5cbbf" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "user_competences" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "supertokens_id" character varying NOT NULL, "competence_id" integer NOT NULL, CONSTRAINT "PK_4b06fdc651e36117f0d6b420f7d" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_35b25f0074a8d4793f30a02cee" ON "user_competences" ("supertokens_id") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."skills_type_enum" AS ENUM('TECHNICAL', 'SOFT', 'PRACTICAL', 'DOMAIN')`,
    );
    await queryRunner.query(
      `CREATE TABLE "skills" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "name" character varying NOT NULL, "escoId" integer, "type" "public"."skills_type_enum" NOT NULL, CONSTRAINT "UQ_81f05095507fd84aa2769b4a522" UNIQUE ("name"), CONSTRAINT "PK_0d3212120f4ecedf90864d7e298" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "regions" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "code" character varying NOT NULL, "classificationName" character varying NOT NULL, "level" integer NOT NULL, CONSTRAINT "UQ_4f6dc5a464961e7c65a395ea4c6" UNIQUE ("code"), CONSTRAINT "PK_4fcd12ed6a046276e2deb08801c" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "municipalities" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "code" character varying NOT NULL, "classificationName" character varying NOT NULL, "level" integer NOT NULL, CONSTRAINT "UQ_9e0e960d7323bb120dc5e915dd8" UNIQUE ("code"), CONSTRAINT "PK_9c4573349577306f221dda4d924" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "job_location" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "job_id" character varying NOT NULL, "flexible" boolean NOT NULL, "country" text NOT NULL, CONSTRAINT "UQ_e9257d6e46580c6ddec5c33e8a6" UNIQUE ("job_id"), CONSTRAINT "REL_e9257d6e46580c6ddec5c33e8a" UNIQUE ("job_id"), CONSTRAINT "PK_9331dcc9601546cc211e12d9be6" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "job_translation" ("id" SERIAL NOT NULL, "job_id" character varying NOT NULL, "language" character varying NOT NULL, "title" character varying NOT NULL, "description" character varying, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), CONSTRAINT "PK_dda647e09181433119f03f3ab31" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_898802f1ed4e40f3697287cc2f" ON "job_translation" ("job_id", "language") `,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."employer_type_enum" AS ENUM('01', '02')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."employer_company_type_enum" AS ENUM('startup', 'scaleup', 'enterprise', 'sme', 'other')`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."employer_company_size_enum" AS ENUM('micro', 'small', 'medium', 'large')`,
    );
    await queryRunner.query(
      `CREATE TABLE "employer" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "type" "public"."employer_type_enum" NOT NULL DEFAULT '01', "business_id" character varying NOT NULL, "name" character varying NOT NULL, "language_code" character varying NOT NULL, "website" character varying, "email" character varying, "phone" character varying, "logoUrl" character varying, "businessInfoUrl" character varying, "company_type" "public"."employer_company_type_enum", "company_size" "public"."employer_company_size_enum", "industry" character varying, "description" character varying, "logo_url" character varying, "social_media" json, CONSTRAINT "UQ_b6209ea56f1c5db0600e34f407c" UNIQUE ("business_id"), CONSTRAINT "PK_74029e6b1f17a4c7c66d43cfd34" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_b6209ea56f1c5db0600e34f407" ON "employer" ("business_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "job" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "ext_id" character varying NOT NULL, "employer_name" character varying, "employer_id" integer, "languages" text NOT NULL, "working_time" character varying NOT NULL, "continuity" character varying NOT NULL, "working_hours" character varying, "workplace_flexibility" character varying, "driver_license_codes" text, "salary_type" character varying, "work_begins" character varying, "shift_type" character varying, "part_time_hours" character varying, "expires_at" TIMESTAMP, "expired" boolean NOT NULL DEFAULT false, "title" character varying, "description" text, "application_url" character varying, "source" character varying NOT NULL DEFAULT 'Tyomarkkinatori', "last_import_date" TIMESTAMP, "published_date" TIMESTAMP, "processed" boolean NOT NULL DEFAULT false, "queued_for_processing" boolean NOT NULL DEFAULT false, "processing_batch_id" character varying, "pinecone_uploaded" boolean NOT NULL DEFAULT false, "pinecone_uploaded_at" TIMESTAMP, CONSTRAINT "UQ_39d69621f22dd02c23685019f14" UNIQUE ("ext_id"), CONSTRAINT "PK_98ab1c14ff8d1cf80d18703b92f" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE UNIQUE INDEX "IDX_39d69621f22dd02c23685019f1" ON "job" ("ext_id") `,
    );
    await queryRunner.query(
      `CREATE TABLE "occupation" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "preferredLabel" character varying NOT NULL, "preferredLabelTranslations" jsonb NOT NULL DEFAULT '{}', "growthStatus" character varying, "code" character varying NOT NULL, "conceptUri" character varying, "description" text, "salaryLow" numeric(10,2), "salaryHigh" numeric(10,2), "programCodes" text, CONSTRAINT "UQ_d3dcd8193f562dc092ef325ea14" UNIQUE ("code"), CONSTRAINT "UQ_d47a77264b4b52226ada6b09b53" UNIQUE ("conceptUri"), CONSTRAINT "PK_07cfcefef555693d96dce8805c5" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "industry" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "code" character varying NOT NULL, "name" character varying NOT NULL, "translations" json, "description" character varying, "last_analyzed_date" TIMESTAMP, CONSTRAINT "UQ_d76e293720ae8ab8fe7afffdc98" UNIQUE ("code"), CONSTRAINT "PK_fc3e38485cff79e9fbba8f13831" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."processed_job_seniority_enum" AS ENUM('none', 'entry', 'junior', 'mid', 'senior', 'lead', 'managerial', 'executive')`,
    );
    await queryRunner.query(
      `CREATE TABLE "processed_job" ("id" SERIAL NOT NULL, "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "isActive" boolean NOT NULL DEFAULT true, "job_id" character varying NOT NULL, "source" character varying NOT NULL DEFAULT 'Tyomarkkinatori', "elevator_pitch" character varying, "industry_code" character varying NOT NULL, "occupation_code" character varying, "occupation_code_secondary" character varying, "occupation_code_tertiary" character varying, "skills" json NOT NULL DEFAULT '[]', "salary" json, "application_url" character varying, "summary" text, "degree_required" boolean NOT NULL DEFAULT false, "seniority" "public"."processed_job_seniority_enum" NOT NULL DEFAULT 'none', "primary_tasks" json NOT NULL DEFAULT '[]', "keywords" json NOT NULL DEFAULT '[]', "skill_contexts" json NOT NULL DEFAULT '[]', "expected_outcomes" json NOT NULL DEFAULT '[]', "possible_career_outcomes" json NOT NULL DEFAULT '[]', "typical_day_tasks" json NOT NULL DEFAULT '[]', "culture_keywords" json NOT NULL DEFAULT '[]', "ideal_candidate_traits" json NOT NULL DEFAULT '[]', "work_life_balance_signals" json NOT NULL DEFAULT '[]', "role_impact_summary" text, "market_salary" json, "evolution" json, CONSTRAINT "UQ_a0afc0ab5d6fe2124ad49de9c11" UNIQUE ("job_id"), CONSTRAINT "REL_a0afc0ab5d6fe2124ad49de9c1" UNIQUE ("job_id"), CONSTRAINT "PK_9dfc78c8a06566c4778bbc127de" PRIMARY KEY ("id"))`,
    );
    await queryRunner.query(
      `CREATE TABLE "job_location_regions" ("job_location_id" integer NOT NULL, "region_code" character varying NOT NULL, CONSTRAINT "PK_b5c38094f4f4070f7513502d07f" PRIMARY KEY ("job_location_id", "region_code"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_5a2e0494e9caff48b86b0ab0ab" ON "job_location_regions" ("job_location_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_4aa8eb271188b7ba99c838a5eb" ON "job_location_regions" ("region_code") `,
    );
    await queryRunner.query(
      `CREATE TABLE "job_location_municipalities" ("job_location_id" integer NOT NULL, "municipality_code" character varying NOT NULL, CONSTRAINT "PK_3c34e53ce1489f283ad0ed22505" PRIMARY KEY ("job_location_id", "municipality_code"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_097f0c43c8f6801060393f8a31" ON "job_location_municipalities" ("job_location_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_0c53e29ce692ca8181658777bb" ON "job_location_municipalities" ("municipality_code") `,
    );
    await queryRunner.query(
      `CREATE TABLE "occupation_industries" ("occupation_code" character varying NOT NULL, "industry_code" character varying NOT NULL, CONSTRAINT "PK_e3d7a21d379367b353ce238295e" PRIMARY KEY ("occupation_code", "industry_code"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_d8bc6141341c2e04f150c84adb" ON "occupation_industries" ("occupation_code") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_de86e88c684df9193a24eb6873" ON "occupation_industries" ("industry_code") `,
    );
    await queryRunner.query(
      `CREATE TABLE "occupation_skills" ("occupation_id" integer NOT NULL, "skill_id" integer NOT NULL, CONSTRAINT "PK_dad0c8f293e47fe9e685e3a2e3d" PRIMARY KEY ("occupation_id", "skill_id"))`,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_8e9ccd472c2b8a2d6bdf76ad39" ON "occupation_skills" ("occupation_id") `,
    );
    await queryRunner.query(
      `CREATE INDEX "IDX_757c01cdea1480322bd8d7c83f" ON "occupation_skills" ("skill_id") `,
    );
    await queryRunner.query(
      `ALTER TABLE "competences" ADD CONSTRAINT "FK_f2aa4d7740446e3c4feb61c74f7" FOREIGN KEY ("program_id") REFERENCES "programs"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_competences" ADD CONSTRAINT "FK_3b0861eed57aa1359e9ebb17b9c" FOREIGN KEY ("competence_id") REFERENCES "competences"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location" ADD CONSTRAINT "FK_e9257d6e46580c6ddec5c33e8a6" FOREIGN KEY ("job_id") REFERENCES "job"("ext_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_translation" ADD CONSTRAINT "FK_8ca3dcb9fb6f510611e74795f83" FOREIGN KEY ("job_id") REFERENCES "job"("ext_id") ON DELETE CASCADE ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" ADD CONSTRAINT "FK_b29124ef862925abf6b729236eb" FOREIGN KEY ("employer_id") REFERENCES "employer"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" ADD CONSTRAINT "FK_a0afc0ab5d6fe2124ad49de9c11" FOREIGN KEY ("job_id") REFERENCES "job"("ext_id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" ADD CONSTRAINT "FK_ddc1e10d22bbb20f0a2fd8abaf0" FOREIGN KEY ("industry_code") REFERENCES "industry"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" ADD CONSTRAINT "FK_1350df20f5df6981a2791fa64a3" FOREIGN KEY ("occupation_code") REFERENCES "occupation"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" ADD CONSTRAINT "FK_2370447acc3180bdec074c97173" FOREIGN KEY ("occupation_code_secondary") REFERENCES "occupation"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" ADD CONSTRAINT "FK_b5b12661e400defe1c7a76ee8cb" FOREIGN KEY ("occupation_code_tertiary") REFERENCES "occupation"("code") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_regions" ADD CONSTRAINT "FK_5a2e0494e9caff48b86b0ab0ab1" FOREIGN KEY ("job_location_id") REFERENCES "job_location"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_regions" ADD CONSTRAINT "FK_4aa8eb271188b7ba99c838a5eb3" FOREIGN KEY ("region_code") REFERENCES "regions"("code") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_municipalities" ADD CONSTRAINT "FK_097f0c43c8f6801060393f8a315" FOREIGN KEY ("job_location_id") REFERENCES "job_location"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_municipalities" ADD CONSTRAINT "FK_0c53e29ce692ca8181658777bbe" FOREIGN KEY ("municipality_code") REFERENCES "municipalities"("code") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "occupation_industries" ADD CONSTRAINT "FK_d8bc6141341c2e04f150c84adb8" FOREIGN KEY ("occupation_code") REFERENCES "occupation"("code") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "occupation_industries" ADD CONSTRAINT "FK_de86e88c684df9193a24eb68730" FOREIGN KEY ("industry_code") REFERENCES "industry"("code") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "occupation_skills" ADD CONSTRAINT "FK_8e9ccd472c2b8a2d6bdf76ad39c" FOREIGN KEY ("occupation_id") REFERENCES "occupation"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
    await queryRunner.query(
      `ALTER TABLE "occupation_skills" ADD CONSTRAINT "FK_757c01cdea1480322bd8d7c83fe" FOREIGN KEY ("skill_id") REFERENCES "skills"("id") ON DELETE CASCADE ON UPDATE CASCADE`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "occupation_skills" DROP CONSTRAINT "FK_757c01cdea1480322bd8d7c83fe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "occupation_skills" DROP CONSTRAINT "FK_8e9ccd472c2b8a2d6bdf76ad39c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "occupation_industries" DROP CONSTRAINT "FK_de86e88c684df9193a24eb68730"`,
    );
    await queryRunner.query(
      `ALTER TABLE "occupation_industries" DROP CONSTRAINT "FK_d8bc6141341c2e04f150c84adb8"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_municipalities" DROP CONSTRAINT "FK_0c53e29ce692ca8181658777bbe"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_municipalities" DROP CONSTRAINT "FK_097f0c43c8f6801060393f8a315"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_regions" DROP CONSTRAINT "FK_4aa8eb271188b7ba99c838a5eb3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location_regions" DROP CONSTRAINT "FK_5a2e0494e9caff48b86b0ab0ab1"`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" DROP CONSTRAINT "FK_b5b12661e400defe1c7a76ee8cb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" DROP CONSTRAINT "FK_2370447acc3180bdec074c97173"`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" DROP CONSTRAINT "FK_1350df20f5df6981a2791fa64a3"`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" DROP CONSTRAINT "FK_ddc1e10d22bbb20f0a2fd8abaf0"`,
    );
    await queryRunner.query(
      `ALTER TABLE "processed_job" DROP CONSTRAINT "FK_a0afc0ab5d6fe2124ad49de9c11"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job" DROP CONSTRAINT "FK_b29124ef862925abf6b729236eb"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_translation" DROP CONSTRAINT "FK_8ca3dcb9fb6f510611e74795f83"`,
    );
    await queryRunner.query(
      `ALTER TABLE "job_location" DROP CONSTRAINT "FK_e9257d6e46580c6ddec5c33e8a6"`,
    );
    await queryRunner.query(
      `ALTER TABLE "user_competences" DROP CONSTRAINT "FK_3b0861eed57aa1359e9ebb17b9c"`,
    );
    await queryRunner.query(
      `ALTER TABLE "competences" DROP CONSTRAINT "FK_f2aa4d7740446e3c4feb61c74f7"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_757c01cdea1480322bd8d7c83f"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_8e9ccd472c2b8a2d6bdf76ad39"`,
    );
    await queryRunner.query(`DROP TABLE "occupation_skills"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_de86e88c684df9193a24eb6873"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_d8bc6141341c2e04f150c84adb"`,
    );
    await queryRunner.query(`DROP TABLE "occupation_industries"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_0c53e29ce692ca8181658777bb"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_097f0c43c8f6801060393f8a31"`,
    );
    await queryRunner.query(`DROP TABLE "job_location_municipalities"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_4aa8eb271188b7ba99c838a5eb"`,
    );
    await queryRunner.query(
      `DROP INDEX "public"."IDX_5a2e0494e9caff48b86b0ab0ab"`,
    );
    await queryRunner.query(`DROP TABLE "job_location_regions"`);
    await queryRunner.query(`DROP TABLE "processed_job"`);
    await queryRunner.query(
      `DROP TYPE "public"."processed_job_seniority_enum"`,
    );
    await queryRunner.query(`DROP TABLE "industry"`);
    await queryRunner.query(`DROP TABLE "occupation"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_39d69621f22dd02c23685019f1"`,
    );
    await queryRunner.query(`DROP TABLE "job"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_b6209ea56f1c5db0600e34f407"`,
    );
    await queryRunner.query(`DROP TABLE "employer"`);
    await queryRunner.query(`DROP TYPE "public"."employer_company_size_enum"`);
    await queryRunner.query(`DROP TYPE "public"."employer_company_type_enum"`);
    await queryRunner.query(`DROP TYPE "public"."employer_type_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_898802f1ed4e40f3697287cc2f"`,
    );
    await queryRunner.query(`DROP TABLE "job_translation"`);
    await queryRunner.query(`DROP TABLE "job_location"`);
    await queryRunner.query(`DROP TABLE "municipalities"`);
    await queryRunner.query(`DROP TABLE "regions"`);
    await queryRunner.query(`DROP TABLE "skills"`);
    await queryRunner.query(`DROP TYPE "public"."skills_type_enum"`);
    await queryRunner.query(
      `DROP INDEX "public"."IDX_35b25f0074a8d4793f30a02cee"`,
    );
    await queryRunner.query(`DROP TABLE "user_competences"`);
    await queryRunner.query(`DROP TABLE "competences"`);
    await queryRunner.query(`DROP TYPE "public"."competences_type_enum"`);
    await queryRunner.query(`DROP TABLE "programs"`);
  }
}
