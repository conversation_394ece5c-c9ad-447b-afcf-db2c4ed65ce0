import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixEvolutionColumnFormat1711160000000
  implements MigrationInterface
{
  name = 'FixEvolutionColumnFormat1711160000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Get all processed_job records where evolution column contains ```json pattern
    const records = await queryRunner.query(
      `SELECT id, evolution FROM processed_job WHERE evolution::text LIKE '%\`\`\`json%'`,
    );

    console.log(
      `Found ${records.length} records with incorrectly formatted evolution data`,
    );

    // Process each record and update with the correct JSON
    for (const record of records) {
      try {
        const rawValue = record.evolution;

        if (typeof rawValue === 'string') {
          // The whole column is a string instead of a JSON object
          let jsonStr = rawValue;

          // Extract JSON content between ```json markers
          const match = rawValue.match(/```json\s*\n([\s\S]*?)```/);
          if (match && match[1]) {
            jsonStr = match[1];
          }

          // Try to parse the JSON string
          const parsedJson = JSON.parse(jsonStr);

          // Update the record with the properly parsed JSON
          await queryRunner.query(
            `UPDATE processed_job SET evolution = $1 WHERE id = $2`,
            [JSON.stringify(parsedJson), record.id],
          );

          console.log(`Successfully fixed record ID: ${record.id}`);
        } else if (rawValue && typeof rawValue === 'object') {
          // The column is an object but might have string properties with incorrect JSON
          const fixedObject = { ...rawValue };
          let hasChanges = false;

          // Check each property for the ```json pattern
          for (const [key, value] of Object.entries(fixedObject)) {
            if (typeof value === 'string' && value.includes('```json')) {
              const match = value.match(/```json\s*\n([\s\S]*?)```/);
              if (match && match[1]) {
                try {
                  fixedObject[key] = match[1];
                  hasChanges = true;
                } catch (e) {
                  console.error(
                    `Error parsing JSON in property ${key} of record ${record.id}: ${e.message}`,
                  );
                }
              }
            }
          }

          if (hasChanges) {
            await queryRunner.query(
              `UPDATE processed_job SET evolution = $1 WHERE id = $2`,
              [JSON.stringify(fixedObject), record.id],
            );
            console.log(
              `Successfully fixed record ID: ${record.id} (object properties)`,
            );
          }
        }
      } catch (error) {
        console.error(
          `Error processing record ID ${record.id}: ${error.message}`,
        );
      }
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // This migration cannot be reversed as it would be impossible to determine
    // which records had the original formatting issue
    console.log('This migration cannot be reversed');
  }
}
