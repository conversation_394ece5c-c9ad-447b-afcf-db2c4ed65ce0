import { MigrationInterface, QueryRunner, Table, TableIndex } from 'typeorm';

export class AddUserActionSummaryEntities1742160000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create enum type for timeframe
    await queryRunner.query(`
      CREATE TYPE "timeframe_type_enum" AS ENUM ('daily', 'weekly', 'monthly');
    `);

    // Create user_action_summary table
    await queryRunner.createTable(
      new Table({
        name: 'user_action_summary',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'userId',
            type: 'varchar',
          },
          {
            name: 'timeframe',
            type: 'timeframe_type_enum',
          },
          {
            name: 'periodStart',
            type: 'date',
          },
          {
            name: 'metrics',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'insights',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'isProcessed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'lastProcessedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'now()',
          },
        ],
      }),
      true,
    );

    // Create organization_action_summary table
    await queryRunner.createTable(
      new Table({
        name: 'organization_action_summary',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            default: 'uuid_generate_v4()',
          },
          {
            name: 'organizationId',
            type: 'varchar',
          },
          {
            name: 'timeframe',
            type: 'timeframe_type_enum',
          },
          {
            name: 'periodStart',
            type: 'date',
          },
          {
            name: 'metrics',
            type: 'jsonb',
            isNullable: true,
          },
          {
            name: 'memberCount',
            type: 'integer',
            default: 0,
          },
          {
            name: 'isProcessed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'lastProcessedAt',
            type: 'timestamp',
            isNullable: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp',
            default: 'now()',
          },
          {
            name: 'updatedAt',
            type: 'timestamp',
            default: 'now()',
          },
        ],
      }),
      true,
    );

    // Create indexes for user_action_summary
    await queryRunner.createIndex(
      'user_action_summary',
      new TableIndex({
        name: 'IDX_USER_ACTION_SUMMARY_USER_ID',
        columnNames: ['userId'],
      }),
    );

    await queryRunner.createIndex(
      'user_action_summary',
      new TableIndex({
        name: 'IDX_USER_ACTION_SUMMARY_TIMEFRAME',
        columnNames: ['timeframe'],
      }),
    );

    await queryRunner.createIndex(
      'user_action_summary',
      new TableIndex({
        name: 'IDX_USER_ACTION_SUMMARY_PERIOD_START',
        columnNames: ['periodStart'],
      }),
    );

    // Create indexes for organization_action_summary
    await queryRunner.createIndex(
      'organization_action_summary',
      new TableIndex({
        name: 'IDX_ORG_ACTION_SUMMARY_ORG_ID',
        columnNames: ['organizationId'],
      }),
    );

    await queryRunner.createIndex(
      'organization_action_summary',
      new TableIndex({
        name: 'IDX_ORG_ACTION_SUMMARY_TIMEFRAME',
        columnNames: ['timeframe'],
      }),
    );

    await queryRunner.createIndex(
      'organization_action_summary',
      new TableIndex({
        name: 'IDX_ORG_ACTION_SUMMARY_PERIOD_START',
        columnNames: ['periodStart'],
      }),
    );

    // Create unique constraint for user+timeframe+periodStart
    await queryRunner.query(`
      ALTER TABLE "user_action_summary" 
      ADD CONSTRAINT "UQ_USER_ACTION_SUMMARY_USER_TIMEFRAME_PERIOD" 
      UNIQUE ("userId", "timeframe", "periodStart");
    `);

    // Create unique constraint for org+timeframe+periodStart
    await queryRunner.query(`
      ALTER TABLE "organization_action_summary" 
      ADD CONSTRAINT "UQ_ORG_ACTION_SUMMARY_ORG_TIMEFRAME_PERIOD" 
      UNIQUE ("organizationId", "timeframe", "periodStart");
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop tables
    await queryRunner.dropTable('user_action_summary');
    await queryRunner.dropTable('organization_action_summary');
    
    // Drop enum type
    await queryRunner.query(`DROP TYPE "timeframe_type_enum"`);
  }
}