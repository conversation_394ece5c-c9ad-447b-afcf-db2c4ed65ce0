import { MigrationInterface, QueryRunner, TableIndex } from "typeorm";

export class AddUserIdIndexToUserActionSummary1747376200893 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.createIndex(
            'user_action_summary',
            new TableIndex({
                name: 'IDX_USER_ACTION_SUMMARY_USER_ID',
                columnNames: ['userId'],
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropIndex('user_action_summary', 'IDX_USER_ACTION_SUMMARY_USER_ID');
    }
}
