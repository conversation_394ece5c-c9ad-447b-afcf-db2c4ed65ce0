import { z } from 'zod';

// Common validation patterns
const ESCO_CODE_PATTERN = /^\d{4}(\.\d{1,3})?$/;
const NACE_CODE_PATTERN = /^[A-Z]\d{2}(\.\d{1,2})?$/;

// Job Schema
export const jobSchema = z.object({
  ext_id: z.string().min(1),
  employer_name: z.string().nullable(),
  languages: z.array(z.string().min(2).max(3)), // e.g., 'en', 'fi'
  working_time: z.enum(['FULL_TIME', 'PART_TIME', 'FLEXIBLE']),
  continuity: z.enum(['PERMANENT', 'TEMPORARY', 'SEASONAL']),
  expires_at: z.date().nullable(),
});

// Classification Schema
export const classificationSchema = z.object({
  esco_code: z.string().regex(ESCO_CODE_PATTERN, 'Invalid ESCO code format'),
  sector_id: z.number().int().positive(),
  name: z.string().min(1),
  description: z.string().nullable(),
});

// Industry Schema
export const industrySchema = z.object({
  nace_code: z.string().regex(NACE_CODE_PATTERN, 'Invalid NACE code format'),
  name: z.string().min(1),
  description: z.string().nullable(),
  last_analyzed_date: z.date().nullable(),
});

// Job Creation DTO Schema
export const createJobDto = jobSchema.extend({
  location: z.object({
    city: z.string().min(1),
    country: z.string().length(2), // ISO country code
    latitude: z.number().min(-90).max(90),
    longitude: z.number().min(-180).max(180),
  }),
});

// Validation Error Handler
export class ValidationError extends Error {
  constructor(public errors: z.ZodError) {
    super('Validation Error');
    this.name = 'ValidationError';
  }
}

// Validation Functions
export const validateJob = (data: unknown) => {
  try {
    return jobSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError(error);
    }
    throw error;
  }
};

export const validateClassification = (data: unknown) => {
  try {
    return classificationSchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError(error);
    }
    throw error;
  }
};

export const validateIndustry = (data: unknown) => {
  try {
    return industrySchema.parse(data);
  } catch (error) {
    if (error instanceof z.ZodError) {
      throw new ValidationError(error);
    }
    throw error;
  }
};
