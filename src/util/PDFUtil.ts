import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios from 'axios';
import { S3Client } from '@aws-sdk/client-s3';
import { Upload } from '@aws-sdk/lib-storage';
import { Readable } from 'stream';

// Helper function to convert stream to buffer
async function streamToBuffer(stream: Readable): Promise<Buffer> {
  return new Promise<Buffer>((resolve, reject) => {
    const chunks: any[] = [];
    stream.on('data', (chunk: any) => chunks.push(chunk));
    stream.on('error', reject);
    stream.on('end', () => resolve(Buffer.concat(chunks)));
  });
}

@Injectable()
export class PdfService {
  private s3Client: S3Client;

  constructor(private configService: ConfigService) {
    // Initialize S3 client with proper typing
    this.s3Client = new S3Client({
      region: this.configService.get<string>('AWS_REGION'),
      credentials: {
        accessKeyId: this.configService.get<string>('AWS_ACCESS_KEY_ID') || '',
        secretAccessKey:
          this.configService.get<string>('AWS_SECRET_ACCESS_KEY') || '',
      },
    });
  }

  /**
   * Converts HTML to PDF using Restpack API and returns the direct URL
   * @param htmlContent The HTML content to convert
   * @param filename Optional filename for the PDF
   * @returns The URL to the generated PDF
   */
  async convertHtmlToPdfWithRestpack(
    htmlContent: string,
    filename: string = 'resume.pdf',
  ): Promise<string> {
    try {
      const apiKey = this.configService.get<string>('RESTPACK_API_KEY');

      if (!apiKey) {
        throw new Error(
          'RESTPACK_API_KEY is not configured in environment variables',
        );
      }

      console.log('Using Restpack API key:', apiKey);

      // Make sure HTML content is properly encoded
      const response = await axios.post(
        'https://restpack.io/api/html2pdf/v7/convert',
        {
          html: htmlContent,
          json: true, // Return a JSON response with the URL
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'x-access-token': apiKey,
          },
        },
      );

      console.log('Restpack API Response:', JSON.stringify(response.data));

      // Extract the file URL from the response
      const fileUrl = response.data.file;

      if (!fileUrl) {
        throw new Error('PDF URL not found in Restpack response');
      }

      return fileUrl;
    } catch (error) {
      console.error('Restpack PDF generation error:', error);
      throw new Error(
        `Restpack PDF generation failed: ${error.message || 'Unknown error'}`,
      );
    }
  }

  async convertHtmlToPdf(
    htmlContent: string,
    filename: string = 'resume.pdf',
  ): Promise<{ pdfUrl: string; s3Url: string }> {
    try {
      // Base64 encode the HTML content with UTF-8 encoding
      const base64HtmlContent = Buffer.from(htmlContent, 'utf-8').toString(
        'base64',
      );

      // Use the PDFBolt Direct API endpoint with base64 encoding
      const response = await axios.post(
        'https://api.pdfbolt.com/v1/direct',
        {
          html: base64HtmlContent,
          format: 'A4',
        },
        {
          headers: {
            'Content-Type': 'application/json',
            API_KEY: this.configService.get<string>('PDFBOLT_API_KEY'),
          },
          responseType: 'stream', // Using stream response type
        },
      );

      // Convert stream to buffer for base64 encoding
      const pdfBuffer = await streamToBuffer(response.data);

      // Upload the PDF to S3
      const s3Key = `pdfs/${Date.now()}-${filename}`;
      const s3Url = await this.uploadToS3(response.data, s3Key);

      return {
        pdfUrl: `data:application/pdf;base64,${pdfBuffer.toString('base64')}`,
        s3Url,
      };
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error(
        `PDF generation failed: ${error.message || 'Unknown error'}`,
      );
    }
  }

  private async uploadToS3(fileStream: Readable, key: string): Promise<string> {
    try {
      const bucketName = this.configService.get<string>('AWS_S3_PUBLIC_BUCKET');

      const upload = new Upload({
        client: this.s3Client,
        params: {
          Bucket: bucketName || '',
          Key: key,
          Body: fileStream,
          ContentType: 'application/pdf',
        },
      });

      await upload.done();

      // Return the S3 URL
      return `https://${bucketName}.s3.${this.configService.get('AWS_REGION')}.amazonaws.com/${key}`;
    } catch (error) {
      console.error('S3 upload error:', error);
      throw new Error(`Failed to upload PDF to S3: ${error.message}`);
    }
  }
}
