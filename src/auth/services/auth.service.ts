import { Injectable, Logger } from '@nestjs/common';
import { Request } from 'express';
import * as Session from 'supertokens-node/recipe/session';
import { SessionContainer } from 'supertokens-node/recipe/session';
import { extractTokenFromRequest } from '../utils/auth.utils';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  /**
   * Get the user ID and session from a request
   * Works with both request.session (from AuthGuard) and Authorization header
   *
   * @param req Express request object
   * @returns Object containing userId and sessionContainer
   */
  async getUserSessionInfo(req: Request): Promise<{
    userId?: string;
    sessionContainer?: SessionContainer;
    token?: string;
  }> {
    // First check for a session (from AuthGuard)
    const session = req['session'] as SessionContainer;
    if (session) {
      return {
        userId: session.getUserId(),
        sessionContainer: session,
        token: undefined, // No direct access to token from session
      };
    }

    // If no session, try to get from Authorization header
    const token = extractTokenFromRequest(req);
    if (!token) {
      return {
        userId: undefined,
        sessionContainer: undefined,
        token: undefined,
      };
    }

    try {
      // Verify the token and get session
      const sessionContainer =
        await Session.getSessionWithoutRequestResponse(token);
      return {
        userId: sessionContainer.getUserId(),
        sessionContainer,
        token,
      };
    } catch (error) {
      this.logger.error(`Error verifying token: ${error.message}`, error.stack);
      return { userId: undefined, sessionContainer: undefined, token };
    }
  }

  /**
   * Verify a session using access token and session handle
   * Used for MCP impersonation validation
   *
   * @param accessToken The access token to verify
   * @param sessionHandle The session handle (optional)
   * @returns true if session is valid, false otherwise
   */
  async verifySession(
    accessToken: string,
    sessionHandle?: string,
  ): Promise<boolean> {
    try {
      const sessionContainer =
        await Session.getSessionWithoutRequestResponse(accessToken);
      
      // If sessionHandle is provided, verify it matches
      if (sessionHandle && sessionContainer.getHandle() !== sessionHandle) {
        return false;
      }
      
      // Check if session is still valid
      const sessionInfo = sessionContainer.getSessionDataFromDatabase();
      return sessionInfo !== undefined;
    } catch (error) {
      this.logger.debug(`Session verification failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Get user ID from session token
   * Used for MCP user validation
   *
   * @param accessToken The access token
   * @returns User ID if session is valid, null otherwise
   */
  async getUserIdFromSession(accessToken: string): Promise<string | null> {
    try {
      const sessionContainer =
        await Session.getSessionWithoutRequestResponse(accessToken);
      return sessionContainer.getUserId();
    } catch (error) {
      this.logger.debug(`Failed to get user ID from session: ${error.message}`);
      return null;
    }
  }
}
