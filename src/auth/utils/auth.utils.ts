import { Request } from 'express';
import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { SessionContainer } from 'supertokens-node/recipe/session';

/**
 * Extracts the authorization token from the request headers
 * @param req Express request object
 * @returns The token string or undefined if not found
 */
export function extractTokenFromRequest(req: Request): string | undefined {
  const authHeader = req.headers['authorization'];
  return authHeader ? authHeader.replace('Bearer ', '') : undefined;
}

/**
 * Parameter decorator that extracts the JWT token from the request
 * Usage: @Token() token: string
 */
export const Token = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest<Request>();
    return extractTokenFromRequest(request);
  },
);

/**
 * Parameter decorator that extracts the user ID from the session
 * Note: This requires AuthGuard to be applied to the endpoint
 * Usage: @UserId() userId: string
 */
export const UserId = createParamDecorator(
  (data: unknown, ctx: ExecutionContext): string => {
    const request = ctx.switchToHttp().getRequest<Request>();
    // Access the session through the custom property added by SuperTokens
    const session = request['session'] as SessionContainer;
    if (!session) {
      throw new Error('No session found. Make sure AuthGuard is applied to the endpoint.');
    }
    return session.getUserId();
  },
);
