/**
 * Authentication Module
 *
 * Responsibilities:
 * - Implements SuperTokens authentication flows
 * - Manages session middleware
 * - Provides authentication guards
 *
 * Dependencies:
 * - SharedModule (for database access)
 * - ConfigModule (environment configuration)
 * - `SupertokensService`: Handles the core SuperTokens functionality, including session management and user authentication.
 * - `AuthMiddleware`: Applies SuperTokens authentication to all routes.
 * - `AuthService`: Provides utilities for accessing user and session information
 *
 * Security:
 * - Implements CSRF protection
 * - Session management via HTTP-only cookies
 * - Rate limiting on auth endpoints (TODO: Implement)
 */
import {
  DynamicModule,
  MiddlewareConsumer,
  Module,
  NestModule,
} from '@nestjs/common';
import { SupertokensService } from './supertokens/supertokens.service';
import { AuthMiddleware } from './middlewares/auth.middleware';
import { AuthModuleConfig, ConfigInjectionToken } from './config.interface';
import { AuthService } from './services/auth.service';
import { AuthGuard } from './guards/auth.guard';

@Module({
  providers: [AuthService, AuthGuard],
  exports: [AuthService, AuthGuard],
  controllers: [],
})
export class AuthModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthMiddleware).forRoutes('*');
  }

  static forRoot({
    connectionURI,
    apiKey,
    appInfo,
  }: AuthModuleConfig): DynamicModule {
    return {
      module: AuthModule,
      providers: [
        {
          useValue: {
            appInfo,
            connectionURI,
            apiKey,
          },
          provide: ConfigInjectionToken,
        },
        SupertokensService,
        AuthService,
        AuthGuard,
      ],
      exports: [AuthService, AuthGuard],
      imports: [],
    };
  }
}
