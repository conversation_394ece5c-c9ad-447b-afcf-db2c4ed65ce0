import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, ManyToOne, JoinColumn, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm';
import { Job } from './job.entity';

@Entity()
@Index(['job_id', 'language'], { unique: true })
export class JobTranslation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  job_id: string;

  @Column()
  language: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string;

  @CreateDateColumn()
  created_at: Date;

  @UpdateDateColumn()
  updated_at: Date;

  @ManyToOne(() => Job, job => job.translation, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'job_id', referencedColumnName: 'ext_id' })
  job: Job;
}
