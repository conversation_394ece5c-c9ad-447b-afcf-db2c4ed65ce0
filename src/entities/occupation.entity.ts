import { Column, Entity, JoinTable, ManyToMany, OneToMany } from 'typeorm';
import { ProcessedJob } from './processed-job.entity';
import { Industry } from './industry.entity';
import { Skill } from './skill.entity';
import { BaseEntity } from './base.entity';

@Entity()
export class Occupation extends BaseEntity {
  @Column()
  preferredLabel: string;

  @Column('jsonb', { default: {} })
  preferredLabelTranslations: Record<string, string>;

  @Column({ nullable: true })
  growthStatus: string;

  @Column({ unique: true })
  code: string;

  @Column({ unique: true, nullable: true })
  conceptUri: string;

  @Column('text', { nullable: true })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  salaryLow: number;

  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  salaryHigh: number;

  @Column('simple-array', { nullable: true })
  programCodes: string[];

  @ManyToMany(() => Industry)
  @JoinTable({
    name: 'occupation_industries',
    joinColumn: { name: 'occupation_code', referencedColumnName: 'code' },
    inverseJoinColumn: { name: 'industry_code', referencedColumnName: 'code' },
  })
  industries: Industry[];

  @OneToMany(() => ProcessedJob, (job) => job.occupation)
  processed_jobs: ProcessedJob[];

  @ManyToMany(() => Skill)
  @JoinTable({
    name: 'occupation_skills',
    joinColumn: { name: 'occupation_id', referencedColumnName: 'id' },
    inverseJoinColumn: { name: 'skill_id', referencedColumnName: 'id' },
  })
  topSkills: Skill[];
}
