import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from './base.entity';

export enum TimeframeType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly'
}

@Entity()
export class UserActionSummary extends BaseEntity {
  @Column()
  @Index()
  userId: string;
  
  @Column({
    type: 'enum',
    enum: TimeframeType,
  })
  @Index()
  timeframe: TimeframeType;
  
  @Column({ type: 'date' })
  @Index()
  periodStart: Date;
  
  @Column({ nullable: true, type: 'simple-json' })
  metrics: Record<string, any>; // Contains calculated metrics like viewCount, searchCount, etc.
  
  @Column({ nullable: true, type: 'simple-json' })
  insights: Record<string, any>; // Contains derived insights
  
  @Column({ default: false })
  isProcessed: boolean; // Flag to indicate if metrics have been fully processed
  
  @Column({ nullable: true })
  lastProcessedAt: Date; // When metrics were last calculated
}