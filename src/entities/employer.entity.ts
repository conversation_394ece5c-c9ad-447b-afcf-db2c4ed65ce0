import { Column, Entity, Index, OneToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Job } from './job.entity';
import { jobMarketConstants } from '../app/jobs/services/constants/job-market.constants';
import {
  IsEmail,
  IsEnum,
  IsOptional,
  IsString,
  IsUrl,
  Length,
  Matches,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export enum CompanyType {
  STARTUP = 'startup',
  SCALEUP = 'scaleup',
  ENTERPRISE = 'enterprise',
  SME = 'sme',
  OTHER = 'other',
}

export enum CompanySize {
  MICRO = 'micro', // 1-9 employees
  SMALL = 'small', // 10-49 employees
  MEDIUM = 'medium', // 50-249 employees
  LARGE = 'large', // 250+ employees
}

@Entity()
export class Employer extends BaseEntity {
  @ApiProperty({ enum: jobMarketConstants.EmployerTypes })
  @Column({
    type: 'enum',
    enum: jobMarketConstants.EmployerTypes,
    default: jobMarketConstants.EmployerTypes.COMPANY,
  })
  @IsEnum(jobMarketConstants.EmployerTypes)
  type: (typeof jobMarketConstants.EmployerTypes)[keyof typeof jobMarketConstants.EmployerTypes];

  @ApiProperty({ required: false })
  @Column({ type: 'varchar', unique: true, nullable: true })
  @Index({ unique: true, where: '"business_id" IS NOT NULL' }) // Postgres specific
  @IsOptional()
  @IsString()
  @Matches(/^\d{7}-\d$/, {
    message: 'Business ID must be in format: 1234567-8',
  })
  business_id: string | null;

  @ApiProperty()
  @Column()
  @IsString()
  @Length(1, 255)
  name: string;

  @ApiProperty()
  @Column()
  @IsString()
  @Length(2, 3)
  language_code: string;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsUrl()
  website: string;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsEmail()
  email: string;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  phone: string;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  logoUrl: string;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsUrl()
  businessInfoUrl: string;

  @ApiProperty({ enum: CompanyType, required: false })
  @Column({
    type: 'enum',
    enum: CompanyType,
    nullable: true,
  })
  @IsOptional()
  @IsEnum(CompanyType)
  company_type: CompanyType;

  @ApiProperty({ enum: CompanySize, required: false })
  @Column({
    type: 'enum',
    enum: CompanySize,
    nullable: true,
  })
  @IsOptional()
  @IsEnum(CompanySize)
  company_size: CompanySize;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  industry: string;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty({ required: false })
  @Column({ nullable: true })
  @IsOptional()
  @IsUrl()
  logo_url: string;

  @ApiProperty({ required: false })
  @Column({ type: 'json', nullable: true })
  @IsOptional()
  social_media: Record<string, string>;

  @OneToMany(() => Job, (job) => job.employer)
  jobs: Job[];
}
