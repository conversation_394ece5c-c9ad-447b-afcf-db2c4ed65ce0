import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { CompetenceType } from './competence-type.enum';
import { Program } from './program.entity';

@Entity('competences')
export class Competence extends BaseEntity {
  @Column()
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'enum', enum: CompetenceType })
  type: CompetenceType;

  @Column({ name: 'program_id' })
  programId: number;

  @ManyToOne(() => Program, (program) => program.competences)
  @JoinColumn({ name: 'program_id' })
  program: Program;
}
