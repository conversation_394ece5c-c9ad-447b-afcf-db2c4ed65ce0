import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Job } from './job.entity';

@Entity()
@Index(['userId', 'job_ext_id'], { unique: true })
export class ForYou extends BaseEntity {
  @Column()
  userId: string;

  @Column({ type: 'varchar' })
  job_ext_id: string;

  @ManyToOne(() => Job)
  @JoinColumn({ name: 'job_ext_id', referencedColumnName: 'ext_id' })
  job: Job;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  recommended_at: Date;

  @Column({ type: 'boolean', default: false })
  is_viewed: boolean;

  @Column({ type: 'boolean', nullable: true })
  is_liked: boolean;

  // Any other important meta date about the user
  @Column({ type: 'jsonb', nullable: true })
  classification_data: Record<string, any>;

  // List of up to 5 reasons why the job is recommended
  @Column({ type: 'text', array: true, nullable: true })
  why: string[];
  
  // Score between 0-100 indicating how well the job aligns with the user's profile and preferences
  @Column({ type: 'integer', nullable: true })
  score: number;
}
