import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne } from 'typeorm';

import { Job } from './job.entity';
import { Industry } from './industry.entity';
import { Occupation } from './occupation.entity';
import { BaseEntity } from './base.entity';
import { JobSeniority } from './job-seniority.enum';

// Define interfaces for the new JSON structures
export interface ContactItem {
  [key: string]: string;
}

export interface Contact {
  phone: string[];
  email: ContactItem[];
  links: string[];
  other: ContactItem[];
}

export interface LanguageRequirement {
  language: string;
  proficiency: string;
}

export interface Requirements {
  languages: LanguageRequirement[];
  certifications: string[];
}

export interface RoleImpactSummary {
  role_impact_summary: string;
  collaboration_focus?: string;
  hiring_urgency_hint?: boolean;
  is_career_switcher_friendly?: boolean;
  is_student_friendly?: boolean;
  learning_opportunity_emphasis?: boolean;
  travel_required?: string;
  work_pace?: string;
}

@Entity()
export class ProcessedJob extends BaseEntity {
  @Column({ unique: true })
  job_id: string;

  @Column({ default: 'Tyomarkkinatori' })
  source: string;

  @Column({ nullable: true })
  elevator_pitch: string;

  @Column()
  industry_code: string;

  @Column({ nullable: true })
  occupation_code: string;

  @Column({ nullable: true })
  occupation_code_secondary: string;

  @Column({ nullable: true })
  occupation_code_tertiary: string;

  @Column('json', { default: [] })
  skills: string[];

  @Column('json', { nullable: true })
  salary: {
    salary_lower_bound: number;
    salary_upper_bound: number;
    period: string;
  };

  @Column({ nullable: true })
  application_url: string;

  @Column({ type: 'text', nullable: true })
  summary: string;

  @Column({ default: false })
  degree_required: boolean;

  @Column({ default: false })
  internship: boolean;

  @Column({
    type: 'enum',
    enum: JobSeniority,
    default: JobSeniority.NONE,
  })
  seniority: JobSeniority;

  @Column('json', { default: [] })
  primary_tasks: string[];

  @Column('json', { default: [] })
  keywords: string[];

  @Column('json', { default: [] })
  skill_contexts: string[];

  @Column('json', { default: [] })
  expected_outcomes: string[];

  @Column('json', { default: [] })
  possible_career_outcomes: string[];

  @Column('json', { default: [] })
  typical_day_tasks: string[];

  @Column('json', { default: [] })
  culture_keywords: string[];

  @Column('json', { default: [] })
  ideal_candidate_traits: string[];

  @Column('json', { default: [] })
  work_life_balance_signals: string[];

  @Column('json', { nullable: true })
  role_impact_summary: RoleImpactSummary;

  @Column('json', { nullable: true })
  contacts: Contact;

  @Column('json', { nullable: true })
  requirements: Requirements;

  @Column('json', { nullable: true })
  market_salary: {
    job_role: string;
    median_salary: number;
    salary_lower_bound: number;
    salary_upper_bound: number;
    currency: string;
    salary_period: string;
    data_source: string;
    data_reliability: string;
  };

  @Column('json', { nullable: true })
  evolution: {
    evolution_summary: string;
    automation_impact: string;
    relevance_prediction: string;
  };

  @OneToOne(() => Job, (job) => job.processedJob)
  @JoinColumn({ name: 'job_id', referencedColumnName: 'ext_id' })
  job: Job;

  @ManyToOne(() => Industry, (industry) => industry.processed_jobs)
  @JoinColumn({ name: 'industry_code', referencedColumnName: 'code' })
  industry: Industry;

  @ManyToOne(() => Occupation, (occupation) => occupation.processed_jobs)
  @JoinColumn({ name: 'occupation_code', referencedColumnName: 'code' })
  occupation: Occupation;

  @ManyToOne(() => Occupation)
  @JoinColumn({
    name: 'occupation_code_secondary',
    referencedColumnName: 'code',
  })
  occupation_secondary: Occupation;

  @ManyToOne(() => Occupation)
  @JoinColumn({
    name: 'occupation_code_tertiary',
    referencedColumnName: 'code',
  })
  occupation_tertiary: Occupation;
}
