import { Column, Entity } from 'typeorm';
import { BaseEntity } from './base.entity';
import { IsNotEmpty, IsNumber, IsString, MaxLength } from 'class-validator';

@Entity('municipalities')
export class Municipality extends BaseEntity {
  @Column({ unique: true })
  @IsNotEmpty()
  @IsString()
  @MaxLength(10)
  code: string;

  @Column()
  @IsNotEmpty()
  @IsString()
  @MaxLength(255)
  classificationName: string;

  @Column()
  @IsNotEmpty()
  @IsNumber()
  level: number;
}
