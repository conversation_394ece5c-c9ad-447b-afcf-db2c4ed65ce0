import { Entity, Column, Index, ManyToOne, JoinColumn } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Job } from './job.entity';

@Entity()
@Index(['userId', 'job_ext_id'], { unique: true })
export class SavedJob extends BaseEntity {
  @Column()
  userId: string;

  @Column({ type: 'varchar' })
  job_ext_id: string;

  @ManyToOne(() => Job)
  @JoinColumn({ name: 'job_ext_id', referencedColumnName: 'ext_id' })
  job: Job;
}
