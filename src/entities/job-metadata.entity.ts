import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from './base.entity';

@Entity('job_metadata')
@Index(['ext_id', 'key'], { unique: true })
export class JobMetadata extends BaseEntity {
  @Column({ name: 'ext_id' })
  ext_id: string;

  @Column()
  key: string;

  @Column('jsonb')
  value: any;

  @Column({ nullable: true })
  source: string; // e.g., 'ai_extraction', 'api', 'manual'
}