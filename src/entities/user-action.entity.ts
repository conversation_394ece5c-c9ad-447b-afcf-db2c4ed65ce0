import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from './base.entity';

export enum UserActionType {
  SEARCH = 'search',
  VIEW = 'view',
  SAVE = 'save',
  UNSAVE = 'unsave',
  APPLY = 'apply',
  RECOMMENDATION_SHOWN = 'recommendation_shown',
  RECOMMENDATION_CLICKED = 'recommendation_clicked',
}

export enum UserActionSource {
  SEARCH = 'search',
  FOR_YOU = 'for_you',
  BROWSE = 'browse',
  EMAIL = 'email',
}

@Entity()
export class UserAction extends BaseEntity {
  @Column()
  @Index()
  userId: string;
  
  @Column({ nullable: true })
  @Index()
  job_ext_id: string;
  
  @Column({
    type: 'enum',
    enum: UserActionType,
  })
  @Index()
  actionType: UserActionType;
  
  @Column({ nullable: true, type: 'simple-json' })
  metadata: Record<string, any>; // For search terms, view duration, filters, etc.
  
  @Column({
    nullable: true,
    type: 'enum',
    enum: UserActionSource,
  })
  source: UserActionSource;
  
  @Column({ nullable: true })
  correlationId: string; // To trace connections between actions
}
