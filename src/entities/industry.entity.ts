import { Column, <PERSON>tity, ManyToMany, OneToMany } from 'typeorm';
import { ProcessedJob } from './processed-job.entity';
import { Occupation } from './occupation.entity';
import { BaseEntity } from './base.entity';

@Entity()
export class Industry extends BaseEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column({ type: 'json', nullable: true })
  translations: {
    ru?: string;
    sv?: string;
    fi?: string;
  };

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true, type: 'timestamp' })
  last_analyzed_date: Date;

  @ManyToMany(() => Occupation, (occupation) => occupation.industries)
  occupations: Occupation[];

  @OneToMany(() => ProcessedJob, (job) => job.industry)
  processed_jobs: ProcessedJob[];
}
