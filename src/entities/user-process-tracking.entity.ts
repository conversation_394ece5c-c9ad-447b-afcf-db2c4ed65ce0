import { Column, Entity } from 'typeorm';
import { BaseEntity } from './base.entity';

export enum ProcessEntityType {
  FOR_YOU = 'for_you',
  SMART_PROFILE = 'smart_profile',
  RESUME_PARSING = 'resume_parsing',
}

export enum ProcessStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

/**
 * Entity for tracking processes running for users
 * 
 * Used to track long-running processes that continue in the background
 * after a request returns to the client
 */
@Entity('user_process_tracking')
export class UserProcessTracking extends BaseEntity {
  @Column()
  userId: string;

  @Column({
    type: 'enum',
    enum: ProcessEntityType,
  })
  entityType: ProcessEntityType;

  @Column({
    type: 'enum',
    enum: ProcessStatus,
    default: ProcessStatus.PENDING,
  })
  status: ProcessStatus;

  @Column({
    type: 'jsonb',
    nullable: true,
  })
  metadata: Record<string, any>;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  startedAt: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  completedAt: Date;

  @Column({
    nullable: true,
  })
  errorMessage?: string;
}
