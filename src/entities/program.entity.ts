import { <PERSON><PERSON><PERSON>, Column, <PERSON>ToMany } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Competence } from './competence.entity';

@Entity('programs')
export class Program extends BaseEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  degree_name: string;

  @Column()
  degree_title: string;

  @OneToMany(() => Competence, (competence) => competence.program)
  competences: Competence[];
}
