import { Column, Entity, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { TimeframeType } from './user-action-summary.entity';

@Entity()
export class OrganizationActionSummary extends BaseEntity {
  @Column()
  @Index()
  organizationId: string;
  
  @Column({
    type: 'enum',
    enum: TimeframeType,
  })
  @Index()
  timeframe: TimeframeType;
  
  @Column({ type: 'date' })
  @Index()
  periodStart: Date;
  
  @Column({ nullable: true, type: 'simple-json' })
  metrics: Record<string, any>; // Contains aggregate metrics for org
  
  @Column({ type: 'integer', default: 0 })
  memberCount: number; // Count of active members in the org
  
  @Column({ default: false })
  isProcessed: boolean; // Flag to indicate if metrics have been fully processed
  
  @Column({ nullable: true })
  lastProcessedAt: Date; // When metrics were last calculated
}