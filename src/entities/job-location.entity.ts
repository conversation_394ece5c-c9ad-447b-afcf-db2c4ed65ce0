import {
  Column,
  <PERSON>ti<PERSON>,
  Join<PERSON><PERSON>umn,
  JoinT<PERSON>,
  ManyToMany,
  OneToOne,
} from 'typeorm';
import { Job } from './job.entity';
import { BaseEntity } from './base.entity';
import { Municipality } from './municipality.entity';
import { Region } from './region.entity';

@Entity()
export class JobLocation extends BaseEntity {
  @Column({ unique: true })
  job_id: string;

  @Column()
  flexible: boolean;

  @Column('simple-array')
  country: string[];

  @ManyToMany(() => Region)
  @JoinTable({
    name: 'job_location_regions',
    joinColumn: {
      name: 'job_location_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'region_code',
      referencedColumnName: 'code',
    },
  })
  regions: Region[];

  @ManyToMany(() => Municipality)
  @JoinTable({
    name: 'job_location_municipalities',
    joinColumn: {
      name: 'job_location_id',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'municipality_code',
      referencedColumnName: 'code',
    },
  })
  municipalities: Municipality[];

  @OneToOne(() => Job, (job) => job.location)
  @JoinColumn({ name: 'job_id', referencedColumnName: 'ext_id' })
  job: Job;
}
