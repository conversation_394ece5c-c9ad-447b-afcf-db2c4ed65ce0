import {
  <PERSON>umn,
  <PERSON><PERSON><PERSON>,
  Index,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  OneToMany,
  OneToOne,
} from 'typeorm';
import { JobLocation } from './job-location.entity';
import { JobTranslation } from './job-translation.entity';
import { ProcessedJob } from './processed-job.entity';
import { Employer } from './employer.entity';
import { BaseEntity } from './base.entity';

@Entity()
export class Job extends BaseEntity {
  @Column({ unique: true })
  @Index({ unique: true })
  ext_id: string;

  @Column({ nullable: true })
  employer_name: string;

  @ManyToOne(() => Employer, (employer) => employer.jobs, { nullable: true })
  @JoinColumn({ name: 'employer_id' })
  employer: Employer;

  @Column({ nullable: true })
  employer_id: number;

  @Column('simple-array')
  languages: string[];

  @Column()
  working_time: string;

  @Column()
  continuity: string;

  @Column({ nullable: true })
  working_hours: string;

  @Column({ nullable: true })
  workplace_flexibility: string;

  @Column({ nullable: true, type: 'simple-array', default: null })
  driver_license_codes: string[];

  @Column({ nullable: true })
  salary_type: string;

  @Column({ nullable: true })
  work_begins: string;

  @Column({ nullable: true })
  shift_type: string;

  @Column({ nullable: true })
  part_time_hours: string;

  @Column({ nullable: true, type: 'timestamp' })
  expires_at: Date;

  @Column({ default: false })
  expired: boolean;

  @Column({ nullable: true })
  title: string;

  @Column({ nullable: true, type: 'text' })
  description: string;

  @Column({ nullable: true })
  application_url: string;

  @Column({ default: 'Tyomarkkinatori' })
  source: string;

  @Column({ nullable: true })
  last_import_date: Date;

  @Column({ nullable: true })
  published_date: Date;

  // Job processing status
  @Column({ default: false })
  processed: boolean;

  @Column({ default: false })
  queued_for_processing: boolean;

  @Column({ nullable: true })
  processing_batch_id: string;

  @Column({ default: false })
  pinecone_uploaded: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  pinecone_uploaded_at: Date;

  @OneToOne(() => JobLocation, (location) => location.job)
  location: JobLocation;

  @OneToMany(() => JobTranslation, (translation) => translation.job)
  translation: JobTranslation[];

  @OneToOne(() => ProcessedJob, (processed) => processed.job)
  processedJob: ProcessedJob;
}
