import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Index } from 'typeorm';
import { BaseEntity } from './base.entity';
import { Competence } from './competence.entity';

@Entity('user_competences')
export class UserCompetence extends BaseEntity {
  @Column()
  @Index()
  supertokens_id: string;

  @Column({ name: 'competence_id' })
  competenceId: number;

  @ManyToOne(() => Competence)
  @JoinColumn({ name: 'competence_id' })
  competence: Competence;
}
