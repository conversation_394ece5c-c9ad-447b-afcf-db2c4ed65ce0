import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Language } from '../app/shared/enums/languages.enum';

@Entity()
@Index(['textHash', 'targetLanguage'], { unique: true })
export class LocalTranslation {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @Index()
  textHash: string;

  @Column('text')
  originalText: string;

  @Column({
    type: 'enum',
    enum: Language,
    default: Language.EN,
  })
  targetLanguage: Language;

  @Column('text')
  translatedText: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
