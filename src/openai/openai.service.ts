import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { CacheService } from '../cache/cache.service';

@Injectable()
export class OpenAIService implements OnModuleInit {
  private readonly logger = new Logger(OpenAIService.name);
  private openaiClient: OpenAI;

  // Default embedding models
  private readonly defaultEmbeddingModel = 'text-embedding-3-large'; // 1536 dimensions
  private readonly smallEmbeddingModel = 'text-embedding-3-small'; // 3072 dimensions

  // Cache settings
  private readonly CACHE_PREFIX = 'openai:embedding:';
  private readonly CACHE_TTL = 24 * 60 * 60; // 24 hours in seconds

  constructor(
    private configService: ConfigService,
    private readonly cacheService?: CacheService,
  ) {}

  async onModuleInit() {
    await this.initOpenAI();
  }

  private async initOpenAI() {
    try {
      const apiKey = this.configService.get<string>('OPENAI_API_KEY');

      if (!apiKey) {
        this.logger.error(
          'OpenAI API key is not defined in environment variables',
        );
        throw new Error('OPENAI_API_KEY is required');
      }

      // Initialize the OpenAI client
      this.openaiClient = new OpenAI({
        apiKey,
      });

      this.logger.log('OpenAI client initialized successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize OpenAI client: ${error.message}`);
      // For MVP, allowing the application to continue even if OpenAI initialization fails
      // This should be addressed in production with proper error handling
    }
  }

  /**
   * Get OpenAI client instance
   */
  public getOpenAIClient(): OpenAI {
    if (!this.openaiClient) {
      this.logger.warn('Attempting to use OpenAI client before initialization');
    }
    return this.openaiClient;
  }

  /**
   * Create embeddings for the provided text
   * @param text The text to create embeddings for (can be a string or array of strings)
   * @param model Optional: The model to use for creating embeddings
   * @param dimensions Optional: The dimensions of the embeddings to return
   * @param useCache Optional: Whether to use cache for embeddings (default: true)
   * @returns An array of embeddings
   */
  async createEmbeddings(
    text: string | string[],
    model: string = this.defaultEmbeddingModel,
    dimensions: number = 3072,
    useCache: boolean = false,
  ): Promise<number[][]> {
    try {
      this.logger.log(
        `Starting to generate embeddings with model: ${model}, dimensions: ${dimensions}, useCache: ${useCache}`,
      );

      if (!this.openaiClient) {
        throw new Error('OpenAI client not initialized');
      }

      const input = Array.isArray(text) ? text : [text];

      // Handle empty array
      if (input.length === 0) {
        return [];
      }

      const results: number[][] = [];

      // Process each text input, potentially using cache
      for (const item of input) {
        // Check cache if enabled and available
        if (useCache && this.cacheService) {
          const cacheKey = `${this.CACHE_PREFIX}${model}:${dimensions || 'default'}:${item.substring(0, 100)}`;
          const cachedEmbedding =
            await this.cacheService.get<number[]>(cacheKey);

          if (cachedEmbedding) {
            this.logger.debug('Using cached embedding');
            results.push(cachedEmbedding);
            continue;
          }
        }

        // No cache hit, generate embedding
        // Create embeddings request parameters
        const params: OpenAI.EmbeddingCreateParams = {
          model,
          input: item,
        };

        // Add dimensions if specified
        if (dimensions) {
          params.dimensions = dimensions;
        }

        const response = await this.openaiClient.embeddings.create(params);
        const embedding = response.data[0].embedding;
        results.push(embedding);

        // Cache the result if caching is enabled
        if (useCache && this.cacheService) {
          const cacheKey = `${this.CACHE_PREFIX}${model}:${dimensions || 'default'}:${item.substring(0, 100)}`;
          await this.cacheService.set(cacheKey, embedding, this.CACHE_TTL);
        }
      }

      return results;
    } catch (error) {
      this.logger.error(`Error creating embeddings: ${error.message}`);
      throw error;
    }
  }

  /**
   * Create a single embedding for the provided text
   * @param text The text to create an embedding for
   * @param model Optional: The model to use for creating embeddings
   * @param dimensions Optional: The dimensions of the embedding to return
   * @param useCache Optional: Whether to use cache for embeddings (default: true)
   * @returns A single embedding vector
   */
  async createEmbedding(
    text: string,
    model: string = this.defaultEmbeddingModel,
    dimensions: number = 3072,
    useCache: boolean = false,
  ): Promise<number[]> {
    const embeddings = await this.createEmbeddings(
      text,
      model,
      dimensions,
      useCache,
    );
    return embeddings[0];
  }

  /**
   * Create an embedding specifically for a query
   * This is useful when using asymmetric embedding models where queries and documents
   * should be embedded differently
   * @param query The query text
   * @param dimensions Optional: The dimensions of the embedding to return
   * @param useCache Optional: Whether to use cache for embeddings (default: true)
   * @returns A query embedding vector
   */
  async createQueryEmbedding(
    query: string,
    dimensions?: number,
    useCache: boolean = false,
  ): Promise<number[]> {
    return this.createEmbedding(
      query,
      this.defaultEmbeddingModel,
      dimensions,
      useCache,
    );
  }

  /**
   * Create an embedding specifically for a document/content
   * Uses the large embedding model for better representational capacity
   * @param content The document/content text
   * @param dimensions Optional: The dimensions of the embedding to return
   * @param useCache Optional: Whether to use cache for embeddings (default: true)
   * @returns A document embedding vector
   */
  async createDocumentEmbedding(
    content: string,
    dimensions?: number,
    useCache: boolean = false,
  ): Promise<number[]> {
    return this.createEmbedding(
      content,
      this.defaultEmbeddingModel,
      dimensions,
      useCache,
    );
  }

  /**
   * Creates a weighted combined embedding from multiple text fields
   * Useful for job or profile embeddings that combine multiple attributes
   * @param fields Object containing named fields with corresponding weights
   * @param dimensions Optional: The dimensions for output embedding
   * @returns Combined weighted embedding vector
   */
  async createCombinedEmbedding(
    fields: Record<string, { text: string | string[]; weight: number }>,
    dimensions?: number,
  ): Promise<number[]> {
    try {
      // Initialize empty combined vector
      let combinedVector: number[] = [];
      let totalWeight = 0;

      for (const [fieldName, { text, weight }] of Object.entries(fields)) {
        if (!text || (Array.isArray(text) && text.length === 0)) {
          continue; // Skip empty fields
        }

        // Handle array of strings by joining
        const fieldText = Array.isArray(text) ? text.join(' ') : text;
        if (!fieldText.trim()) continue;

        this.logger.debug(`Creating embedding for field: ${fieldName}`);

        // Get embedding for this field
        const fieldEmbedding = await this.createEmbedding(
          fieldText,
          this.defaultEmbeddingModel,
          dimensions,
        );

        // Initialize combined vector if first valid field
        if (combinedVector.length === 0) {
          combinedVector = new Array(fieldEmbedding.length).fill(0);
        }

        // Add weighted component to combined vector
        for (let i = 0; i < combinedVector.length; i++) {
          combinedVector[i] += fieldEmbedding[i] * weight;
        }

        totalWeight += weight;
      }

      // Normalize by total weight
      if (totalWeight > 0) {
        for (let i = 0; i < combinedVector.length; i++) {
          combinedVector[i] /= totalWeight;
        }
      } else {
        this.logger.warn(
          'No valid fields with weights found for combined embedding',
        );
        return []; // Return empty if no fields were processed
      }

      // Normalize the vector to unit length
      const magnitude = Math.sqrt(
        combinedVector.reduce((sum, val) => sum + val * val, 0),
      );

      if (magnitude > 0) {
        for (let i = 0; i < combinedVector.length; i++) {
          combinedVector[i] /= magnitude;
        }
      }

      return combinedVector;
    } catch (error) {
      this.logger.error(`Error creating combined embedding: ${error.message}`);
      throw error;
    }
  }
}
