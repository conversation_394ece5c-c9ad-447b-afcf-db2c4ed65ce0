# OpenAI Service

This module provides an integration with OpenAI's API, primarily focusing on creating embeddings for text data.

## Configuration

Add your OpenAI API key to your environment variables:

```
OPENAI_API_KEY=your_api_key_here
```

## Usage Examples

### Creating Embeddings

```typescript
import { OpenAIService } from './openai/openai.service';

// Inject the service
constructor(private readonly openaiService: OpenAIService) {}

// Create embeddings for a single text
async function createSingleEmbedding() {
  const text = 'Software engineer with 5 years of experience';
  const embedding = await this.openaiService.createEmbedding(text);
  console.log(`Embedding dimension: ${embedding.length}`);
  return embedding;
}

// Create embeddings for multiple texts
async function createMultipleEmbeddings() {
  const texts = [
    'Software engineer with 5 years of experience',
    'Data scientist specializing in machine learning',
    'UX designer with expertise in user research'
  ];
  const embeddings = await this.openaiService.createEmbeddings(texts);
  console.log(`Number of embeddings: ${embeddings.length}`);
  console.log(`Embedding dimension: ${embeddings[0].length}`);
  return embeddings;
}

// Create embeddings with custom model and dimensions
async function createCustomEmbedding() {
  const text = 'Software engineer with 5 years of experience';
  const model = 'text-embedding-3-large'; // Use larger model
  const dimensions = 1024; // Output 1024-dimensional vectors
  const embedding = await this.openaiService.createEmbedding(text, model, dimensions);
  console.log(`Custom embedding dimension: ${embedding.length}`);
  return embedding;
}
```

## Integration with Pinecone

The OpenAI embeddings can be stored in Pinecone for efficient vector search:

```typescript
import { OpenAIService } from './openai/openai.service';
import { PineconeService } from './pinecone/pinecone.service';

// Inject both services
constructor(
  private readonly openaiService: OpenAIService,
  private readonly pineconeService: PineconeService,
) {}

// Store embeddings in Pinecone
async function storeEmbeddingsInPinecone() {
  // Generate embedding
  const jobDescription = 'Software engineer with expertise in Node.js and TypeScript';
  const embedding = await this.openaiService.createEmbedding(jobDescription);
  
  // Get Pinecone index
  const index = await this.pineconeService.getDefaultIndex();
  
  // Store in Pinecone
  await index.upsert([
    {
      id: 'job-123', // Unique ID for the vector
      values: embedding,
      metadata: {
        type: 'job',
        description: jobDescription,
        title: 'Senior Software Engineer',
      },
    },
  ]);
  
  console.log('Embedding stored in Pinecone');
}

// Query similar jobs
async function findSimilarJobs(query: string) {
  // Generate embedding for the query
  const queryEmbedding = await this.openaiService.createEmbedding(query);
  
  // Get Pinecone index
  const index = await this.pineconeService.getDefaultIndex();
  
  // Query Pinecone
  const results = await index.query({
    vector: queryEmbedding,
    topK: 10,
    includeMetadata: true,
  });
  
  console.log(`Found ${results.matches.length} similar jobs`);
  return results.matches;
}
```

## Available Models

- `text-embedding-3-small` (default) - 1536 dimensions
- `text-embedding-3-large` - 3072 dimensions

You can specify dimensions to truncate the vectors to a smaller size.
