import { Injectable, Logger } from '@nestjs/common';
import { OpenAIService } from '../openai.service';
import { PineconeService } from '../../pinecone/pinecone.service';

/**
 * This is an example service showing how to use the OpenAI embeddings
 * with Pinecone for job recommendation features.
 * 
 * NOTE: This is just an example and not meant to be used in production.
 */
@Injectable()
export class JobEmbeddingService {
  private readonly logger = new Logger(JobEmbeddingService.name);

  constructor(
    private readonly openaiService: OpenAIService,
    private readonly pineconeService: PineconeService,
  ) {}

  /**
   * Generate embeddings for a job description
   */
  async generateJobEmbedding(jobDescription: string): Promise<number[]> {
    return this.openaiService.createDocumentEmbedding(jobDescription);
  }

  /**
   * Generate embeddings for a user query
   */
  async generateQueryEmbedding(query: string): Promise<number[]> {
    return this.openaiService.createQueryEmbedding(query);
  }

  /**
   * Store a job embedding in Pinecone
   */
  async storeJobEmbedding(
    jobId: string,
    jobTitle: string,
    jobDescription: string,
    skills: string[] = [],
    location: string = '',
    employerName: string = '',
  ): Promise<void> {
    try {
      // Use combined embedding approach for richer representation
      const embedding = await this.openaiService.createCombinedEmbedding({
        title: { text: jobTitle, weight: 2.0 },
        description: { text: jobDescription, weight: 1.5 },
        skills: { text: skills, weight: 1.0 },
        location: { text: location, weight: 0.5 },
        employer: { text: employerName, weight: 0.5 },
      });
      
      // Get the Pinecone index
      const index = await this.pineconeService.getDefaultIndex();
      
      // Store the embedding in Pinecone
      await index.upsert([
        {
          id: `job-${jobId}`,
          values: embedding,
          metadata: {
            type: 'job',
            id: jobId,
            title: jobTitle,
            description: jobDescription.substring(0, 500), // Truncate for metadata
            skills: skills.join(', '),
            location,
            employer: employerName,
            timestamp: new Date().toISOString(),
          },
        },
      ]);

      this.logger.log(`Successfully stored embedding for job ${jobId}`);
    } catch (error) {
      this.logger.error(`Failed to store job embedding: ${error.message}`);
      throw error;
    }
  }

  /**
   * Store a user profile embedding in Pinecone
   */
  async storeUserProfileEmbedding(
    userId: string,
    resumeText: string,
    skills: string[] = [],
    preferredLocations: string[] = [],
    jobTitles: string[] = [],
  ): Promise<void> {
    try {
      // Use combined embedding for user profile
      const embedding = await this.openaiService.createCombinedEmbedding({
        resume: { text: resumeText, weight: 2.0 },
        skills: { text: skills, weight: 1.5 },
        jobTitles: { text: jobTitles, weight: 1.2 },
        locations: { text: preferredLocations, weight: 0.8 },
      });
      
      // Get the Pinecone index
      const index = await this.pineconeService.getDefaultIndex();
      
      // Store the embedding in Pinecone
      await index.upsert([
        {
          id: `user-${userId}`,
          values: embedding,
          metadata: {
            type: 'user',
            id: userId,
            skills: skills.join(', '),
            locations: preferredLocations.join(', '),
            jobTitles: jobTitles.join(', '),
            timestamp: new Date().toISOString(),
          },
        },
      ]);

      this.logger.log(`Successfully stored embedding for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to store user profile embedding: ${error.message}`);
      throw error;
    }
  }

  /**
   * Find similar jobs based on a query
   */
  async findSimilarJobs(query: string, limit: number = 10) {
    try {
      // Generate embedding for the query using query-optimized model
      const queryEmbedding = await this.generateQueryEmbedding(query);
      
      // Get the Pinecone index
      const index = await this.pineconeService.getDefaultIndex();
      
      // Query Pinecone for similar jobs
      const results = await index.query({
        vector: queryEmbedding,
        topK: limit,
        includeMetadata: true,
        filter: {
          type: 'job',
        },
      });
      
      this.logger.log(`Found ${results.matches.length} jobs matching query: "${query.substring(0, 50)}..."`);
      
      return results.matches.map(match => ({
        id: match.metadata?.id || 'unknown',
        title: match.metadata?.title || 'Unknown Title',
        description: match.metadata?.description || '',
        skills: match.metadata?.skills || '',
        location: match.metadata?.location || '',
        employer: match.metadata?.employer || '',
        score: match.score,
      }));
    } catch (error) {
      this.logger.error(`Error finding similar jobs: ${error.message}`);
      return [];
    }
  }

  /**
   * Find jobs matching a user profile
   */
  async matchJobsToUserProfile(userId: string, limit: number = 10) {
    try {
      // Get the Pinecone index
      const index = await this.pineconeService.getDefaultIndex();
      
      // First, fetch the user's embedding
      const userResults = await index.query({
        id: `user-${userId}`,
        topK: 1,
        includeValues: true,
      });
      
      if (!userResults.matches || userResults.matches.length === 0) {
        this.logger.warn(`User profile not found for user ${userId}`);
        return [];
      }
      
      const userMatch = userResults.matches[0];
      // Get the user's embedding vector
      if (!userMatch.values) {
        this.logger.warn(`No embedding values found for user ${userId}`);
        return [];
      }
      
      const userEmbedding = userMatch.values;
      
      // Query Pinecone for matching jobs using the user's embedding
      const results = await index.query({
        vector: userEmbedding,
        topK: limit,
        includeMetadata: true,
        filter: {
          type: 'job',
        },
      });
      
      this.logger.log(`Found ${results.matches.length} jobs matching user profile for user ${userId}`);
      
      return results.matches.map(match => ({
        id: match.metadata?.id || 'unknown',
        title: match.metadata?.title || 'Unknown Title',
        description: match.metadata?.description || '',
        skills: match.metadata?.skills || '',
        location: match.metadata?.location || '',
        employer: match.metadata?.employer || '',
        score: match.score,
      }));
    } catch (error) {
      this.logger.error(`Error matching jobs to user profile: ${error.message}`);
      return [];
    }
  }

  /**
   * Get job recommendations based on user's interaction history
   * This combines collaborative filtering principles with embedding similarity
   */
  async getJobRecommendations(userId: string, limit: number = 10) {
    try {
      // 1. Fetch user's liked/applied jobs from a hypothetical repository
      // const userInteractions = await this.userJobInteractionRepository.findByUserId(userId);
      // const likedJobIds = userInteractions.filter(i => i.action === 'like' || i.action === 'apply').map(i => i.jobId);
      
      // For demo purposes, we'll just use some example job IDs
      const likedJobIds = ['job-123', 'job-456', 'job-789'];
      
      if (likedJobIds.length === 0) {
        // Fall back to profile-based matching if no interaction history
        return this.matchJobsToUserProfile(userId, limit);
      }
      
      // 2. Get embeddings for the liked jobs
      const index = await this.pineconeService.getDefaultIndex();
      const likedJobsResults = await Promise.all(
        likedJobIds.map(async (jobId) => {
          const result = await index.query({
            id: jobId,
            topK: 1,
            includeValues: true,
          });
          return result.matches && result.matches.length > 0 ? result.matches[0] : null;
        })
      );
      
      // Type definition to ensure we have values
      interface JobWithValues {
        id: string;
        values: number[];
        [key: string]: any;
      }
      
      // 3. Calculate the centroid of liked job embeddings
      // Filter out nulls and ensure values exist
      const validJobs = likedJobsResults
        .filter((job): job is NonNullable<typeof job> => job !== null)
        .filter((job): job is JobWithValues => 
          Array.isArray(job.values) && job.values.length > 0
        );
      
      if (validJobs.length === 0) {
        this.logger.warn('No valid embeddings found for liked jobs');
        return this.matchJobsToUserProfile(userId, limit);
      }
      
      // Calculate average embedding (centroid)
      const dimensions = validJobs[0].values.length;
      const centroid = new Array(dimensions).fill(0);
      
      // Sum up all values
      validJobs.forEach(job => {
        const values = job.values;
        for (let i = 0; i < dimensions; i++) {
          centroid[i] += values[i] / validJobs.length;
        }
      });
      
      // Normalize the centroid
      const magnitude = Math.sqrt(centroid.reduce((sum, val) => sum + val * val, 0));
      for (let i = 0; i < dimensions; i++) {
        centroid[i] /= magnitude;
      }
      
      // 4. Find similar jobs to this centroid, excluding already interacted jobs
      const results = await index.query({
        vector: centroid,
        topK: limit + likedJobIds.length, // Get extra results to account for filtering
        includeMetadata: true,
        filter: {
          type: 'job',
        },
      });
      
      // 5. Filter out jobs the user has already interacted with
      const recommendations = results.matches
        .filter(match => !likedJobIds.includes(match.id))
        .slice(0, limit)
        .map(match => ({
          id: match.metadata?.id || 'unknown',
          title: match.metadata?.title || 'Unknown Title',
          description: match.metadata?.description || '',
          skills: match.metadata?.skills || '',
          location: match.metadata?.location || '',
          employer: match.metadata?.employer || '',
          score: match.score,
        }));
      
      this.logger.log(`Generated ${recommendations.length} job recommendations for user ${userId}`);
      return recommendations;
    } catch (error) {
      this.logger.error(`Error getting job recommendations: ${error.message}`);
      return [];
    }
  }
}
