import { DataSource, DataSourceOptions } from 'typeorm';
import * as dotenv from 'dotenv';
import { runSeeds } from './index';
import { Municipality } from '../entities/municipality.entity';
import { Region } from '../entities/region.entity';

// Load environment variables
dotenv.config();

// Define the entities to include
const entities = [
  Municipality,
  Region,
  // Add other entities as needed
];

// Create a temporary data source for seeding
const dataSourceOptions: DataSourceOptions = {
  type: 'postgres',
  url: process.env.DATABASE_URL,
  schema: process.env.DATABASE_SCHEMA,
  entities,
  synchronize: false,
  logging: true,
};

async function main() {
  try {
    console.log('Creating data source...');
    const dataSource = new DataSource(dataSourceOptions);
    await dataSource.initialize();

    console.log('Running seeds...');
    await runSeeds(dataSource);

    await dataSource.destroy();
    console.log('Seed process completed successfully.');
    process.exit(0);
  } catch (error) {
    console.error('Error during Data Source initialization or seeding:', error);
    process.exit(1);
  }
}

main();
