import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';
import { Municipality } from '../entities/municipality.entity';

interface MunicipalityData {
  code: string;
  level: number;
  classificationName: string;
}

/**
 * Seed municipalities from CSV file
 *
 * This seed file reads municipality data from the provided CSV file
 * and inserts it into the database.
 */
export const seedMunicipalities = async (
  dataSource: DataSource,
): Promise<void> => {
  const municipalityRepository = dataSource.getRepository(Municipality);

  // Check if municipalities already exist to avoid duplicate seeding
  const existingCount = await municipalityRepository.count();
  if (existingCount > 0) {
    console.log(
      `Municipalities already seeded (${existingCount} records found). Skipping...`,
    );
    return;
  }

  const municipalities: MunicipalityData[] = [];

  // Read the CSV file
  const csvFilePath = path.resolve(
    process.cwd(),
    'src/seeds/kunta_1_20250101.csv',
  );

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(
        csv({
          separator: ';',
          mapHeaders: ({ header }) => header.trim(),
          mapValues: ({ value }) => value.replace(/^'|'$/g, '').trim(), // Remove quotes and trim
        }),
      )
      .on('data', (data: any) => {
        municipalities.push({
          code: data.code,
          level: parseInt(data.level, 10),
          classificationName: data.classificationName,
        });
      })
      .on('end', async () => {
        try {
          // Insert municipalities in batches
          console.log(`Seeding ${municipalities.length} municipalities...`);

          // Create entities
          const municipalityEntities = municipalities.map((data) =>
            municipalityRepository.create(data),
          );

          // Save all at once
          await municipalityRepository.save(municipalityEntities);

          console.log('Municipality seeding completed successfully.');
          resolve();
        } catch (error) {
          console.error('Error seeding municipalities:', error);
          reject(error);
        }
      })
      .on('error', (error) => {
        console.error('Error reading municipality CSV:', error);
        reject(error);
      });
  });
};
