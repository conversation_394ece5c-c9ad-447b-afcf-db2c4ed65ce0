import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';
import { Region } from '../entities/region.entity';

interface RegionData {
  code: string;
  level: number;
  classificationName: string;
}

/**
 * Seed regions from CSV file
 *
 * This seed file reads region data from the provided CSV file
 * and inserts it into the database.
 */
export const seedRegions = async (dataSource: DataSource): Promise<void> => {
  const regionRepository = dataSource.getRepository(Region);

  // Check if regions already exist to avoid duplicate seeding
  const existingCount = await regionRepository.count();
  if (existingCount > 0) {
    console.log(
      `Regions already seeded (${existingCount} records found). Skipping...`,
    );
    return;
  }

  const regions: RegionData[] = [];

  // Read the CSV file
  const csvFilePath = path.resolve(
    process.cwd(),
    'src/seeds/maakunta_1_20250101.csv',
  );

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(
        csv({
          separator: ';',
          mapHeaders: ({ header }) => header.trim(),
          mapValues: ({ value }) => value.replace(/^'|'$/g, '').trim(), // Remove quotes and trim
        }),
      )
      .on('data', (data: any) => {
        regions.push({
          code: data.code,
          level: parseInt(data.level, 10),
          classificationName: data.classificationName,
        });
      })
      .on('end', async () => {
        try {
          // Insert regions in batches
          console.log(`Seeding ${regions.length} regions...`);

          // Create entities
          const regionEntities = regions.map((data) =>
            regionRepository.create(data),
          );

          // Save all at once
          await regionRepository.save(regionEntities);

          console.log('Region seeding completed successfully.');
          resolve();
        } catch (error) {
          console.error('Error seeding regions:', error);
          reject(error);
        }
      })
      .on('error', (error) => {
        console.error('Error reading region CSV:', error);
        reject(error);
      });
  });
};
