import { DataSource } from 'typeorm';
import * as fs from 'fs';
import * as path from 'path';
import csv from 'csv-parser';

interface OccupationData {
  conceptUri: string;
  preferredLabel: string;
  description: string;
  code: string;
  preferredLabelTranslations: Record<string, string>;
}

/**
 * Seed occupations from CSV file
 *
 * This seed file reads occupation data from the provided CSV file
 * and inserts it into the database using direct SQL queries.
 */
export const seedOccupations = async (
  dataSource: DataSource,
): Promise<void> => {
  console.log('Seeding occupations...');
  // Check if occupations already exist to avoid duplicate seeding using direct SQL
  const existingCountResult = await dataSource.query(
    'SELECT COUNT(*) FROM occupation',
  );
  const existingCount = parseInt(existingCountResult[0].count, 10);

  if (existingCount > 0) {
    console.log(
      `Occupations already seeded (${existingCount} records found). Skipping...`,
    );
    return;
  }

  const occupations: OccupationData[] = [];
  const finnishTranslations: Record<string, string> = {};

  // Read the CSV file
  const csvFilePath = path.resolve(
    process.cwd(),
    'src/seeds/occupations_en.csv',
  );
  const fiFilePath = path.resolve(
    process.cwd(),
    'src/seeds/occupations_fi.csv',
  );

  // First, read the Finnish translations and build a map by code
  await new Promise<void>((resolve, reject) => {
    fs.createReadStream(fiFilePath)
      .pipe(
        csv({
          mapValues: ({ value }) => value.trim(),
        }),
      )
      .on('data', (data: any) => {
        // Store Finnish translations with occupation code as key
        finnishTranslations[data.code] = data.preferredLabel;
      })
      .on('end', () => {
        console.log(
          `Loaded ${Object.keys(finnishTranslations).length} Finnish occupation translations`,
        );
        resolve();
      })
      .on('error', (error) => {
        console.error('Error reading Finnish occupation CSV:', error);
        reject(error);
      });
  });

  return new Promise((resolve, reject) => {
    fs.createReadStream(csvFilePath)
      .pipe(
        csv({
          // No separator needed as the default is comma
          mapValues: ({ value }) => value.trim(), // Trim whitespace
        }),
      )
      .on('data', (data: any) => {
        // Create translations object with Finnish translation if available
        const translations: Record<string, string> = {};
        if (finnishTranslations[data.code]) {
          translations.fi = finnishTranslations[data.code];
        }

        occupations.push({
          conceptUri: data.conceptUri,
          preferredLabel: data.preferredLabel,
          description: data.description,
          code: data.code,
          preferredLabelTranslations: translations,
        });
      })
      .on('end', async () => {
        try {
          console.log(`Seeding ${occupations.length} occupations...`);

          // Insert records in batches for better performance
          const BATCH_SIZE = 100;
          for (let i = 0; i < occupations.length; i += BATCH_SIZE) {
            const batch = occupations.slice(i, i + BATCH_SIZE);

            // Build multi-row insert query
            const valueStrings = batch.map(
              (data) => `(
                '${data.conceptUri.replace(/'/g, "''")}',
                '${data.preferredLabel.replace(/'/g, "''")}',
                '${data.description.replace(/'/g, "''")}',
                '${data.code.replace(/'/g, "''")}'::text,
                '${JSON.stringify(data.preferredLabelTranslations).replace(/'/g, "''")}'::jsonb
              )`,
            );

            const query = `
              INSERT INTO occupation (
                "conceptUri",
                "preferredLabel",
                "description",
                "code",
                "preferredLabelTranslations"
              )
              VALUES ${valueStrings.join(',')}
            `;

            await dataSource.query(query);

            console.log(
              `Processed batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(
                occupations.length / BATCH_SIZE,
              )}`,
            );
          }

          console.log('Occupation seeding completed successfully.');
          resolve();
        } catch (error) {
          console.error('Error seeding occupations:', error);
          reject(error);
        }
      })
      .on('error', (error) => {
        console.error('Error reading occupation CSV:', error);
        reject(error);
      });
  });
};
