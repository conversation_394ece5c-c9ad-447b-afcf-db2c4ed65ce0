import { DataSource } from 'typeorm';

/**
 * Seed Industries
 * 
 * This seed file populates the industry table with predefined industry data
 * using direct SQL queries for better performance.
 */
export const seedIndustries = async (
  dataSource: DataSource,
): Promise<void> => {
  // Check if industries already exist to avoid duplicate seeding
  const existingCountResult = await dataSource.query(
    'SELECT COUNT(*) FROM industry'
  );
  const existingCount = parseInt(existingCountResult[0].count, 10);
  
  if (existingCount > 0) {
    console.log(
      `Industries already seeded (${existingCount} records found). Skipping...`,
    );
    return;
  }

  // Industry data
  const industries = [
    {
      code: 'TECH',
      name: 'Technology',
      translations: {
        ru: 'Технологии',
        sv: 'Teknologi',
        fi: 'Teknologia',
      },
    },
    {
      code: 'SOFTDEV',
      name: 'Software Development',
      translations: {
        ru: 'Разработка программного обеспечения',
        sv: 'Programvaruutveckling',
        fi: 'Ohjelmistoke<PERSON><PERSON>',
      },
    },
    {
      code: 'IT',
      name: 'Information Technology (IT)',
      translations: {
        ru: 'Информационные технологии',
        sv: 'Informationsteknologi',
        fi: 'Tietotekniikka',
      },
    },
    {
      code: 'DATASCI',
      name: 'Data Science',
      translations: {
        ru: 'Наука о данных',
        sv: 'Data Science',
        fi: 'Datatiede',
      },
    },
    {
      code: 'AI',
      name: 'Artificial Intelligence (AI)',
      translations: {
        ru: 'Искусственный интеллект',
        sv: 'Artificiell intelligens',
        fi: 'Tekoäly',
      },
    },
    {
      code: 'ML',
      name: 'Machine Learning (ML)',
      translations: {
        ru: 'Машинное обучение',
        sv: 'Maskininlärning',
        fi: 'Koneoppiminen',
      },
    },
    {
      code: 'CYBERSEC',
      name: 'Cybersecurity',
      translations: {
        ru: 'Кибербезопасность',
        sv: 'Cybersäkerhet',
        fi: 'Kyberturvallisuus',
      },
    },
    {
      code: 'CLOUD',
      name: 'Cloud Computing',
      translations: {
        ru: 'Облачные вычисления',
        sv: 'Molntjänster',
        fi: 'Pilvilaskenta',
      },
    },
    {
      code: 'FINTECH',
      name: 'FinTech (Financial Technology)',
      translations: {
        ru: 'Финансовые технологии',
        sv: 'Finansiell teknik',
        fi: 'Rahoitusteknologia',
      },
    },
    {
      code: 'ECOMM',
      name: 'E-commerce',
      translations: {
        ru: 'Электронная коммерция',
        sv: 'E-handel',
        fi: 'Verkkokauppa',
      },
    },
    {
      code: 'HEALTH',
      name: 'Healthcare',
      translations: {
        ru: 'Здравоохранение',
        sv: 'Hälsovård',
        fi: 'Terveydenhuolto',
      },
    },
    {
      code: 'PHARMA',
      name: 'Pharmaceuticals',
      translations: { ru: 'Фармацевтика', sv: 'Läkemedel', fi: 'Lääketiede' },
    },
    {
      code: 'BIOTECH',
      name: 'Biotechnology',
      translations: {
        ru: 'Биотехнологии',
        sv: 'Bioteknik',
        fi: 'Bioteknologia',
      },
    },
    {
      code: 'MEDDEV',
      name: 'Medical Devices',
      translations: {
        ru: 'Медицинские устройства',
        sv: 'Medicintekniska produkter',
        fi: 'Lääketieteelliset laitteet',
      },
    },
    {
      code: 'EDU',
      name: 'Education',
      translations: { ru: 'Образование', sv: 'Utbildning', fi: 'Koulutus' },
    },
    {
      code: 'HIGHER_EDU',
      name: 'Higher Education',
      translations: {
        ru: 'Высшее образование',
        sv: 'Högre utbildning',
        fi: 'Korkeakoulutus',
      },
    },
    {
      code: 'FOREST',
      name: 'Forest Industry',
      translations: {
        ru: 'Лесная промышленность',
        sv: 'Skogsindustri',
        fi: 'Metsäteollisuus',
      },
    },
    {
      code: 'EDTECH',
      name: 'EdTech (Educational Technology)',
      translations: {
        ru: 'Образовательные технологии',
        sv: 'Utbildningsteknik',
        fi: 'Koulutusteknologia',
      },
    },
    {
      code: 'ENG',
      name: 'Engineering',
      translations: {
        ru: 'Инженерия',
        sv: 'Ingenjörskonst',
        fi: 'Tekniikka',
      },
    },
    {
      code: 'CIVIL_ENG',
      name: 'Civil Engineering',
      translations: {
        ru: 'Гражданское строительство',
        sv: 'Byggnadsteknik',
        fi: 'Rakennustekniikka',
      },
    },
    {
      code: 'MECH_ENG',
      name: 'Mechanical Engineering',
      translations: {
        ru: 'Механическая инженерия',
        sv: 'Maskinteknik',
        fi: 'Konetekniikka',
      },
    },
    {
      code: 'ELEC_ENG',
      name: 'Electrical Engineering',
      translations: {
        ru: 'Электротехника',
        sv: 'Elektroteknik',
        fi: 'Sähkötekniikka',
      },
    },
    {
      code: 'AERO_ENG',
      name: 'Aerospace Engineering',
      translations: {
        ru: 'Аэрокосмическая инженерия',
        sv: 'Rymdteknik',
        fi: 'Ilmailu- ja avaruustekniikka',
      },
    },
    {
      code: 'MFG',
      name: 'Manufacturing',
      translations: {
        ru: 'Производство',
        sv: 'Tillverkning',
        fi: 'Valmistus',
      },
    },
    {
      code: 'AUTO',
      name: 'Automotive',
      translations: {
        ru: 'Автомобилестроение',
        sv: 'Bilindustri',
        fi: 'Autoteollisuus',
      },
    },
    {
      code: 'AERO',
      name: 'Aerospace',
      translations: {
        ru: 'Аэрокосмическая отрасль',
        sv: 'Rymdindustri',
        fi: 'Ilmailu- ja avaruusteollisuus',
      },
    },
    {
      code: 'CONSUMER',
      name: 'Consumer Goods',
      translations: {
        ru: 'Потребительские товары',
        sv: 'Konsumentvaror',
        fi: 'Kulutustavarat',
      },
    },
    {
      code: 'RETAIL',
      name: 'Retail',
      translations: {
        ru: 'Розничная торговля',
        sv: 'Detaljhandel',
        fi: 'Vähittäiskauppa',
      },
    },
    {
      code: 'FOOD_BEV',
      name: 'Food & Beverage',
      translations: {
        ru: 'Пищевая промышленность',
        sv: 'Livsmedel och dryck',
        fi: 'Elintarvike- ja juomateollisuus',
      },
    },
    {
      code: 'AGRI',
      name: 'Agriculture',
      translations: {
        ru: 'Сельское хозяйство',
        sv: 'Jordbruk',
        fi: 'Maatalous',
      },
    },
    {
      code: 'ENERGY',
      name: 'Energy',
      translations: { ru: 'Энергетика', sv: 'Energi', fi: 'Energia' },
    },
    {
      code: 'RENEWABLE',
      name: 'Renewable Energy',
      translations: {
        ru: 'Возобновляемая энергия',
        sv: 'Förnybar energi',
        fi: 'Uusiutuva energia',
      },
    },
    {
      code: 'OIL_GAS',
      name: 'Oil & Gas',
      translations: {
        ru: 'Нефтегазовая промышленность',
        sv: 'Olja och gas',
        fi: 'Öljy- ja kaasuteollisuus',
      },
    },
    {
      code: 'MINING',
      name: 'Mining',
      translations: {
        ru: 'Горнодобывающая промышленность',
        sv: 'Gruvdrift',
        fi: 'Kaivosteollisuus',
      },
    },
    {
      code: 'CONSTR',
      name: 'Construction',
      translations: {
        ru: 'Строительство',
        sv: 'Byggnation',
        fi: 'Rakentaminen',
      },
    },
    {
      code: 'REAL_EST',
      name: 'Real Estate',
      translations: {
        ru: 'Недвижимость',
        sv: 'Fastigheter',
        fi: 'Kiinteistöt',
      },
    },
    {
      code: 'ARCH',
      name: 'Architecture',
      translations: {
        ru: 'Архитектура',
        sv: 'Arkitektur',
        fi: 'Arkkitehtuuri',
      },
    },
    {
      code: 'GOV',
      name: 'Government',
      translations: { ru: 'Правительство', sv: 'Regering', fi: 'Hallitus' },
    },
    {
      code: 'PUB_ADMIN',
      name: 'Public Administration',
      translations: {
        ru: 'Государственное управление',
        sv: 'Offentlig förvaltning',
        fi: 'Julkishallinto',
      },
    },
    {
      code: 'DEFENSE',
      name: 'Defense',
      translations: { ru: 'Оборона', sv: 'Försvar', fi: 'Puolustus' },
    },
    {
      code: 'LEGAL',
      name: 'Legal',
      translations: { ru: 'Юриспруденция', sv: 'Juridik', fi: 'Oikeustiede' },
    },
    {
      code: 'LAW_ENF',
      name: 'Law Enforcement',
      translations: {
        ru: 'Правоохранительные органы',
        sv: 'Brottsbekämpning',
        fi: 'Lainvalvonta',
      },
    },
    {
      code: 'FIN',
      name: 'Finance',
      translations: { ru: 'Финансы', sv: 'Finans', fi: 'Rahoitus' },
    },
    {
      code: 'BANKING',
      name: 'Banking',
      translations: {
        ru: 'Банковское дело',
        sv: 'Bankväsende',
        fi: 'Pankkitoiminta',
      },
    },
    {
      code: 'INSUR',
      name: 'Insurance',
      translations: { ru: 'Страхование', sv: 'Försäkring', fi: 'Vakuutus' },
    },
    {
      code: 'INV_BANK',
      name: 'Investment Banking',
      translations: {
        ru: 'Инвестиционное банкинг',
        sv: 'Investeringsbank',
        fi: 'Sijoituspankkitoiminta',
      },
    },
    {
      code: 'ASSET_MGT',
      name: 'Asset Management',
      translations: {
        ru: 'Управление активами',
        sv: 'Tillgångsförvaltning',
        fi: 'Varainhoito',
      },
    },
    {
      code: 'MKTG',
      name: 'Marketing',
      translations: {
        ru: 'Маркетинг',
        sv: 'Marknadsföring',
        fi: 'Markkinointi',
      },
    },
    {
      code: 'ADV',
      name: 'Advertising',
      translations: { ru: 'Реклама', sv: 'Reklam', fi: 'Mainonta' },
    },
    {
      code: 'PR',
      name: 'Public Relations',
      translations: {
        ru: 'Связи с общественностью',
        sv: 'PR',
        fi: 'Suhdetoiminta',
      },
    },
    {
      code: 'MEDIA',
      name: 'Media',
      translations: { ru: 'Медиа', sv: 'Media', fi: 'Media' },
    },
    {
      code: 'ENT',
      name: 'Entertainment',
      translations: { ru: 'Развлечения', sv: 'Underhållning', fi: 'Viihde' },
    },
    {
      code: 'SPORTS',
      name: 'Sports',
      translations: { ru: 'Спорт', sv: 'Sport', fi: 'Urheilu' },
    },
    {
      code: 'HOSP',
      name: 'Hospitality',
      translations: {
        ru: 'Гостеприимство',
        sv: 'Gästfrihet',
        fi: 'Hotelli- ja ravintola-ala',
      },
    },
    {
      code: 'TOURISM',
      name: 'Tourism',
      translations: { ru: 'Туризм', sv: 'Turism', fi: 'Matkailu' },
    },
    {
      code: 'TRANS',
      name: 'Transportation',
      translations: { ru: 'Транспорт', sv: 'Transport', fi: 'Kuljetus' },
    },
    {
      code: 'LOGIST',
      name: 'Logistics',
      translations: { ru: 'Логистика', sv: 'Logistik', fi: 'Logistiikka' },
    },
    {
      code: 'SUPPLY',
      name: 'Supply Chain',
      translations: {
        ru: 'Цепочка поставок',
        sv: 'Försörjningskedja',
        fi: 'Toimitusketju',
      },
    },
    {
      code: 'TELECOM',
      name: 'Telecommunications',
      translations: {
        ru: 'Телекоммуникации',
        sv: 'Telekommunikation',
        fi: 'Telekommunikaatio',
      },
    },
    {
      code: 'CUST_SVC',
      name: 'Customer Service',
      translations: {
        ru: 'Клиентский сервис',
        sv: 'Kundservice',
        fi: 'Asiakaspalvelu',
      },
    },
    {
      code: 'HR',
      name: 'Human Resources (HR)',
      translations: {
        ru: 'Кадровые ресурсы',
        sv: 'Personalresurser',
        fi: 'Henkilöstöhallinto',
      },
    },
    {
      code: 'CONSULT',
      name: 'Consulting',
      translations: {
        ru: 'Консалтинг',
        sv: 'Konsultverksamhet',
        fi: 'Konsultointi',
      },
    },
    {
      code: 'MGT_CONS',
      name: 'Management Consulting',
      translations: {
        ru: 'Управленческий консалтинг',
        sv: 'Managementkonsult',
        fi: 'Johtamiskonsultointi',
      },
    },
    {
      code: 'RND',
      name: 'Research & Development (R&D)',
      translations: {
        ru: 'Научные исследования и разработки',
        sv: 'Forskning och utveckling',
        fi: 'Tutkimus ja kehitys',
      },
    },
    {
      code: 'ENV_SVC',
      name: 'Environmental Services',
      translations: {
        ru: 'Экологические услуги',
        sv: 'Miljötjänster',
        fi: 'Ympäristöpalvelut',
      },
    },
    {
      code: 'NONPROFIT',
      name: 'Nonprofit',
      translations: {
        ru: 'Некомерческие организации',
        sv: 'Ideella organisationer',
        fi: 'Voittoa tavoittelemattomat järjestöt',
      },
    },
    {
      code: 'SOC_SVC',
      name: 'Social Services',
      translations: {
        ru: 'Социальные услуги',
        sv: 'Socialtjänster',
        fi: 'Sosiaalipalvelut',
      },
    },
    {
      code: 'ARTS',
      name: 'Arts & Culture',
      translations: {
        ru: 'Искусство и культура',
        sv: 'Konst och kultur',
        fi: 'Taide ja kulttuuri',
      },
    },
    {
      code: 'PUBLISH',
      name: 'Publishing',
      translations: {
        ru: 'Издательское дело',
        sv: 'Förlagsverksamhet',
        fi: 'Kustannustoiminta',
      },
    },
    {
      code: 'FASHION',
      name: 'Fashion',
      translations: { ru: 'Мода', sv: 'Mode', fi: 'Muoti' },
    },
    {
      code: 'INT_DES',
      name: 'Interior Design',
      translations: {
        ru: 'Дизайн интерьера',
        sv: 'Inredningsdesign',
        fi: 'Sisustussuunnittelu',
      },
    },
    {
      code: 'GAMING',
      name: 'Gaming',
      translations: { ru: 'Игры', sv: 'Spel', fi: 'Pelaaminen' },
    },
    {
      code: 'ANIM',
      name: 'Animation',
      translations: { ru: 'Анимация', sv: 'Animation', fi: 'Animaatio' },
    },
    {
      code: 'DATA_ANAL',
      name: 'Data Analytics',
      translations: {
        ru: 'Аналитика данных',
        sv: 'Dataanalys',
        fi: 'Data-analytiikka',
      },
    },
    {
      code: 'VC_FUND',
      name: 'Venture Capital Funding',
      translations: {
        ru: 'Венчурное финансирование',
        sv: 'Riskkapitalfinansiering',
        fi: 'Pääomasijoitusrahoitus',
      },
    },
    {
      code: 'ANGEL_INV',
      name: 'Angel Investment',
      translations: {
        ru: 'Ангельские инвестиции',
        sv: 'Affärsängel-investeringar',
        fi: 'Enkelisijoitukset',
      },
    },
    {
      code: 'IoT',
      name: 'Internet of Things (IoT)',
      translations: {
        ru: 'Интернет вещей',
        sv: 'Sakernas internet',
        fi: 'Esineiden internet',
      },
    },
    {
      code: 'AR_VR',
      name: 'Augmented/Virtual Reality',
      translations: {
        ru: 'Дополненная/виртуальная реальность',
        sv: 'Förstärkt/virtuell verklighet',
        fi: 'Lisätty/virtuaalitodellisuus',
      },
    },
    {
      code: 'ROBOTICS',
      name: 'Robotics',
      translations: { ru: 'Робототехника', sv: 'Robotteknik', fi: 'Robotiikka' },
    },
    {
      code: 'SaaS',
      name: 'Software as a Service (SaaS)',
      translations: {
        ru: 'Программное обеспечение как услуга',
        sv: 'Programvara som tjänst',
        fi: 'Ohjelmisto palveluna',
      },
    },
    {
      code: 'IaaS',
      name: 'Infrastructure as a Service (IaaS)',
      translations: {
        ru: 'Инфраструктура как услуга',
        sv: 'Infrastruktur som tjänst',
        fi: 'Infrastruktuuri palveluna',
      },
    },
    {
      code: 'PaaS',
      name: 'Platform as a Service (PaaS)',
      translations: {
        ru: 'Платформа как услуга',
        sv: 'Plattform som tjänst',
        fi: 'Alusta palveluna',
      },
    },
    {
      code: 'DaaS',
      name: 'Data as a Service (DaaS)',
      translations: {
        ru: 'Данные как услуга',
        sv: 'Data som tjänst',
        fi: 'Data palveluna',
      },
    },
    {
      code: 'XaaS',
      name: 'Everything as a Service (XaaS)',
      translations: {
        ru: 'Все как услуга',
        sv: 'Allt som tjänst',
        fi: 'Kaikki palveluna',
      },
    },
    {
      code: 'CRM',
      name: 'Customer Relationship Management (CRM)',
      translations: {
        ru: 'Управление взаимоотношениями с клиентами',
        sv: 'Kundrelationshantering',
        fi: 'Asiakkuudenhallinta',
      },
    },
    {
      code: 'ERP',
      name: 'Enterprise Resource Planning (ERP)',
      translations: {
        ru: 'Планирование ресурсов предприятия',
        sv: 'Affärssystem',
        fi: 'Toiminnanohjausjärjestelmä',
      },
    },
    {
      code: 'TRAINING',
      name: 'Training & Development',
      translations: {
        ru: 'Обучение и развитие',
        sv: 'Utbildning och utveckling',
        fi: 'Koulutus ja kehitys',
      },
    },
    {
      code: 'SEC_SVC',
      name: 'Security Services',
      translations: {
        ru: 'Охранные услуги',
        sv: 'Säkerhetstjänster',
        fi: 'Turvapalvelut',
      },
    },
    {
      code: 'WASTE_MGT',
      name: 'Waste Management',
      translations: {
        ru: 'Управление отходами',
        sv: 'Avfallshantering',
        fi: 'Jätehuolto',
      },
    },
    {
      code: 'VC',
      name: 'Venture Capital',
      translations: {
        ru: 'Венчурный капитал',
        sv: 'Riskkapital',
        fi: 'Pääomasijoitus',
      },
    },
    {
      code: 'PE',
      name: 'Private Equity',
      translations: {
        ru: 'Частный капитал',
        sv: 'Privat kapital',
        fi: 'Yksityinen pääoma',
      },
    },
    {
      code: 'CRYPTO',
      name: 'Cryptocurrency',
      translations: {
        ru: 'Криптовалюта',
        sv: 'Kryptoteknik',
        fi: 'Kryptovaluutta',
      },
    },
    {
      code: 'BLOCKCHAIN',
      name: 'Blockchain',
      translations: { ru: 'Блокчейн', sv: 'Blockkedja', fi: 'Blockchain' },
    },
    {
      code: 'REMOTE',
      name: 'Remote Work',
      translations: {
        ru: 'Удаленная работа',
        sv: 'Distansarbete',
        fi: 'Etätyö',
      },
    },
    {
      code: 'FREELANCE',
      name: 'Freelance/Gig Economy',
      translations: {
        ru: 'Фриланс/гиг-экономика',
        sv: 'Frilans/gig-ekonomi',
        fi: 'Freelance/gig-talous',
      },
    },
    {
      code: 'TEXTILE',
      name: 'Textile and Leather',
      translations: {
        ru: 'Текстиль и кожа',
        sv: 'Textil och läder',
        fi: 'Tekstiili ja nahka',
      },
    },
    {
      code: 'SHIPBUILD',
      name: 'Shipbuilding',
      translations: {
        ru: 'Судостроение',
        sv: 'Skeppsbyggnad',
        fi: 'Laivanrakennus',
      },
    },
  ];

  try {
    console.log(`Seeding ${industries.length} industries...`);
    
    // Insert records in batches for better performance
    const BATCH_SIZE = 20;
    for (let i = 0; i < industries.length; i += BATCH_SIZE) {
      const batch = industries.slice(i, i + BATCH_SIZE);
      
      // Build multi-row insert query
      const valueStrings = batch.map(
        (data) => `(
          '${data.code.replace(/'/g, "''")}',
          '${data.name.replace(/'/g, "''")}',
          '${JSON.stringify(data.translations).replace(/'/g, "''")}'
        )`
      );
      
      const query = `
        INSERT INTO industry (
          "code",
          "name",
          "translations"
        )
        VALUES ${valueStrings.join(',')}
      `;
      
      await dataSource.query(query);
      
      console.log(
        `Processed batch ${Math.floor(i / BATCH_SIZE) + 1}/${Math.ceil(
          industries.length / BATCH_SIZE,
        )}`
      );
    }
    
    console.log('Industry seeding completed successfully.');
  } catch (error) {
    console.error('Error seeding industries:', error);
    throw error;
  }
};
