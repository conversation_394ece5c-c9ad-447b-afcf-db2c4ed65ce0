import { DataSource } from 'typeorm';
import { seedMunicipalities } from './municipality.seed';
import { seedRegions } from './region.seed';
import { seedOccupations } from './occupation.seed';
import { seedIndustries } from './industry.seed';

/**
 * Run all seed operations
 * 
 * This function runs all seed operations in the correct order.
 * Add new seed functions here as they are created.
 */
export const runSeeds = async (dataSource: DataSource): Promise<void> => {
  try {
    console.log('Starting seed operations...');
    
    // Run seeds in order (add more as needed)
    await seedRegions(dataSource);
    await seedMunicipalities(dataSource);
    await seedOccupations(dataSource);
    await seedIndustries(dataSource);
    
    console.log('All seed operations completed successfully.');
  } catch (error) {
    console.error('Error during seed operations:', error);
    throw error;
  }
};
