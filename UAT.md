### **Job Service v1 Features (Acceptance Criteria)**
**Ordered by Implementation Priority**

---

#### **1. Job Import Pipeline**
- **1.1**: BullMQ setup for job ingestion.
- **1.2**: Fetch jobs from Job Board API (mock data initially).
- **1.3**: Store raw jobs in BigQuery (`raw_jobs` table).

---

#### **2. Job Processing**
- **2.1**: Translate job descriptions to English (Google NMT).
- **2.2**: Classify jobs using NACE/ESCO taxonomies.
- **2.3**: Save processed jobs to Postgres (`processed_jobs` table).

---

#### **3. Recommendation Engine**
- **3.1**: Precompute TF-IDF vectors for job titles/descriptions.
- **3.2**: Match user skills to jobs using cosine similarity.
- **3.3**: Return top 10 job recommendations.

---

#### **4. Caching Layer**
- **4.1**: <PERSON><PERSON> translated job descriptions in Redis (24h TTL).
- **4.2**: Cache user-specific recommendations (e.g., `user:123:JOBS`).

---

#### **5. API Endpoints**
- **5.1**: `POST /jobs/import` (trigger job ingestion).
- **5.2**: `POST /jobs/recommend` (get job recommendations).
- **5.3**: `GET /jobs/:id` (fetch job details).

---

#### **6. Monitoring & Logging**
- **6.1**: Log all BullMQ job statuses (success/failure).
- **6.2**: Track API latency/errors (e.g., `/recommend`).
- **6.3**: Set up basic health checks (`/health` endpoint).

---

#### **7. Documentation**
- **7.1**: Swagger docs for all API endpoints.
- **7.2**: ADR for architectural decisions (e.g., Prisma over Knex).

---

### **UAT Acceptance Criteria**
1. **Job Import**: Verify raw jobs are stored in BigQuery.
2. **Job Processing**: Confirm jobs are classified and stored in Postgres.
3. **Recommendations**: Validate job recommendations match user skills.
4. **Caching**: Ensure translations/recommendations are cached in Redis.
5. **API**: Test all endpoints for correctness and performance.
6. **Logging**: Confirm errors/latency are logged for all critical paths.

---

This list ensures incremental delivery and clear UAT milestones. 🚀